import{_ as P,r as i,m as N,K as T,o as O,c as G,a as e,h as c,v as D,i as m,j as H,p as K,e as R}from"./Dy7juzJL.js";const l=b=>(K("data-v-1b4af074"),b=b(),R(),b),W={class:"scanner-demo"},q={class:"bg-white shadow-lg rounded-lg overflow-hidden p-6"},X=l(()=>e("h2",{class:"text-2xl font-bold mb-4 text-blue-600 border-b pb-2"}," 图像扫描管理系统 ",-1)),Y={class:"mb-8"},J=l(()=>e("h3",{class:"text-xl font-semibold mb-3 text-blue-600"},"扫描设置",-1)),z={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Q=l(()=>e("div",{class:"form-control"},[e("label",{class:"font-medium mb-1 block"},"扫描设备"),e("select",{id:"devices",class:"w-full p-2 border rounded"})],-1)),Z={class:"form-control"},$=l(()=>e("label",{class:"font-medium mb-1 block"},"分辨率",-1)),ee={class:"flex items-center"},te=l(()=>e("span",{class:"mx-2"},"X",-1)),oe={class:"form-control"},ne=l(()=>e("label",{class:"font-medium mb-1 block"},"色彩模式",-1)),se=l(()=>e("option",{value:"RGB"},"彩色",-1)),le=l(()=>e("option",{value:"GRAY"},"灰色",-1)),ae=l(()=>e("option",{value:"BW"},"黑白",-1)),de=[se,le,ae],ie={class:"form-control"},ce=l(()=>e("label",{class:"font-medium mb-1 block"},"显示设备对话框",-1)),re=l(()=>e("option",{value:"true"},"显示",-1)),ue=l(()=>e("option",{value:"false"},"不显示",-1)),me=[re,ue],pe={class:"form-control"},be=l(()=>e("label",{class:"font-medium mb-1 block"},"自动进纸模式",-1)),ge=l(()=>e("option",{value:"true"},"是",-1)),ve=l(()=>e("option",{value:"false"},"否",-1)),fe=[ge,ve],he={class:"form-control"},_e=l(()=>e("label",{class:"font-medium mb-1 block"},"自动装填纸张",-1)),xe=l(()=>e("option",{value:"true"},"是",-1)),we=l(()=>e("option",{value:"false"},"否",-1)),ye=[xe,we],Ie={class:"form-control"},ke=l(()=>e("label",{class:"font-medium mb-1 block"},"双面模式",-1)),De=l(()=>e("option",{value:"true"},"是",-1)),Ee=l(()=>e("option",{value:"false"},"否",-1)),Se=[De,Ee],Le={class:"form-control"},Ce=l(()=>e("label",{class:"font-medium mb-1 block"},"自动纠偏",-1)),Be=l(()=>e("option",{value:"true"},"是",-1)),Ue=l(()=>e("option",{value:"false"},"否",-1)),Ae=[Be,Ue],Me={class:"form-control"},Fe=l(()=>e("label",{class:"font-medium mb-1 block"},"自动边框检测",-1)),Ve=l(()=>e("option",{value:"true"},"是",-1)),je=l(()=>e("option",{value:"false"},"否",-1)),Pe=[Ve,je],Ne={class:"mb-8"},Te=l(()=>e("h3",{class:"text-xl font-semibold mb-3 text-blue-600"},"上传设置",-1)),Oe={class:"bg-gray-100 p-4 rounded"},Ge={class:"mb-4"},He=l(()=>e("label",{class:"font-medium mb-1 block"},"上传地址",-1)),Ke=H('<div data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>扫描结果</h3><div id="imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-1b4af074></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-1b4af074><p class="font-bold" data-v-1b4af074> 注意：本演示需要下载安装托盘扫描服务才可正常工作，下载链接在下面。 </p></div><div class="mt-6 flex justify-center gap-4 border-t pt-4" data-v-1b4af074><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 扫描服务托盘程序下载 </a><a href="https://www.brainysoft.cn/video/scanh5.mp4" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 视频教程 </a><a href="https://www.brainysoft.cn" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 官方网站 </a></div>',3),Re={__name:"ScannerDemo",setup(b){const v=i("300"),f=i("300"),h=i("RGB"),_=i("false"),x=i("false"),w=i("false"),y=i("false"),I=i("false"),k=i("false"),g=i("http://localhost:44300/api/ImageUpload/upload");let s=null;const S=()=>{s?s.loadDevices():alert("扫描控件未初始化，请先加载扫描控件！")},L=()=>{if(!s){alert("扫描控件未初始化，请先加载扫描控件！");return}const o=document.getElementById("devices");if(o.selectedIndex===-1){alert("请先刷新或者选中要使用的扫描设备后再开始扫描!");return}s.scaner_work_config.dpi_x=v.value,s.scaner_work_config.dpi_y=f.value,s.scaner_work_config.deviceIndex=o.selectedIndex,s.scaner_work_config.colorMode=h.value,s.scaner_work_config.showDialog=_.value,s.scaner_work_config.autoFeedEnable=x.value,s.scaner_work_config.autoFeed=w.value,s.scaner_work_config.dupxMode=y.value,s.scaner_work_config.autoDeskew=I.value,s.scaner_work_config.autoBorderDetection=k.value,s.startScan()},C=()=>{s&&(s.clearAll(),document.getElementById("imageList").innerHTML="")},B=()=>{s&&s.getAllImage()},U=()=>{s&&s.setFocus()},A=()=>{s&&s.hidden()},E=()=>{if(!s)return;const o=g.value||"http://localhost:44300/api/ImageUpload/upload",n=new Date().getTime().toString(),t="PDF扫描文档_"+new Date().toLocaleDateString();console.log("开始上传PDF，ID:"+n+"，描述:"+t),s.uploadAllImageAsPdfToUrl(o,n,t)},M=()=>{if(!s)return;const o=document.getElementById("imageList"),n=g.value||"http://localhost:44300/api/ImageUpload/upload",t=o.querySelectorAll("img");if(t.length===0){alert("没有可上传的图像！");return}Array.from(t).forEach((d,a)=>{const p=d.src.split(",")[1],r=new FormData;r.append("image",p),r.append("imageIndex",a),r.append("name","test"),fetch(n,{method:"POST",body:r}).then(u=>{console.log(u)}).catch(u=>{console.error(u)})}),alert("上传请求已发送！")},F=()=>{if(!s)return;const o=prompt("请输入保存文件路径","d:/test.pdf");o&&(s.saveAllImageToLocal(o),alert("文件已保存到: "+o))},V=()=>{if(typeof ScanOnWeb>"u"){console.error("ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件");return}s=new ScanOnWeb,s.onGetDevicesListEvent=o=>{const n=document.getElementById("devices");n.innerHTML="";for(let r=0;r<o.devices.length;++r){const u=document.createElement("option");u.innerHTML=o.devices[r],r===o.currentIndex&&(u.selected=!0),n.appendChild(u)}s.setLicenseKey(4,"","","http://127.0.0.1:28110/check")},s.onGetImageByIdEvent=o=>{console.log("获取图像事件回调,图像id:"+o.imageIndex+" 图像总数:"+o.imageCount+" 注意试用版随机删除图像会造成总数降低或不变");const n=document.getElementById("imageList"),t=document.createElement("div");t.className="image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";const d=document.createElement("img");d.src="data:image/jpg;base64,"+o.imageBase64,d.className="w-full h-64 object-contain",d.setAttribute("imageIndex",o.imageIndex);const a=document.createElement("div");a.className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm",a.textContent="图像 #"+(o.imageIndex+1),t.appendChild(d),t.appendChild(a),n.appendChild(t)},s.onImageEditedEvent=o=>{console.log("图像编辑事件回调,图像id:"+o.imageIndex);const t=document.getElementById("imageList").querySelectorAll("img");for(let d=0;d<t.length;d++){const a=t[d];if(parseInt(a.getAttribute("imageIndex"))===o.imageIndex){a.src="data:image/jpg;base64,"+o.imageBase64,a.style.display="none",a.offsetHeight,a.style.display="block";break}}},s.onImageDrapEvent=o=>{console.log("图像顺序调整事件回调,调整前:"+o.beforeIndex+" 调整后:"+o.afterIndex);const n=document.getElementById("imageList"),t=Array.from(n.children),d=t[o.beforeIndex];t.splice(o.beforeIndex,1),t.splice(o.afterIndex,0,d),t.forEach(a=>n.appendChild(a))},s.onScanFinishedEvent=o=>{console.log("扫描完成事件回调,扫描前:"+o.imageBeforeCount+" 扫描后:"+o.imageAfterCount)},s.onGetAllImageEvent=o=>{console.log("图像总数:"+o.imageCount),console.log("当前选中编辑的图像id:"+o.currentSelected);const n=document.getElementById("imageList");if(n.innerHTML="",o.images.length===0){console.log("没有图像可以显示");return}for(let t=0;t<o.images.length;t++){const d=document.createElement("div");d.className="image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";const a=document.createElement("img");a.src="data:image/jpg;base64,"+o.images[t],a.className="w-full h-64 object-contain",a.setAttribute("imageIndex",t);const p=document.createElement("div");p.className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm",p.textContent="图像 #"+(t+1),d.appendChild(a),d.appendChild(p),n.appendChild(d)}},s.onUploadAllImageAsPdfToUrlEvent=o=>{console.log(o);try{const n=JSON.parse(o.uploadResult);n&&n.network===!0?alert("PDF上传成功！"):alert("PDF上传失败！错误信息: "+(n.msg||"未知错误"))}catch(n){console.error("解析上传结果失败:",n),alert("PDF上传处理失败！请检查网络连接和服务器状态。")}},s.onUploadEvent=o=>{console.log("用户点击了开始上传按钮,当前图像总数:"+o.imageCount),E()}},j=()=>new Promise((o,n)=>{if(document.getElementById("scanonweb-script")){o();return}const t=document.createElement("script");t.id="scanonweb-script",t.src="/scanonweb.js",t.type="text/javascript",t.async=!0,t.onload=o,t.onerror=n,document.head.appendChild(t)});return N(async()=>{try{await j(),V()}catch(o){console.error("加载扫描控件失败:",o),alert("加载扫描控件失败，请确保服务器上存在scanonweb.js文件。")}}),T(()=>{s=null}),(o,n)=>(O(),G("div",W,[e("div",q,[X,e("div",Y,[J,e("div",z,[Q,e("div",Z,[$,e("div",ee,[c(e("input",{type:"text",id:"dpi_x","onUpdate:modelValue":n[0]||(n[0]=t=>v.value=t),class:"w-16 p-2 border rounded"},null,512),[[D,v.value]]),te,c(e("input",{type:"text",id:"dpi_y","onUpdate:modelValue":n[1]||(n[1]=t=>f.value=t),class:"w-16 p-2 border rounded"},null,512),[[D,f.value]])])]),e("div",oe,[ne,c(e("select",{id:"colorMode","onUpdate:modelValue":n[2]||(n[2]=t=>h.value=t),class:"w-full p-2 border rounded"},de,512),[[m,h.value]])]),e("div",ie,[ce,c(e("select",{id:"showDialog","onUpdate:modelValue":n[3]||(n[3]=t=>_.value=t),class:"w-full p-2 border rounded"},me,512),[[m,_.value]])]),e("div",pe,[be,c(e("select",{id:"feedEnable","onUpdate:modelValue":n[4]||(n[4]=t=>x.value=t),class:"w-full p-2 border rounded"},fe,512),[[m,x.value]])]),e("div",he,[_e,c(e("select",{id:"autoFeed","onUpdate:modelValue":n[5]||(n[5]=t=>w.value=t),class:"w-full p-2 border rounded"},ye,512),[[m,w.value]])]),e("div",Ie,[ke,c(e("select",{id:"dupxMode","onUpdate:modelValue":n[6]||(n[6]=t=>y.value=t),class:"w-full p-2 border rounded"},Se,512),[[m,y.value]])]),e("div",Le,[Ce,c(e("select",{id:"autoDeskew","onUpdate:modelValue":n[7]||(n[7]=t=>I.value=t),class:"w-full p-2 border rounded"},Ae,512),[[m,I.value]])]),e("div",Me,[Fe,c(e("select",{id:"autoBorderDetection","onUpdate:modelValue":n[8]||(n[8]=t=>k.value=t),class:"w-full p-2 border rounded"},Pe,512),[[m,k.value]])])]),e("div",{class:"flex flex-wrap gap-2 mt-6"},[e("button",{class:"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",onClick:S}," 获取设备列表 "),e("button",{class:"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",onClick:L}," 开始扫描 "),e("button",{class:"btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors",onClick:C}," 清空扫描结果 "),e("button",{class:"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",onClick:B}," 获取所有图像 "),e("button",{class:"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors",onClick:U}," 显示界面 "),e("button",{class:"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors",onClick:A}," 隐藏界面 "),e("button",{class:"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors",onClick:F}," 本地另存 ")])]),e("div",Ne,[Te,e("div",Oe,[e("div",Ge,[He,c(e("input",{type:"text",id:"uploadUrl","onUpdate:modelValue":n[9]||(n[9]=t=>g.value=t),class:"w-full p-2 border rounded",placeholder:"请输入服务器上传地址"},null,512),[[D,g.value]])]),e("div",{class:"flex flex-wrap gap-2"},[e("button",{class:"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",onClick:E}," PDF格式上传 "),e("button",{class:"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",onClick:M}," DOM图像上传 ")])])]),Ke])]))}},qe=P(Re,[["__scopeId","data-v-1b4af074"]]);export{qe as default};
