{"_Cp9XtYH-.js": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "Cp9XtYH-.js", "name": "vue.f36acd1f", "imports": ["node_modules/nuxt/dist/app/entry.js"]}, "_DYzyhJ5q.js": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "DYzyhJ5q.js", "name": "Footer", "imports": ["_L-3nxwpR.js", "node_modules/nuxt/dist/app/entry.js"]}, "_L-3nxwpR.js": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "L-3nxwpR.js", "name": "nuxt-link", "imports": ["node_modules/nuxt/dist/app/entry.js"]}, "components/demos/GaoPaiYiDemo.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "CXX19N4f.js", "name": "GaoPaiYiDemo", "src": "components/demos/GaoPaiYiDemo.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js"], "css": []}, "GaoPaiYiDemo.DqbD2G0_.css": {"file": "GaoPaiYiDemo.DqbD2G0_.css", "resourceType": "style", "prefetch": true, "preload": true}, "components/demos/ImageCapDemo.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "DUkI3E2_.js", "name": "ImageCapDemo", "src": "components/demos/ImageCapDemo.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js"], "css": []}, "ImageCapDemo.3KpPFnw3.css": {"file": "ImageCapDemo.3KpPFnw3.css", "resourceType": "style", "prefetch": true, "preload": true}, "components/demos/ScannerDemo.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "KIU17udJ.js", "name": "ScannerDemo", "src": "components/demos/ScannerDemo.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js"], "css": []}, "ScannerDemo.BKn8Znhx.css": {"file": "ScannerDemo.BKn8Znhx.css", "resourceType": "style", "prefetch": true, "preload": true}, "components/demos/SimpleDemo.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "CzkiU9vd.js", "name": "SimpleDemo", "src": "components/demos/SimpleDemo.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js"], "css": []}, "SimpleDemo.C1mDNgYj.css": {"file": "SimpleDemo.C1mDNgYj.css", "resourceType": "style", "prefetch": true, "preload": true}, "node_modules/nuxt/dist/app/components/error-404.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "q5UI1IJe.js", "name": "error-404", "src": "node_modules/nuxt/dist/app/components/error-404.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "_Cp9XtYH-.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "error-404.CjGVuf6H.css": {"file": "error-404.CjGVuf6H.css", "resourceType": "style", "prefetch": true, "preload": true}, "node_modules/nuxt/dist/app/components/error-500.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "DcJ3jc3j.js", "name": "error-500", "src": "node_modules/nuxt/dist/app/components/error-500.vue", "isDynamicEntry": true, "imports": ["_Cp9XtYH-.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "error-500.DFBAsgKS.css": {"file": "error-500.DFBAsgKS.css", "resourceType": "style", "prefetch": true, "preload": true}, "node_modules/nuxt/dist/app/entry.js": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "Dy7juzJL.js", "name": "entry", "src": "node_modules/nuxt/dist/app/entry.js", "isEntry": true, "dynamicImports": ["node_modules/nuxt/dist/app/components/error-404.vue", "node_modules/nuxt/dist/app/components/error-500.vue"], "_globalCSS": true}, "pages/contact.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "XfRs8NHu.js", "name": "contact", "src": "pages/contact.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "node_modules/nuxt/dist/app/entry.js", "_DYzyhJ5q.js"], "css": []}, "contact.B5JARLiN.css": {"file": "contact.B5JARLiN.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/demo.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "Co61pfxM.js", "name": "demo", "src": "pages/demo.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js", "_L-3nxwpR.js", "_DYzyhJ5q.js"], "dynamicImports": ["components/demos/ScannerDemo.vue", "components/demos/SimpleDemo.vue", "components/demos/ImageCapDemo.vue", "components/demos/GaoPaiYiDemo.vue"], "css": []}, "demo.CFZrKK-x.css": {"file": "demo.CFZrKK-x.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/docs/scanonweb-api.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "D4VnDwTm.js", "name": "scanonweb-api", "src": "pages/docs/scanonweb-api.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "_Cp9XtYH-.js", "_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "scanonweb-api.BRG4jYWy.css": {"file": "scanonweb-api.BRG4jYWy.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/docs/scanonweb-faq.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "Bcgw38WA.js", "name": "scanonweb-faq", "src": "pages/docs/scanonweb-faq.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "_Cp9XtYH-.js", "_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "scanonweb-faq.CVAKN5_o.css": {"file": "scanonweb-faq.CVAKN5_o.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/docs/scanonweb-features.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "VF4cvea4.js", "name": "scanonweb-features", "src": "pages/docs/scanonweb-features.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "_Cp9XtYH-.js", "_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "scanonweb-features.lonBaYeP.css": {"file": "scanonweb-features.lonBaYeP.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/docs/scanonweb-getting-started.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "DoxFx5l-.js", "name": "scanonweb-getting-started", "src": "pages/docs/scanonweb-getting-started.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "_Cp9XtYH-.js", "_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js"], "css": []}, "scanonweb-getting-started.CY2ikeOm.css": {"file": "scanonweb-getting-started.CY2ikeOm.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/documents.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "B04A3j4W.js", "name": "documents", "src": "pages/documents.vue", "isDynamicEntry": true, "imports": ["_L-3nxwpR.js", "node_modules/nuxt/dist/app/entry.js", "_DYzyhJ5q.js"], "css": []}, "documents.DqXxXzM1.css": {"file": "documents.DqXxXzM1.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/download.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "C1WhqXxa.js", "name": "download", "src": "pages/download.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js", "_DYzyhJ5q.js", "_L-3nxwpR.js"], "dynamicImports": ["utils/analytics.ts"]}, "pages/index.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "Ru65YT6T.js", "name": "index", "src": "pages/index.vue", "isDynamicEntry": true, "imports": ["_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js", "_L-3nxwpR.js"]}, "pages/products.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "CY8I3eQZ.js", "name": "products", "src": "pages/products.vue", "isDynamicEntry": true, "imports": ["node_modules/nuxt/dist/app/entry.js", "_DYzyhJ5q.js", "_L-3nxwpR.js"], "css": []}, "products.CDhbi0Rm.css": {"file": "products.CDhbi0Rm.css", "resourceType": "style", "prefetch": true, "preload": true}, "pages/purchase.vue": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "4-5J9pYw.js", "name": "purchase", "src": "pages/purchase.vue", "isDynamicEntry": true, "imports": ["_DYzyhJ5q.js", "node_modules/nuxt/dist/app/entry.js", "_L-3nxwpR.js"], "css": []}, "purchase.C3zoN31D.css": {"file": "purchase.C3zoN31D.css", "resourceType": "style", "prefetch": true, "preload": true}, "utils/analytics.ts": {"resourceType": "script", "module": true, "prefetch": true, "preload": true, "file": "CTyWRjd9.js", "name": "analytics", "src": "utils/analytics.ts", "isDynamicEntry": true}}