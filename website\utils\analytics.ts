// 百度统计工具函数

/**
 * 发送自定义事件到百度统计
 * @param category 事件分类
 * @param action 事件动作
 * @param label 事件标签（可选）
 */
export const trackEvent = (category: string, action: string, label?: string) => {
  if (process.client && (window as any)._hmt) {
    const eventData = ['_trackEvent', category, action];
    if (label) {
      eventData.push(label);
    }
    (window as any)._hmt.push(eventData);
  }
};

/**
 * 发送页面浏览事件到百度统计
 * @param pageUrl 页面URL
 */
export const trackPageView = (pageUrl: string) => {
  if (process.client && (window as any)._hmt) {
    (window as any)._hmt.push(['_trackPageview', pageUrl]);
  }
};

/**
 * 检查百度统计是否已加载
 */
export const isBaiduAnalyticsLoaded = (): boolean => {
  return process.client && !!(window as any)._hmt;
};

/**
 * 常用的事件追踪函数
 */
export const analytics = {
  // 下载事件
  trackDownload: (productName: string, version: string) => {
    trackEvent('Download', 'Click', `${productName}_${version}`);
  },
  
  // 联系事件
  trackContact: (method: string) => {
    trackEvent('Contact', 'Click', method);
  },
  
  // 产品查看事件
  trackProductView: (productName: string) => {
    trackEvent('Product', 'View', productName);
  },
  
  // 文档查看事件
  trackDocumentView: (documentName: string) => {
    trackEvent('Document', 'View', documentName);
  },
  
  // 表单提交事件
  trackFormSubmit: (formName: string) => {
    trackEvent('Form', 'Submit', formName);
  }
};
