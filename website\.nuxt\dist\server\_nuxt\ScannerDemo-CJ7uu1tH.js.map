{"version": 3, "file": "ScannerDemo-CJ7uu1tH.js", "sources": ["../../../../components/demos/ScannerDemo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scanner-demo\">\r\n    <div class=\"bg-white shadow-lg rounded-lg overflow-hidden p-6\">\r\n      <h2 class=\"text-2xl font-bold mb-4 text-blue-600 border-b pb-2\">\r\n        图像扫描管理系统\r\n      </h2>\r\n\r\n      <div class=\"mb-8\">\r\n        <h3 class=\"text-xl font-semibold mb-3 text-blue-600\">扫描设置</h3>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">扫描设备</label>\r\n            <select id=\"devices\" class=\"w-full p-2 border rounded\"></select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">分辨率</label>\r\n            <div class=\"flex items-center\">\r\n              <input\r\n                type=\"text\"\r\n                id=\"dpi_x\"\r\n                v-model=\"dpiX\"\r\n                class=\"w-16 p-2 border rounded\"\r\n              />\r\n              <span class=\"mx-2\">X</span>\r\n              <input\r\n                type=\"text\"\r\n                id=\"dpi_y\"\r\n                v-model=\"dpiY\"\r\n                class=\"w-16 p-2 border rounded\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">色彩模式</label>\r\n            <select\r\n              id=\"colorMode\"\r\n              v-model=\"colorMode\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"RGB\">彩色</option>\r\n              <option value=\"GRAY\">灰色</option>\r\n              <option value=\"BW\">黑白</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">显示设备对话框</label>\r\n            <select\r\n              id=\"showDialog\"\r\n              v-model=\"showDialog\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">显示</option>\r\n              <option value=\"false\">不显示</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">自动进纸模式</label>\r\n            <select\r\n              id=\"feedEnable\"\r\n              v-model=\"feedEnable\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">是</option>\r\n              <option value=\"false\">否</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">自动装填纸张</label>\r\n            <select\r\n              id=\"autoFeed\"\r\n              v-model=\"autoFeed\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">是</option>\r\n              <option value=\"false\">否</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">双面模式</label>\r\n            <select\r\n              id=\"dupxMode\"\r\n              v-model=\"dupxMode\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">是</option>\r\n              <option value=\"false\">否</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">自动纠偏</label>\r\n            <select\r\n              id=\"autoDeskew\"\r\n              v-model=\"autoDeskew\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">是</option>\r\n              <option value=\"false\">否</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">自动边框检测</label>\r\n            <select\r\n              id=\"autoBorderDetection\"\r\n              v-model=\"autoBorderDetection\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"true\">是</option>\r\n              <option value=\"false\">否</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap gap-2 mt-6\">\r\n          <button\r\n            class=\"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\"\r\n            @click=\"loadDevices\"\r\n          >\r\n            获取设备列表\r\n          </button>\r\n          <button\r\n            class=\"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n            @click=\"startScan\"\r\n          >\r\n            开始扫描\r\n          </button>\r\n          <button\r\n            class=\"btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\"\r\n            @click=\"clearAll\"\r\n          >\r\n            清空扫描结果\r\n          </button>\r\n          <button\r\n            class=\"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\"\r\n            @click=\"getAllImage\"\r\n          >\r\n            获取所有图像\r\n          </button>\r\n          <button\r\n            class=\"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors\"\r\n            @click=\"focusService\"\r\n          >\r\n            显示界面\r\n          </button>\r\n          <button\r\n            class=\"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors\"\r\n            @click=\"hideService\"\r\n          >\r\n            隐藏界面\r\n          </button>\r\n          <button\r\n            class=\"btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors\"\r\n            @click=\"saveas\"\r\n          >\r\n            本地另存\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-8\">\r\n        <h3 class=\"text-xl font-semibold mb-3 text-blue-600\">上传设置</h3>\r\n        <div class=\"bg-gray-100 p-4 rounded\">\r\n          <div class=\"mb-4\">\r\n            <label class=\"font-medium mb-1 block\">上传地址</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"uploadUrl\"\r\n              v-model=\"uploadUrl\"\r\n              class=\"w-full p-2 border rounded\"\r\n              placeholder=\"请输入服务器上传地址\"\r\n            />\r\n          </div>\r\n          <div class=\"flex flex-wrap gap-2\">\r\n            <button\r\n              class=\"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n              @click=\"uploadAllImageAsPdfFormat\"\r\n            >\r\n              PDF格式上传\r\n            </button>\r\n            <button\r\n              class=\"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n              @click=\"uploadImageFromDom\"\r\n            >\r\n              DOM图像上传\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <h3 class=\"text-xl font-semibold mb-3 text-blue-600\">扫描结果</h3>\r\n        <div\r\n          id=\"imageList\"\r\n          class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\"\r\n        ></div>\r\n      </div>\r\n\r\n      <div class=\"mt-6 p-4 bg-yellow-100 text-yellow-800 rounded\">\r\n        <p class=\"font-bold\">\r\n          注意：本演示需要下载安装托盘扫描服务才可正常工作，下载链接在下面。\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"mt-6 flex justify-center gap-4 border-t pt-4\">\r\n        <a\r\n          href=\"https://www.brainysoft.cn/download/ScanOnWebH5Install.exe\"\r\n          target=\"_blank\"\r\n          class=\"text-blue-600 hover:underline\"\r\n        >\r\n          扫描服务托盘程序下载\r\n        </a>\r\n        <a\r\n          href=\"https://www.brainysoft.cn/video/scanh5.mp4\"\r\n          target=\"_blank\"\r\n          class=\"text-blue-600 hover:underline\"\r\n        >\r\n          视频教程\r\n        </a>\r\n        <a\r\n          href=\"https://www.brainysoft.cn\"\r\n          target=\"_blank\"\r\n          class=\"text-blue-600 hover:underline\"\r\n        >\r\n          官方网站\r\n        </a>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from \"vue\";\r\n\r\n// 扫描配置参数\r\nconst dpiX = ref(\"300\");\r\nconst dpiY = ref(\"300\");\r\nconst colorMode = ref(\"RGB\");\r\nconst showDialog = ref(\"false\");\r\nconst feedEnable = ref(\"false\");\r\nconst autoFeed = ref(\"false\");\r\nconst dupxMode = ref(\"false\");\r\nconst autoDeskew = ref(\"false\");\r\nconst autoBorderDetection = ref(\"false\");\r\nconst uploadUrl = ref(\"http://localhost:44300/api/ImageUpload/upload\");\r\n\r\n// 扫描控件实例\r\nlet scanonweb = null;\r\n\r\n// 加载扫描设备列表\r\nconst loadDevices = () => {\r\n  if (scanonweb) {\r\n    scanonweb.loadDevices();\r\n  } else {\r\n    alert(\"扫描控件未初始化，请先加载扫描控件！\");\r\n  }\r\n};\r\n\r\n// 开始扫描\r\nconst startScan = () => {\r\n  if (!scanonweb) {\r\n    alert(\"扫描控件未初始化，请先加载扫描控件！\");\r\n    return;\r\n  }\r\n\r\n  const deviceSelect = document.getElementById(\"devices\");\r\n  if (deviceSelect.selectedIndex === -1) {\r\n    alert(\"请先刷新或者选中要使用的扫描设备后再开始扫描!\");\r\n    return;\r\n  }\r\n\r\n  // 设置扫描参数\r\n  scanonweb.scaner_work_config.dpi_x = dpiX.value;\r\n  scanonweb.scaner_work_config.dpi_y = dpiY.value;\r\n  scanonweb.scaner_work_config.deviceIndex = deviceSelect.selectedIndex;\r\n  scanonweb.scaner_work_config.colorMode = colorMode.value;\r\n  scanonweb.scaner_work_config.showDialog = showDialog.value;\r\n  scanonweb.scaner_work_config.autoFeedEnable = feedEnable.value;\r\n  scanonweb.scaner_work_config.autoFeed = autoFeed.value;\r\n  scanonweb.scaner_work_config.dupxMode = dupxMode.value;\r\n  scanonweb.scaner_work_config.autoDeskew = autoDeskew.value;\r\n  scanonweb.scaner_work_config.autoBorderDetection = autoBorderDetection.value;\r\n\r\n  // 开始扫描\r\n  scanonweb.startScan();\r\n};\r\n\r\n// 清空所有扫描结果\r\nconst clearAll = () => {\r\n  if (scanonweb) {\r\n    scanonweb.clearAll();\r\n    document.getElementById(\"imageList\").innerHTML = \"\";\r\n  }\r\n};\r\n\r\n// 获取所有图像\r\nconst getAllImage = () => {\r\n  if (scanonweb) {\r\n    scanonweb.getAllImage();\r\n  }\r\n};\r\n\r\n// 显示扫描服务界面\r\nconst focusService = () => {\r\n  if (scanonweb) {\r\n    scanonweb.setFocus();\r\n  }\r\n};\r\n\r\n// 隐藏扫描服务界面\r\nconst hideService = () => {\r\n  if (scanonweb) {\r\n    scanonweb.hidden();\r\n  }\r\n};\r\n\r\n// PDF格式上传\r\nconst uploadAllImageAsPdfFormat = () => {\r\n  if (!scanonweb) return;\r\n\r\n  const url =\r\n    uploadUrl.value || \"http://localhost:44300/api/ImageUpload/upload\";\r\n  const id = new Date().getTime().toString();\r\n  const desc = \"PDF扫描文档_\" + new Date().toLocaleDateString();\r\n\r\n  console.log(\"开始上传PDF，ID:\" + id + \"，描述:\" + desc);\r\n  scanonweb.uploadAllImageAsPdfToUrl(url, id, desc);\r\n};\r\n\r\n// DOM图像上传\r\nconst uploadImageFromDom = () => {\r\n  if (!scanonweb) return;\r\n\r\n  const imageList = document.getElementById(\"imageList\");\r\n  const url =\r\n    uploadUrl.value || \"http://localhost:44300/api/ImageUpload/upload\";\r\n  const imgList = imageList.querySelectorAll(\"img\");\r\n\r\n  if (imgList.length === 0) {\r\n    alert(\"没有可上传的图像！\");\r\n    return;\r\n  }\r\n\r\n  Array.from(imgList).forEach((imageDom, i) => {\r\n    const imageBase64 = imageDom.src.split(\",\")[1];\r\n\r\n    // 构造表单数据\r\n    const formData = new FormData();\r\n    formData.append(\"image\", imageBase64);\r\n    formData.append(\"imageIndex\", i);\r\n    formData.append(\"name\", \"test\");\r\n\r\n    // 使用fetch发送数据\r\n    fetch(url, {\r\n      method: \"POST\",\r\n      body: formData,\r\n    })\r\n      .then((response) => {\r\n        console.log(response);\r\n      })\r\n      .catch((error) => {\r\n        console.error(error);\r\n      });\r\n  });\r\n\r\n  alert(\"上传请求已发送！\");\r\n};\r\n\r\n// 本地另存为文件\r\nconst saveas = () => {\r\n  if (!scanonweb) return;\r\n\r\n  const fileName = prompt(\"请输入保存文件路径\", \"d:/test.pdf\");\r\n  if (fileName) {\r\n    scanonweb.saveAllImageToLocal(fileName);\r\n    alert(\"文件已保存到: \" + fileName);\r\n  }\r\n};\r\n\r\n// 初始化扫描控件和事件\r\nconst initScanonweb = () => {\r\n  // 检查是否已加载scanonweb.js\r\n  if (typeof ScanOnWeb === \"undefined\") {\r\n    console.error(\"ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件\");\r\n    return;\r\n  }\r\n\r\n  scanonweb = new ScanOnWeb();\r\n\r\n  // 响应返回扫描设备列表的回调函数\r\n  scanonweb.onGetDevicesListEvent = (msg) => {\r\n    const deviceListDom = document.getElementById(\"devices\");\r\n\r\n    // 清空设备列表\r\n    deviceListDom.innerHTML = \"\";\r\n\r\n    // 添加设备信息\r\n    for (let i = 0; i < msg.devices.length; ++i) {\r\n      const opt = document.createElement(\"option\");\r\n      opt.innerHTML = msg.devices[i];\r\n      if (i === msg.currentIndex) {\r\n        opt.selected = true;\r\n      }\r\n      deviceListDom.appendChild(opt);\r\n    }\r\n\r\n    // 设置授权信息\r\n    const licenseMode = 4; // 4代表逐台机器绑定授权信息授权方式\r\n    const licenseKey1 = \"\"; // 授权许可信息\r\n    const licenseKey2 = \"\"; // 授权许可信息\r\n    const checkLicenseUrl = \"http://127.0.0.1:28110/check\"; // 这里换成您的授权服务器真实ip地址\r\n    scanonweb.setLicenseKey(\r\n      licenseMode,\r\n      licenseKey1,\r\n      licenseKey2,\r\n      checkLicenseUrl\r\n    );\r\n  };\r\n\r\n  // 响应获取某一页图像的回调函数\r\n  scanonweb.onGetImageByIdEvent = (msg) => {\r\n    console.log(\r\n      \"获取图像事件回调,图像id:\" +\r\n        msg.imageIndex +\r\n        \" 图像总数:\" +\r\n        msg.imageCount +\r\n        \" 注意试用版随机删除图像会造成总数降低或不变\"\r\n    );\r\n    const imageListDom = document.getElementById(\"imageList\");\r\n\r\n    // 创建图像容器\r\n    const containerDiv = document.createElement(\"div\");\r\n    containerDiv.className =\r\n      \"image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg\";\r\n\r\n    // 创建图像元素\r\n    const imageDom = document.createElement(\"img\");\r\n    imageDom.src = \"data:image/jpg;base64,\" + msg.imageBase64;\r\n    imageDom.className = \"w-full h-64 object-contain\";\r\n    // 为img标签赋予额外的图像imageIndex属性\r\n    imageDom.setAttribute(\"imageIndex\", msg.imageIndex); // 后续修改图像时需要用到\r\n\r\n    // 创建图像信息覆盖层\r\n    const overlayDiv = document.createElement(\"div\");\r\n    overlayDiv.className =\r\n      \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm\";\r\n    overlayDiv.textContent = \"图像 #\" + (msg.imageIndex + 1);\r\n\r\n    // 将图像和覆盖层添加到容器\r\n    containerDiv.appendChild(imageDom);\r\n    containerDiv.appendChild(overlayDiv);\r\n\r\n    // 将容器添加到图像列表\r\n    imageListDom.appendChild(containerDiv);\r\n  };\r\n\r\n  // 响应用户在扫描托盘服务程序中对某一页图像进行了编辑的回调函数\r\n  scanonweb.onImageEditedEvent = (msg) => {\r\n    console.log(\"图像编辑事件回调,图像id:\" + msg.imageIndex);\r\n    // 遍历所有的img标签,如果imageIndex属性值等于msg.imageIndex,则替换图像\r\n    const imageListDom = document.getElementById(\"imageList\");\r\n    const imgList = imageListDom.querySelectorAll(\"img\");\r\n\r\n    for (let i = 0; i < imgList.length; i++) {\r\n      const imageDom = imgList[i];\r\n      if (parseInt(imageDom.getAttribute(\"imageIndex\")) === msg.imageIndex) {\r\n        imageDom.src = \"data:image/jpg;base64,\" + msg.imageBase64;\r\n        // 让img强制刷新\r\n        imageDom.style.display = \"none\";\r\n        imageDom.offsetHeight;\r\n        imageDom.style.display = \"block\";\r\n        break;\r\n      }\r\n    }\r\n  };\r\n\r\n  // 响应用户在托盘程序里面对图像顺序进行了拖拽调整的回调事件\r\n  scanonweb.onImageDrapEvent = (msg) => {\r\n    console.log(\r\n      \"图像顺序调整事件回调,调整前:\" +\r\n        msg.beforeIndex +\r\n        \" 调整后:\" +\r\n        msg.afterIndex\r\n    );\r\n    const imageListDom = document.getElementById(\"imageList\");\r\n    // 获取所有子节点的数组\r\n    const children = Array.from(imageListDom.children);\r\n    // 获取需要移动的元素\r\n    const moveElement = children[msg.beforeIndex];\r\n    // 从原位置删除\r\n    children.splice(msg.beforeIndex, 1);\r\n    // 插入到新位置\r\n    children.splice(msg.afterIndex, 0, moveElement);\r\n    // 重新排列DOM\r\n    children.forEach((child) => imageListDom.appendChild(child));\r\n  };\r\n\r\n  // 响应扫描完成事件\r\n  scanonweb.onScanFinishedEvent = (msg) => {\r\n    console.log(\r\n      \"扫描完成事件回调,扫描前:\" +\r\n        msg.imageBeforeCount +\r\n        \" 扫描后:\" +\r\n        msg.imageAfterCount\r\n    );\r\n  };\r\n\r\n  // 响应获取所有图像的回调函数\r\n  scanonweb.onGetAllImageEvent = (msg) => {\r\n    console.log(\"图像总数:\" + msg.imageCount);\r\n    console.log(\"当前选中编辑的图像id:\" + msg.currentSelected);\r\n    const imageListDom = document.getElementById(\"imageList\");\r\n    imageListDom.innerHTML = \"\";\r\n\r\n    if (msg.images.length === 0) {\r\n      console.log(\"没有图像可以显示\");\r\n      return;\r\n    }\r\n\r\n    for (let i = 0; i < msg.images.length; i++) {\r\n      // 创建图像容器\r\n      const containerDiv = document.createElement(\"div\");\r\n      containerDiv.className =\r\n        \"image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg\";\r\n\r\n      // 创建图像元素\r\n      const imageDom = document.createElement(\"img\");\r\n      imageDom.src = \"data:image/jpg;base64,\" + msg.images[i];\r\n      imageDom.className = \"w-full h-64 object-contain\";\r\n      imageDom.setAttribute(\"imageIndex\", i);\r\n\r\n      // 创建图像信息覆盖层\r\n      const overlayDiv = document.createElement(\"div\");\r\n      overlayDiv.className =\r\n        \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm\";\r\n      overlayDiv.textContent = \"图像 #\" + (i + 1);\r\n\r\n      // 将图像和覆盖层添加到容器\r\n      containerDiv.appendChild(imageDom);\r\n      containerDiv.appendChild(overlayDiv);\r\n\r\n      // 将容器添加到图像列表\r\n      imageListDom.appendChild(containerDiv);\r\n    }\r\n  };\r\n\r\n  // PDF上传结果回调\r\n  scanonweb.onUploadAllImageAsPdfToUrlEvent = (msg) => {\r\n    console.log(msg);\r\n    // 解析上传结果\r\n    try {\r\n      const uploadResult = JSON.parse(msg.uploadResult);\r\n      if (uploadResult && uploadResult.network === true) {\r\n        alert(\"PDF上传成功！\");\r\n      } else {\r\n        alert(\"PDF上传失败！错误信息: \" + (uploadResult.msg || \"未知错误\"));\r\n      }\r\n    } catch (e) {\r\n      console.error(\"解析上传结果失败:\", e);\r\n      alert(\"PDF上传处理失败！请检查网络连接和服务器状态。\");\r\n    }\r\n  };\r\n\r\n  // 绑定托盘上传按钮事件\r\n  scanonweb.onUploadEvent = (msg) => {\r\n    console.log(\"用户点击了开始上传按钮,当前图像总数:\" + msg.imageCount);\r\n    uploadAllImageAsPdfFormat();\r\n  };\r\n};\r\n\r\n// 加载扫描控件脚本\r\nconst loadScanonwebScript = () => {\r\n  return new Promise((resolve, reject) => {\r\n    // 检查是否已加载\r\n    if (document.getElementById(\"scanonweb-script\")) {\r\n      resolve();\r\n      return;\r\n    }\r\n\r\n    const script = document.createElement(\"script\");\r\n    script.id = \"scanonweb-script\";\r\n    script.src = \"/scanonweb.js\";\r\n    script.type = \"text/javascript\";\r\n    script.async = true;\r\n    script.onload = resolve;\r\n    script.onerror = reject;\r\n    document.head.appendChild(script);\r\n  });\r\n};\r\n\r\nonMounted(async () => {\r\n  try {\r\n    await loadScanonwebScript();\r\n    initScanonweb();\r\n  } catch (err) {\r\n    console.error(\"加载扫描控件失败:\", err);\r\n    alert(\"加载扫描控件失败，请确保服务器上存在scanonweb.js文件。\");\r\n  }\r\n});\r\n\r\nonUnmounted(() => {\r\n  // 清理资源\r\n  scanonweb = null;\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.scanner-demo {\r\n  font-family: \"Microsoft YaHei\", \"PingFang SC\", Arial, sans-serif;\r\n}\r\n\r\n.form-control {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.btn {\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.btn:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.image-container {\r\n  display: block;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .image-container img {\r\n    height: auto;\r\n  }\r\n}\r\n</style>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAyOM,UAAA,OAAO,IAAI,KAAK;AAChB,UAAA,OAAO,IAAI,KAAK;AAChB,UAAA,YAAY,IAAI,KAAK;AACrB,UAAA,aAAa,IAAI,OAAO;AACxB,UAAA,aAAa,IAAI,OAAO;AACxB,UAAA,WAAW,IAAI,OAAO;AACtB,UAAA,WAAW,IAAI,OAAO;AACtB,UAAA,aAAa,IAAI,OAAO;AACxB,UAAA,sBAAsB,IAAI,OAAO;AACjC,UAAA,YAAY,IAAI,+CAA+C;;;;;;;;;;;;;"}