{"version": 3, "file": "demo-Bb430eIF.js", "sources": ["../../../../pages/demo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bg-gray-50 min-h-screen\">\r\n    <Header />\r\n    <main>\r\n      <!-- 面包屑导航 -->\r\n      <div class=\"bg-white border-b border-gray-200\">\r\n        <div class=\"container mx-auto px-4 py-4\">\r\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\r\n            <svg\r\n              class=\"w-4 h-4\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 5l7 7-7 7\"\r\n              ></path>\r\n            </svg>\r\n            <span class=\"text-gray-900 font-medium\">在线演示</span>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 页面标题区域 -->\r\n      <div class=\"bg-white py-8\">\r\n        <div class=\"container mx-auto px-4\">\r\n          <div class=\"max-w-4xl\">\r\n            <h1 class=\"heading-primary mb-4\">产品演示中心</h1>\r\n            <p class=\"text-lg text-gray-600 mb-6\">\r\n              体验我们专业Web控件的强大功能，通过在线演示了解产品特性和使用方法\r\n            </p>\r\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>实时演示</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>功能完整</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>易于上手</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"container mx-auto py-8 px-4\">\r\n        <div class=\"flex flex-col lg:flex-row gap-8\">\r\n          <div class=\"lg:w-1/4\">\r\n            <DemoSidebar\r\n              :demos=\"demos\"\r\n              :selectedDemoId=\"selectedDemo?.id\"\r\n              @select=\"selectDemo\"\r\n            />\r\n          </div>\r\n          <div class=\"lg:w-3/4\">\r\n            <transition name=\"fade\" mode=\"out-in\">\r\n              <component\r\n                :is=\"selectedDemo?.component\"\r\n                :key=\"selectedDemo?.id\"\r\n              />\r\n            </transition>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n    <Footer />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, defineAsyncComponent } from \"vue\";\r\nimport Header from \"~/components/Header.vue\";\r\nimport Footer from \"~/components/Footer.vue\";\r\nimport DemoSidebar from \"~/components/DemoSidebar.vue\";\r\n\r\n// 使用异步组件按需加载各个演示组件\r\nconst ScannerDemo = defineAsyncComponent(() =>\r\n  import(\"~/components/demos/ScannerDemo.vue\")\r\n);\r\nconst SimpleDemo = defineAsyncComponent(() =>\r\n  import(\"~/components/demos/SimpleDemo.vue\")\r\n);\r\nconst ImageCapDemo = defineAsyncComponent(() =>\r\n  import(\"~/components/demos/ImageCapDemo.vue\")\r\n);\r\nconst GaoPaiYiDemo = defineAsyncComponent(() =>\r\n  import(\"~/components/demos/GaoPaiYiDemo.vue\")\r\n);\r\n\r\nconst demos = ref([\r\n  {\r\n    id: 1,\r\n    name: \"ScanOnWeb 扫描演示\",\r\n    description: \"基础扫描功能演示，包括设备选择、参数设置、图像获取等\",\r\n    component: ScannerDemo,\r\n    icon: \"document-scanner\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"简易扫描工具\",\r\n    description: \"简化版扫描工具，适合快速上手使用\",\r\n    component: SimpleDemo,\r\n    icon: \"document\",\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"ImageCapOnWeb 演示\",\r\n    description: \"摄像头图像采集功能演示\",\r\n    component: ImageCapDemo,\r\n    icon: \"camera\",\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"GaoPaiYi 高拍仪演示\",\r\n    description: \"高拍仪设备图像采集功能演示\",\r\n    component: GaoPaiYiDemo,\r\n    icon: \"photo\",\r\n  },\r\n]);\r\n\r\nconst selectedDemo = ref(null);\r\n\r\nconst selectDemo = (demo) => {\r\n  selectedDemo.value = demo;\r\n};\r\n\r\nonMounted(() => {\r\n  // 默认选中第一个演示\r\n  if (demos.value.length > 0) {\r\n    selectedDemo.value = demos.value[0];\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "names": ["demo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA,UAAM,cAAc;AAAA,MAAqB,MACvC,OAAO,2BAAoC;AAAA,IAAA;AAE7C,UAAM,aAAa;AAAA,MAAqB,MACtC,OAAO,0BAAmC;AAAA,IAAA;AAE5C,UAAM,eAAe;AAAA,MAAqB,MACxC,OAAO,4BAAqC;AAAA,IAAA;AAE9C,UAAM,eAAe;AAAA,MAAqB,MACxC,OAAO,4BAAqC;AAAA,IAAA;AAG9C,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,IAAA,CACD;AAEK,UAAA,eAAe,IAAI,IAAI;AAEvB,UAAA,aAAa,CAACA,UAAS;AAC3B,mBAAa,QAAQA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}