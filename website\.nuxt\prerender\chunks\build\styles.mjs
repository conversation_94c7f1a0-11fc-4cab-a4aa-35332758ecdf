const interopDefault = r => r.default || r || [];
const styles = {
  "node_modules/nuxt/dist/app/entry.js": () => import('./entry-styles.uA65jsqs.mjs').then(interopDefault),
  "pages/contact.vue": () => import('./contact-styles.-YwGc5TO.mjs').then(interopDefault),
  "pages/demo.vue": () => import('./demo-styles.Bb5DtSSj.mjs').then(interopDefault),
  "pages/docs/scanonweb-api.vue": () => import('./scanonweb-api-styles.BjiZYctW.mjs').then(interopDefault),
  "pages/docs/scanonweb-faq.vue": () => import('./scanonweb-faq-styles.D_3CRHjD.mjs').then(interopDefault),
  "pages/docs/scanonweb-features.vue": () => import('./scanonweb-features-styles.B2E9CwGR.mjs').then(interopDefault),
  "pages/docs/scanonweb-getting-started.vue": () => import('./scanonweb-getting-started-styles.Dtls-L8f.mjs').then(interopDefault),
  "pages/documents.vue": () => import('./documents-styles.DKcP6Hie.mjs').then(interopDefault),
  "pages/contact.vue?vue&type=style&index=0&scoped=37ff425d&lang.css": () => import('./contact-styles.BoEUfnRo.mjs').then(interopDefault),
  "pages/demo.vue?vue&type=style&index=0&scoped=ad3dd77b&lang.css": () => import('./demo-styles.b7mgx-pK.mjs').then(interopDefault),
  "pages/products.vue": () => import('./products-styles.KKqtDzVX.mjs').then(interopDefault),
  "pages/purchase.vue": () => import('./purchase-styles.HWQCmgdO.mjs').then(interopDefault),
  "pages/products.vue?vue&type=style&index=0&scoped=9e2630bc&lang.css": () => import('./products-styles.i9Ed9xDF.mjs').then(interopDefault),
  "pages/purchase.vue?vue&type=style&index=0&scoped=873a08a0&lang.css": () => import('./purchase-styles.BS-dmHH8.mjs').then(interopDefault),
  "pages/docs/scanonweb-api.vue?vue&type=style&index=0&scoped=d2fddfc7&lang.css": () => import('./scanonweb-api-styles.Cb1NkSE0.mjs').then(interopDefault),
  "pages/docs/scanonweb-faq.vue?vue&type=style&index=0&scoped=13cbb726&lang.css": () => import('./scanonweb-faq-styles.C1-arYzn.mjs').then(interopDefault),
  "pages/docs/scanonweb-features.vue?vue&type=style&index=0&scoped=b2103ab5&lang.css": () => import('./scanonweb-features-styles.DgtRxK0j.mjs').then(interopDefault),
  "pages/docs/scanonweb-getting-started.vue?vue&type=style&index=0&scoped=ec61ad0e&lang.css": () => import('./scanonweb-getting-started-styles.BQWHRnoY.mjs').then(interopDefault),
  "pages/documents.vue?vue&type=style&index=0&scoped=f24d6ae8&lang.css": () => import('./documents-styles.BVXJUusL.mjs').then(interopDefault),
  "components/demos/SimpleDemo.vue": () => import('./SimpleDemo-styles.BqumqV5A.mjs').then(interopDefault),
  "components/demos/ScannerDemo.vue": () => import('./ScannerDemo-styles.Do-J0HRy.mjs').then(interopDefault),
  "components/demos/ImageCapDemo.vue": () => import('./ImageCapDemo-styles.Can7A18f.mjs').then(interopDefault),
  "components/demos/GaoPaiYiDemo.vue": () => import('./GaoPaiYiDemo-styles.D3QsfzNJ.mjs').then(interopDefault),
  "components/demos/ImageCapDemo.vue?vue&type=style&index=0&scoped=db70dee0&lang.css": () => import('./ImageCapDemo-styles.CT42aSTO.mjs').then(interopDefault),
  "components/demos/GaoPaiYiDemo.vue?vue&type=style&index=0&scoped=9e9ef8b0&lang.css": () => import('./GaoPaiYiDemo-styles.CGQ3oMIZ.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue": () => import('./error-404-styles.WpyeoFtc.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue": () => import('./error-500-styles.BFuVBFDJ.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue?vue&type=style&index=0&scoped=922baad2&lang.css": () => import('./error-404-styles.CibK9Qn2.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue?vue&type=style&index=0&scoped=1e3620c9&lang.css": () => import('./error-500-styles.JhxDD0mW.mjs').then(interopDefault),
  "components/demos/SimpleDemo.vue?vue&type=style&index=0&scoped=b6686b1b&lang.css": () => import('./SimpleDemo-styles.D35KZ3C7.mjs').then(interopDefault),
  "components/demos/ScannerDemo.vue?vue&type=style&index=0&scoped=1b4af074&lang.css": () => import('./ScannerDemo-styles.DeH4UFui.mjs').then(interopDefault)
};

export { styles as default };
//# sourceMappingURL=styles.mjs.map
