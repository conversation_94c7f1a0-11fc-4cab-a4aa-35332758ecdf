import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { ref, h, computed, mergeProps, withCtx, createTextVNode, createVNode, resolveDynamicComponent, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderVNode } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import '../_/renderer.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const _sfc_main = {
  __name: "documents",
  __ssrInlineRender: true,
  setup(__props) {
    const products = ref([
      {
        id: 1,
        name: "ScanOnWeb",
        description: "\u4E13\u4E1A\u626B\u63CF\u4EEA\u63A7\u4EF6\u89E3\u51B3\u65B9\u6848"
      },
      {
        id: 2,
        name: "ImageCapOnWeb",
        description: "\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u63A7\u4EF6"
      },
      {
        id: 3,
        name: "GaoPaiYi",
        description: "\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6\u63A7\u4EF6"
      }
    ]);
    const selectedProductId = ref(1);
    const selectedCategoryId = ref("getting-started");
    const documentCategories = ref([
      {
        id: "getting-started",
        name: "\u5165\u95E8\u6307\u5357",
        description: "\u5FEB\u901F\u5F00\u59CB\u4F7F\u7528\u4EA7\u54C1\u7684\u57FA\u7840\u6559\u7A0B\u548C\u5B89\u88C5\u6307\u5357",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          })
        ])
      },
      {
        id: "api-docs",
        name: "API\u6587\u6863",
        description: "\u5B8C\u6574\u7684API\u53C2\u8003\u6587\u6863\u548C\u63A5\u53E3\u8BF4\u660E",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
          })
        ])
      },
      {
        id: "video-tutorials",
        name: "\u89C6\u9891\u6559\u7A0B",
        description: "\u76F4\u89C2\u7684\u89C6\u9891\u6559\u5B66\u5185\u5BB9\uFF0C\u5FEB\u901F\u638C\u63E1\u4F7F\u7528\u6280\u5DE7",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
          })
        ])
      },
      {
        id: "code-examples",
        name: "\u793A\u4F8B\u4EE3\u7801",
        description: "\u5B9E\u7528\u7684\u4EE3\u7801\u793A\u4F8B\u548C\u6700\u4F73\u5B9E\u8DF5",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          })
        ])
      },
      {
        id: "faq",
        name: "\u5E38\u89C1\u95EE\u9898",
        description: "\u5E38\u89C1\u95EE\u9898\u89E3\u7B54\u548C\u6545\u969C\u6392\u9664\u6307\u5357",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          })
        ])
      },
      {
        id: "features",
        name: "\u6280\u672F\u7279\u6027",
        description: "\u8BE6\u7EC6\u7684\u6280\u672F\u89C4\u683C\u8868\u683C\u548C\u5E73\u53F0\u517C\u5BB9\u6027\u4FE1\u606F",
        icon: () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          }),
          h("path", {
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-width": "2",
            d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          })
        ])
      }
    ]);
    const allDocuments = ref([
      // ScanOnWeb 文档
      {
        id: 1,
        title: "ScanOnWeb \u5165\u95E8\u6307\u5357",
        description: "\u8BE6\u7EC6\u7684\u96C6\u6210\u6559\u7A0B\uFF0C\u5305\u542BHTML\u548CVue3\u9879\u76EE\u7684\u5B8C\u6574\u793A\u4F8B\u4EE3\u7801",
        type: "guide",
        category: "getting-started",
        productId: 1,
        date: "2024-03-15",
        version: "v3.5.0",
        url: "/docs/scanonweb-getting-started"
      },
      {
        id: 16,
        title: "ScanOnWeb \u5FEB\u901F\u5165\u95E8\u6307\u5357",
        description: "5\u5206\u949F\u5FEB\u901F\u4E0A\u624BScanOnWeb\u63A7\u4EF6\uFF0C\u5305\u542B\u5B89\u88C5\u3001\u914D\u7F6E\u548C\u57FA\u672C\u4F7F\u7528",
        type: "pdf",
        category: "getting-started",
        productId: 1,
        date: "2024-02-15",
        version: "v3.5.0",
        downloadUrl: "/docs/ScanOnWeb-QuickStart.pdf"
      },
      {
        id: 2,
        title: "ScanOnWeb API \u53C2\u8003\u624B\u518C",
        description: "\u5B8C\u6574\u7684API\u63A5\u53E3\u6587\u6863\uFF0C\u5305\u542B\u6240\u6709\u65B9\u6CD5\u3001\u4E8B\u4EF6\u548C\u53C2\u6570\u8BF4\u660E",
        type: "code",
        category: "api-docs",
        productId: 1,
        date: "2024-02-20",
        version: "v3.5.0",
        downloadUrl: "/docs/scanonweb-api"
      },
      {
        id: 3,
        title: "ScanOnWeb \u89C6\u9891\u6559\u7A0B",
        description: "\u901A\u8FC7\u89C6\u9891\u6F14\u793A\u5B66\u4E60ScanOnWeb\u7684\u5B89\u88C5\u914D\u7F6E\u548C\u5E38\u7528\u529F\u80FD",
        type: "video",
        category: "video-tutorials",
        productId: 1,
        date: "2024-02-25",
        version: "v3.5.0",
        downloadUrl: "/docs/ScanOnWeb-Video.mp4"
      },
      {
        id: 4,
        title: "Vue3 + Spring Boot \u96C6\u6210\u793A\u4F8B",
        description: "Vue3\u524D\u7AEF + Spring Boot\u540E\u7AEF\u7684\u5B8C\u6574ScanOnWeb\u96C6\u6210\u9879\u76EE",
        type: "code",
        category: "code-examples",
        productId: 1,
        date: "2024-03-01",
        version: "v3.5.0",
        downloadUrl: "/downloads/examples/scanonweb-vue3-springboot.zip"
      },
      {
        id: 41,
        title: "Vue3 + ASP.NET Core \u96C6\u6210\u793A\u4F8B",
        description: "Vue3\u524D\u7AEF + ASP.NET Core\u540E\u7AEF\u7684\u5B8C\u6574ScanOnWeb\u96C6\u6210\u9879\u76EE",
        type: "code",
        category: "code-examples",
        productId: 1,
        date: "2024-03-05",
        version: "v3.5.0",
        downloadUrl: "/downloads/examples/scanonweb-vue3-aspnetcore.zip"
      },
      {
        id: 42,
        title: "HTML + JS + Spring Boot \u96C6\u6210\u793A\u4F8B",
        description: "\u539F\u751FHTML/JavaScript\u524D\u7AEF + Spring Boot\u540E\u7AEF\u7684ScanOnWeb\u96C6\u6210\u9879\u76EE",
        type: "code",
        category: "code-examples",
        productId: 1,
        date: "2024-03-08",
        version: "v3.5.0",
        downloadUrl: "/downloads/examples/scanonweb-html-springboot.zip"
      },
      {
        id: 43,
        title: "React + Spring Boot \u96C6\u6210\u793A\u4F8B",
        description: "React\u524D\u7AEF + Spring Boot\u540E\u7AEF\u7684\u5B8C\u6574ScanOnWeb\u96C6\u6210\u9879\u76EE",
        type: "code",
        category: "code-examples",
        productId: 1,
        date: "2024-03-10",
        version: "v3.5.0",
        downloadUrl: "/downloads/examples/scanonweb-react-springboot.zip"
      },
      {
        id: 44,
        title: "Vue3 + Go \u96C6\u6210\u793A\u4F8B",
        description: "Vue3\u524D\u7AEF + Go\u540E\u7AEF\u7684\u5B8C\u6574ScanOnWeb\u96C6\u6210\u9879\u76EE",
        type: "code",
        category: "code-examples",
        productId: 1,
        date: "2024-03-12",
        version: "v3.5.0",
        downloadUrl: "/downloads/examples/scanonweb-vue3-go.zip"
      },
      {
        id: 5,
        title: "ScanOnWeb \u5E38\u89C1\u95EE\u9898\u89E3\u7B54",
        description: "\u4F7F\u7528\u8FC7\u7A0B\u4E2D\u7684\u5E38\u89C1\u95EE\u9898\u548C\u89E3\u51B3\u65B9\u6848",
        type: "text",
        category: "faq",
        productId: 1,
        date: "2024-03-05",
        version: "v3.5.0",
        downloadUrl: "/docs/scanonweb-faq"
      },
      {
        id: 6,
        title: "ScanOnWeb \u6280\u672F\u7279\u6027\u8868\u683C",
        description: "\u5168\u9762\u7684\u6280\u672F\u89C4\u683C\u3001\u5E73\u53F0\u652F\u6301\u548C\u529F\u80FD\u7279\u6027\u5BF9\u6BD4\u8868\u683C",
        type: "table",
        category: "features",
        productId: 1,
        date: "2024-03-15",
        version: "v3.5.0",
        downloadUrl: "/docs/scanonweb-features"
      },
      // ImageCapOnWeb 文档
      {
        id: 7,
        title: "ImageCapOnWeb \u5FEB\u901F\u5165\u95E8\u6307\u5357",
        description: "\u6444\u50CF\u5934\u63A7\u4EF6\u7684\u5B89\u88C5\u914D\u7F6E\u548C\u57FA\u672C\u4F7F\u7528\u6559\u7A0B",
        type: "pdf",
        category: "getting-started",
        productId: 2,
        date: "2024-01-20",
        version: "v2.8.0",
        downloadUrl: "/docs/ImageCapOnWeb-QuickStart.pdf"
      },
      {
        id: 7,
        title: "ImageCapOnWeb API \u6587\u6863",
        description: "\u6444\u50CF\u5934\u63A7\u4EF6\u7684\u5B8C\u6574API\u63A5\u53E3\u8BF4\u660E",
        type: "code",
        category: "api-docs",
        productId: 2,
        date: "2024-01-25",
        version: "v2.8.0",
        downloadUrl: "/docs/ImageCapOnWeb-API.pdf"
      },
      {
        id: 8,
        title: "ImageCapOnWeb \u89C6\u9891\u6559\u7A0B",
        description: "\u901A\u8FC7\u89C6\u9891\u5B66\u4E60\u6444\u50CF\u5934\u63A7\u4EF6\u7684\u4F7F\u7528\u65B9\u6CD5",
        type: "video",
        category: "video-tutorials",
        productId: 2,
        date: "2024-02-05",
        version: "v2.8.0",
        downloadUrl: "/docs/ImageCapOnWeb-Video.mp4"
      },
      {
        id: 9,
        title: "ImageCapOnWeb React \u96C6\u6210\u793A\u4F8B",
        description: "\u5728React\u9879\u76EE\u4E2D\u96C6\u6210ImageCapOnWeb\u7684\u4EE3\u7801\u793A\u4F8B",
        type: "code",
        category: "code-examples",
        productId: 2,
        date: "2024-02-10",
        version: "v2.8.0",
        downloadUrl: "/docs/ImageCapOnWeb-React-Example.zip"
      },
      {
        id: 10,
        title: "ImageCapOnWeb \u5E38\u89C1\u95EE\u9898",
        description: "\u6444\u50CF\u5934\u63A7\u4EF6\u4F7F\u7528\u4E2D\u7684\u5E38\u89C1\u95EE\u9898\u548C\u89E3\u51B3\u65B9\u6848",
        type: "text",
        category: "faq",
        productId: 2,
        date: "2024-02-15",
        version: "v2.8.0",
        downloadUrl: "/docs/ImageCapOnWeb-FAQ.pdf"
      },
      // GaoPaiYi 文档
      {
        id: 11,
        title: "GaoPaiYi \u5FEB\u901F\u5165\u95E8\u6307\u5357",
        description: "\u9AD8\u62CD\u4EEA\u63A7\u4EF6\u7684\u5B89\u88C5\u548C\u57FA\u672C\u4F7F\u7528\u65B9\u6CD5",
        type: "pdf",
        category: "getting-started",
        productId: 3,
        date: "2024-03-05",
        version: "v2.0.1",
        downloadUrl: "/docs/GaoPaiYi-QuickStart.pdf"
      },
      {
        id: 12,
        title: "GaoPaiYi API \u53C2\u8003\u624B\u518C",
        description: "\u9AD8\u62CD\u4EEA\u63A7\u4EF6\u7684API\u63A5\u53E3\u6587\u6863",
        type: "code",
        category: "api-docs",
        productId: 3,
        date: "2024-03-10",
        version: "v2.0.1",
        downloadUrl: "/docs/GaoPaiYi-API.pdf"
      },
      {
        id: 13,
        title: "GaoPaiYi \u89C6\u9891\u6559\u7A0B",
        description: "\u901A\u8FC7\u89C6\u9891\u5B66\u4E60\u9AD8\u62CD\u4EEA\u63A7\u4EF6\u7684\u4F7F\u7528",
        type: "video",
        category: "video-tutorials",
        productId: 3,
        date: "2024-03-20",
        version: "v2.0.1",
        downloadUrl: "/docs/GaoPaiYi-Video.mp4"
      },
      {
        id: 14,
        title: "GaoPaiYi Angular \u96C6\u6210\u793A\u4F8B",
        description: "\u5728Angular\u9879\u76EE\u4E2D\u96C6\u6210GaoPaiYi\u7684\u4EE3\u7801\u793A\u4F8B",
        type: "code",
        category: "code-examples",
        productId: 3,
        date: "2024-03-25",
        version: "v2.0.1",
        downloadUrl: "/docs/GaoPaiYi-Angular-Example.zip"
      },
      {
        id: 15,
        title: "GaoPaiYi \u5E38\u89C1\u95EE\u9898",
        description: "\u9AD8\u62CD\u4EEA\u63A7\u4EF6\u4F7F\u7528\u4E2D\u7684\u95EE\u9898\u89E3\u7B54",
        type: "text",
        category: "faq",
        productId: 3,
        date: "2024-03-30",
        version: "v2.0.1",
        downloadUrl: "/docs/GaoPaiYi-FAQ.pdf"
      }
    ]);
    const filteredDocuments = computed(() => {
      return allDocuments.value.filter(
        (doc) => doc.productId === selectedProductId.value && doc.category === selectedCategoryId.value
      );
    });
    const getCurrentCategoryName = () => {
      const category = documentCategories.value.find(
        (cat) => cat.id === selectedCategoryId.value
      );
      return category ? category.name : "";
    };
    const getCurrentCategoryDescription = () => {
      const category = documentCategories.value.find(
        (cat) => cat.id === selectedCategoryId.value
      );
      return category ? category.description : "";
    };
    const getDocumentCount = (categoryId) => {
      return allDocuments.value.filter(
        (doc) => doc.productId === selectedProductId.value && doc.category === categoryId
      ).length;
    };
    const getDocumentTypeClass = (type) => {
      switch (type) {
        case "pdf":
          return "bg-red-500";
        case "video":
          return "bg-blue-500";
        case "code":
          return "bg-green-500";
        case "text":
          return "bg-gray-500";
        case "guide":
          return "bg-orange-500";
        case "table":
          return "bg-purple-500";
        default:
          return "bg-gray-400";
      }
    };
    const getDocumentIcon = (type) => {
      switch (type) {
        case "pdf":
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
              })
            ]
          );
        case "video":
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
              })
            ]
          );
        case "code":
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
              })
            ]
          );
        case "guide":
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              })
            ]
          );
        case "table":
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1V10z"
              })
            ]
          );
        default:
          return () => h(
            "svg",
            { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
            [
              h("path", {
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
                "stroke-width": "2",
                d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              })
            ]
          );
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-f24d6ae8>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-f24d6ae8><div class="bg-white border-b border-gray-200" data-v-f24d6ae8><div class="container mx-auto px-4 py-4" data-v-f24d6ae8><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-f24d6ae8>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-f24d6ae8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-f24d6ae8></path></svg><span class="text-gray-900 font-medium" data-v-f24d6ae8>\u6587\u6863\u8D44\u6599</span></nav></div></div><div class="bg-white py-12" data-v-f24d6ae8><div class="container mx-auto px-4" data-v-f24d6ae8><div class="max-w-4xl" data-v-f24d6ae8><h1 class="heading-primary mb-4" data-v-f24d6ae8>\u6280\u672F\u6587\u6863\u4E2D\u5FC3</h1><p class="text-xl text-gray-600 mb-6" data-v-f24d6ae8> \u5B8C\u6574\u7684\u4EA7\u54C1\u6587\u6863\u3001API\u53C2\u8003\u3001\u793A\u4F8B\u4EE3\u7801\u548C\u89C6\u9891\u6559\u7A0B\uFF0C\u52A9\u60A8\u5FEB\u901F\u4E0A\u624B\u548C\u6DF1\u5165\u4F7F\u7528\u6211\u4EEC\u7684\u4EA7\u54C1 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-f24d6ae8><div class="flex items-center" data-v-f24d6ae8><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-f24d6ae8><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-f24d6ae8></path></svg><span data-v-f24d6ae8>\u5B8C\u6574API\u6587\u6863</span></div><div class="flex items-center" data-v-f24d6ae8><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-f24d6ae8><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-f24d6ae8></path></svg><span data-v-f24d6ae8>\u5B9E\u7528\u4EE3\u7801\u793A\u4F8B</span></div><div class="flex items-center" data-v-f24d6ae8><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-f24d6ae8><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-f24d6ae8></path></svg><span data-v-f24d6ae8>\u89C6\u9891\u6559\u7A0B\u6307\u5BFC</span></div></div></div></div></div><div class="bg-orange-50 py-8" data-v-f24d6ae8><div class="container mx-auto px-4" data-v-f24d6ae8><div class="flex flex-col md:flex-row items-center justify-between" data-v-f24d6ae8><div class="mb-4 md:mb-0" data-v-f24d6ae8><h2 class="text-lg font-semibold text-gray-900 mb-2" data-v-f24d6ae8> \u9009\u62E9\u4EA7\u54C1\u67E5\u770B\u6587\u6863 </h2><p class="text-gray-600" data-v-f24d6ae8>\u6BCF\u4E2A\u4EA7\u54C1\u90FD\u6709\u5B8C\u6574\u7684\u6280\u672F\u6587\u6863\u548C\u4F7F\u7528\u6307\u5357</p></div><div class="flex flex-wrap gap-3" data-v-f24d6ae8><!--[-->`);
      ssrRenderList(products.value, (product) => {
        _push(`<button class="${ssrRenderClass([
          "px-6 py-3 rounded-lg font-semibold transition-all duration-200",
          selectedProductId.value === product.id ? "bg-orange-500 text-white shadow-lg" : "bg-white text-gray-700 hover:bg-orange-100 border border-gray-200"
        ])}" data-v-f24d6ae8>${ssrInterpolate(product.name)}</button>`);
      });
      _push(`<!--]--></div></div></div></div><div class="container mx-auto px-4 py-12" data-v-f24d6ae8><div class="grid grid-cols-1 lg:grid-cols-4 gap-8" data-v-f24d6ae8><div class="lg:col-span-1" data-v-f24d6ae8><div class="card-business p-6 sticky top-8" data-v-f24d6ae8><h3 class="heading-tertiary mb-4" data-v-f24d6ae8>\u6587\u6863\u5206\u7C7B</h3><nav class="space-y-2" data-v-f24d6ae8><!--[-->`);
      ssrRenderList(documentCategories.value, (category) => {
        _push(`<button class="${ssrRenderClass([
          "w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center",
          selectedCategoryId.value === category.id ? "bg-orange-100 text-orange-700 border-l-4 border-orange-500" : "text-gray-700 hover:bg-gray-50"
        ])}" data-v-f24d6ae8>`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(category.icon), { class: "w-5 h-5 mr-3" }, null), _parent);
        _push(`<span class="font-medium" data-v-f24d6ae8>${ssrInterpolate(category.name)}</span><span class="ml-auto text-sm text-gray-500" data-v-f24d6ae8>(${ssrInterpolate(getDocumentCount(category.id))})</span></button>`);
      });
      _push(`<!--]--></nav></div></div><div class="lg:col-span-3" data-v-f24d6ae8><div class="mb-6" data-v-f24d6ae8><h2 class="heading-secondary mb-2" data-v-f24d6ae8>${ssrInterpolate(getCurrentCategoryName())}</h2><p class="text-gray-600" data-v-f24d6ae8>${ssrInterpolate(getCurrentCategoryDescription())}</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-f24d6ae8><!--[-->`);
      ssrRenderList(filteredDocuments.value, (document) => {
        _push(`<div class="card-business p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer" data-v-f24d6ae8><div class="flex items-start" data-v-f24d6ae8><div class="${ssrRenderClass([getDocumentTypeClass(document.type), "w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0"])}" data-v-f24d6ae8>`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(getDocumentIcon(document.type)), { class: "w-6 h-6 text-white" }, null), _parent);
        _push(`</div><div class="flex-1" data-v-f24d6ae8><h3 class="font-bold text-gray-900 mb-2" data-v-f24d6ae8>${ssrInterpolate(document.title)}</h3><p class="text-gray-600 text-sm mb-3" data-v-f24d6ae8>${ssrInterpolate(document.description)}</p><div class="flex items-center justify-between text-xs text-gray-500" data-v-f24d6ae8><span data-v-f24d6ae8>${ssrInterpolate(document.version)}</span><span data-v-f24d6ae8>${ssrInterpolate(document.date)}</span></div></div></div></div>`);
      });
      _push(`<!--]--></div>`);
      if (filteredDocuments.value.length === 0) {
        _push(`<div class="text-center py-12" data-v-f24d6ae8><svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-f24d6ae8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-f24d6ae8></path></svg><h3 class="text-lg font-semibold text-gray-900 mb-2" data-v-f24d6ae8> \u6682\u65E0\u76F8\u5173\u6587\u6863 </h3><p class="text-gray-500" data-v-f24d6ae8>\u8BE5\u4EA7\u54C1\u7684\u6B64\u7C7B\u6587\u6863\u6B63\u5728\u51C6\u5907\u4E2D\uFF0C\u656C\u8BF7\u671F\u5F85</p></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/documents.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const documents = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-f24d6ae8"]]);

export { documents as default };
//# sourceMappingURL=documents-D_JGK4xR.mjs.map
