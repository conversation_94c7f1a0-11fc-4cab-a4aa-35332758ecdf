<template>
  <div class="bg-gray-100 min-h-screen">
    <Header />
    <main class="container mx-auto py-12 px-4">
      <div class="flex flex-col lg:flex-row gap-8">
        <div class="lg:w-1/4">
          <ProductSidebar
            :products="products"
            :selectedProductId="selectedProduct?.id"
            @select="selectProduct"
          />
        </div>
        <div class="lg:w-3/4">
          <transition name="fade" mode="out-in">
            <ProductDetails
              :key="selectedProduct?.id"
              :product="selectedProduct"
            />
          </transition>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { ref } from "vue";
import Header from "~/components/Header.vue";
import Footer from "~/components/Footer.vue";
import ProductSidebar from "~/components/ProductSidebar.vue";
import ProductDetails from "~/components/ProductDetails.vue";

const products = ref([
  {
    id: 1,
    name: "ScanOnWeb",
    description:
      "ScanOnWeb控件(软件著作权登记号 2013SR145420，证书号0651182)用于处理图像扫描编程，适合用于web环境下的扫描仪编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容目前主流的数款扫描设备，对于个别非按标准协议支持的扫描设备亦提供了集成支持，目前控件经过多年的发展已经很成熟稳定，被广泛的应用于办公OA、电子政务、纸质文档电子化等应用场景，客户单位遍布税务、公安、建筑、银行等多个行业，是目前国内唯一成熟稳定的扫描控件产品。",
    image: "/images/slide1.png",
    features: [
      "扫描设备选择",
      "自动进纸器连续多页扫描",
      "双面扫描模式",
      "自动纠偏模式",
      "多种图像扫描模式",
      "多种分辨率设置",
    ],
    specs: {
      浏览器兼容性:
        "所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等",
      图像扫描协议:
        "支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备，支持WIA协议的所有图像采集设备",
      图像编辑特性: "支持旋转、裁剪、填白、马赛克等多种处理",
      图像上传能力: "支持多种格式上传到指定URL",
    },
    useCases: [
      "办公OA",
      "电子政务",
      "纸质文档电子化",
      "税务系统",
      "公安系统",
      "银行业务",
    ],
    pricing: "请下载报价单了解详情",
    videoUrl: "https://example.com/scanonweb-demo.mp4",
    pdfUrl: "/ScanOnWeb-quotation.pdf",
    tableData: [
      {
        category: "浏览器兼容性",
        value:
          "所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等",
      },
      {
        category: "图像扫描协议",
        value:
          "1.支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备2.支持WIA协议的所有图像采集设备",
      },
      {
        category: "图像扫描参数控制",
        value:
          "1. 扫描设备选择 2. 是否显示扫描仪驱动程序内置设置对话框 3. 是否使用自动进纸器进行连续多页扫描 4. 是否使用自动装填纸张模式 5. 是否使用双面扫描模式 6. 是否使用自动纠偏模式 7. 是否使用自动边框检测模式 8. 图像扫描模式：黑白模式、灰度模式、彩色模式 9. dpi分辨率设置 10. 扫描结果传输模式：内存、文件、原生",
      },
      {
        category: "图像编辑特性",
        value:
          "1. 支持图像向左、向右旋转 90 度 2. 支持图像自定义角度旋转 3. 支持魔术棒选择模式图像选择 4. 支持矩形图像选择 5. 支持选中区域填白处理 6. 支持选中区域反选填白处理 7. 支持选中区域马赛克处理 8. 支持裁剪选区以外所有图像处理 9. 支持选中区域归红、归绿处理 10. 支持去除图像黑边处理 11. 支持去除图像底色处理 12. UNDO 撤销操作 13. 客户端单页本地图像保存 14. 客户端单页本地图像打印及打印预览 15. 批量图像删除 16. 多页扫描结果排序（直接拖拽顺序） 17. 扫描图像删除单页处理 18. 客户端多页图像保存 19. 客户端多页图像打印机打印预览 20. 鼠标滚轮缩放图像",
      },
      {
        category: "图像上传能力",
        value:
          "1. 图像转jpg base64编码，供前端显示或上传 2. 图像按照 tiff 格式上传到指定 url 3. 图像按照 pdf 格式上传到指定 url 4. 图像按照多页 jpg 方式上传到指定 url 5. 单独将某一页图像以 jpg 方式上传到指定 url 6. 上传到指定的 sftp 地址（需定制）",
      },
    ],
  },
  {
    id: 2,
    name: "ImageCapOnWeb",
    description:
      "ImageCapOnWeb控件用于处理摄像头图像采集编程，适合用于web环境下的图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的大部分摄像头数码设备。目前已经在多所学校、银行、政府机构中进行集成应用。",
    image: "/images/slide2.png",
    features: [
      "摄像头图像采集",
      "Web环境下的图像采集编程",
      "兼容Windows平台下的大部分摄像头设备",
      "无缝集成到多种编程技术",
    ],
    specs: {
      兼容平台: "Windows平台",
      支持设备: "大部分摄像头数码设备",
      集成技术: "jsp、php、asp.net等",
    },
    useCases: ["学校信息系统", "银行业务系统", "政府机构应用"],
    pricing: "请下载报价单了解详情",
    videoUrl: "https://example.com/imagecaponweb-demo.mp4",
    pdfUrl: "/ImageCapOnWeb-quotation.pdf",
  },
  {
    id: 3,
    name: "GaoPaiYi",
    description:
      "GaoPaiYi控件用于处理高拍仪图像采集编程，适合用于web环境下的高拍仪图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的以摄像头为核心设备的高拍仪产品。",
    image: "/images/slide3.png",
    features: [
      "高拍仪图像采集",
      "Web环境下的编程应用",
      "兼容Windows平台下的高拍仪产品",
      "无缝集成到各种编程技术",
    ],
    specs: {
      兼容平台: "Windows平台",
      支持设备: "以摄像头为核心设备的高拍仪产品",
      集成技术: "jsp、php、asp.net等",
    },
    useCases: ["文档扫描", "证件采集", "教育培训"],
    pricing: "请下载报价单了解详情",
    videoUrl: "https://example.com/gaopaiyi-demo.mp4",
    pdfUrl: "/GaoPaiYi-quotation.pdf",
  },
]);

// 默认选中第一个产品
const selectedProduct = ref(products.value[0] || null);

const selectProduct = (product) => {
  selectedProduct.value = product;
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
