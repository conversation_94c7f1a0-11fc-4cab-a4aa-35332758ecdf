<template>
  <section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h2 class="heading-primary mb-6">为什么选择我们</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          专注Web控件技术10年，为1000+企业客户提供稳定可靠的解决方案
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="feature in features"
          :key="feature.title"
          class="card-business p-8 text-center group"
        >
          <div
            class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-200 transition-colors duration-200"
          >
            <component :is="feature.icon" class="w-8 h-8 text-orange-500" />
          </div>
          <h3 class="heading-tertiary mb-4">{{ feature.title }}</h3>
          <p class="text-business">{{ feature.description }}</p>

          <!-- 添加数据支撑 -->
          <div v-if="feature.stats" class="mt-6 pt-6 border-t border-gray-100">
            <div class="flex justify-center space-x-6">
              <div
                v-for="stat in feature.stats"
                :key="stat.label"
                class="text-center"
              >
                <div class="text-2xl font-bold text-orange-500">
                  {{ stat.value }}
                </div>
                <div class="text-sm text-gray-500">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户案例数据 -->
      <div class="mt-20 bg-white rounded-xl p-8 shadow-sm">
        <div class="text-center mb-12">
          <h3 class="heading-secondary mb-4">客户遍布各行各业</h3>
          <p class="text-business">服务税务、公安、银行、教育等多个重要行业</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div
            v-for="industry in industries"
            :key="industry.name"
            class="text-center"
          >
            <div
              class="w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <component :is="industry.icon" class="w-10 h-10 text-blue-600" />
            </div>
            <h4 class="font-semibold text-gray-800 mb-2">
              {{ industry.name }}
            </h4>
            <p class="text-sm text-gray-500">{{ industry.count }}+ 客户</p>
          </div>
        </div>

        <!-- 典型用户列表 -->
        <div class="mt-16 pt-12 border-t border-gray-200">
          <div class="text-center mb-12">
            <h3 class="heading-secondary mb-4">典型用户</h3>
            <p class="text-business">部分合作伙伴和客户（以下排名不分先后）</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="client in typicalClients"
              :key="client.name"
              class="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors duration-200"
            >
              <div
                class="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm"
              >
                <component :is="client.icon" class="w-8 h-8 text-gray-600" />
              </div>
              <h4 class="font-semibold text-gray-900 text-sm leading-relaxed">
                {{ client.name }}
              </h4>
              <p class="text-xs text-gray-500 mt-2">{{ client.category }}</p>
            </div>
          </div>

          <div class="text-center mt-8">
            <p class="text-sm text-gray-500">
              感谢所有合作伙伴的信任与支持，我们将持续为您提供优质的产品和服务
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { h } from "vue";

// 图标组件
const PlatformIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z",
    }),
  ]);

const EaseIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
    }),
  ]);

const ScaleIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4",
    }),
  ]);

const SecurityIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
    }),
  ]);

const SupportIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944l7.071 7.071-7.071 7.071-7.071-7.071L12 2.944z",
    }),
  ]);

const CustomIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4",
    }),
  ]);

// 行业图标
const TaxIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z",
    }),
  ]);

const PoliceIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
    }),
  ]);

const BankIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
    }),
  ]);

const EduIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M12 14l9-5-9-5-9 5 9 5z",
    }),
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z",
    }),
  ]);

const features = [
  {
    title: "跨平台兼容",
    description:
      "国产软件厂商，产品全面支持Windows和信创国产化软硬件应用环境，通过多项认证。",
    icon: PlatformIcon,
    stats: [
      { value: "100%", label: "兼容性" },
      { value: "10+", label: "平台支持" },
    ],
  },
  {
    title: "简单易用",
    description:
      "直观的API设计和完善的文档，让开发者可以快速集成，大幅降低开发成本。",
    icon: EaseIcon,
    stats: [
      { value: "30分钟", label: "快速集成" },
      { value: "99%", label: "客户满意度" },
    ],
  },
  {
    title: "高度可扩展",
    description:
      "灵活的架构设计，支持二次开发和定制，可根据业务需求进行功能扩展。",
    icon: ScaleIcon,
  },
  {
    title: "安全可靠",
    description:
      "产品历经10年研发应用，在1000+企业客户中稳定运行，经受了实际生产环境考验。",
    icon: SecurityIcon,
    stats: [
      { value: "99.9%", label: "稳定性" },
      { value: "0", label: "安全事故" },
    ],
  },
  {
    title: "专业支持",
    description: "7×24小时技术支持团队，提供远程协助、现场服务等多种支持方式。",
    icon: SupportIcon,
  },
  {
    title: "定制服务",
    description: "根据客户具体需求，提供专业的定制开发服务和行业解决方案。",
    icon: CustomIcon,
  },
];

const industries = [
  { name: "税务系统", icon: TaxIcon, count: "200" },
  { name: "公安系统", icon: PoliceIcon, count: "150" },
  { name: "银行金融", icon: BankIcon, count: "300" },
  { name: "教育机构", icon: EduIcon, count: "350" },
];

// 客户图标组件
const GovernmentIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
    }),
  ]);

const CompanyIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6",
    }),
  ]);

const MediaIcon = () =>
  h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
    h("path", {
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "2",
      d: "M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z",
    }),
  ]);

// 典型用户列表
const typicalClients = [
  {
    name: "中共中央网信办",
    category: "中央机关",
    icon: GovernmentIcon,
  },
  {
    name: "中央编办事业发展中心",
    category: "中央机关",
    icon: GovernmentIcon,
  },
  {
    name: "中国人民银行宜昌支行",
    category: "金融机构",
    icon: BankIcon,
  },
  {
    name: "紫光软件系统有限公司",
    category: "科技企业",
    icon: CompanyIcon,
  },
  {
    name: "中国黄金集团公司",
    category: "国有企业",
    icon: CompanyIcon,
  },
  {
    name: "中国家庭医生杂志社",
    category: "媒体机构",
    icon: MediaIcon,
  },
  {
    name: "西北工业大学管理学院",
    category: "教育机构",
    icon: EduIcon,
  },
  {
    name: "万达信息股份有限公司",
    category: "上市公司",
    icon: CompanyIcon,
  },
  {
    name: "太极计算机股份有限公司",
    category: "上市公司",
    icon: CompanyIcon,
  },
  {
    name: "兴业银行长沙分行",
    category: "金融机构",
    icon: BankIcon,
  },
  {
    name: "用友汽车信息科技（上海）股份有限公司",
    category: "科技企业",
    icon: CompanyIcon,
  },
  {
    name: "用友软件有限公司四川分公司",
    category: "科技企业",
    icon: CompanyIcon,
  },
  {
    name: "中山市公安局南头分局",
    category: "公安系统",
    icon: PoliceIcon,
  },
  {
    name: "首都信息发展股份有限公司",
    category: "上市公司",
    icon: CompanyIcon,
  },
  {
    name: "神州数码信息系统有限公司",
    category: "科技企业",
    icon: CompanyIcon,
  },
];
</script>
