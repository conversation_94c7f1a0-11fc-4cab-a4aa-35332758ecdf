import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs } from 'vue/server-renderer';
import { _ as _export_sfc } from './server.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';

const _sfc_main = {
  __name: "ImageCapDemo",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "imagecap-demo" }, _attrs))} data-v-db70dee0><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-db70dee0><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-db70dee0> \u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u6F14\u793A </h2><div class="p-8 bg-gray-100 rounded mb-6 text-center" data-v-db70dee0><svg class="w-24 h-24 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" data-v-db70dee0></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" data-v-db70dee0></path></svg><h3 class="text-xl font-semibold mt-4 mb-2" data-v-db70dee0>ImageCapOnWeb \u529F\u80FD\u6F14\u793A</h3><p class="text-gray-600 mb-4" data-v-db70dee0> \u8BE5\u529F\u80FD\u9700\u8981\u53E6\u884C\u5B89\u88C5\u6444\u50CF\u5934\u91C7\u96C6\u63A7\u4EF6\u624D\u80FD\u4F7F\u7528\u3002 </p><button class="btn bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition-colors" data-v-db70dee0> \u4E0B\u8F7D\u5B89\u88C5\u6444\u50CF\u5934\u63A7\u4EF6 </button></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-db70dee0><div data-v-db70dee0><h3 class="text-lg font-semibold mb-3 text-blue-600" data-v-db70dee0>\u4E3B\u8981\u529F\u80FD</h3><ul class="space-y-2" data-v-db70dee0><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-db70dee0></path></svg> \u6444\u50CF\u5934\u8BBE\u5907\u9009\u62E9\u4E0E\u521D\u59CB\u5316 </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-db70dee0></path></svg> \u6444\u50CF\u5934\u53C2\u6570\u8C03\u6574\uFF08\u5206\u8FA8\u7387\u3001\u4EAE\u5EA6\u3001\u5BF9\u6BD4\u5EA6\u7B49\uFF09 </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-db70dee0></path></svg> \u5B9E\u65F6\u9884\u89C8\u6444\u50CF\u5934\u753B\u9762 </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-db70dee0></path></svg> \u56FE\u50CF\u6355\u83B7\u4E0E\u4FDD\u5B58 </li></ul></div><div data-v-db70dee0><h3 class="text-lg font-semibold mb-3 text-blue-600" data-v-db70dee0>\u5E94\u7528\u573A\u666F</h3><ul class="space-y-2" data-v-db70dee0><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-db70dee0></path></svg> \u8BC1\u4EF6\u91C7\u96C6\u4E0E\u8BC6\u522B </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-db70dee0></path></svg> \u4EBA\u8138\u91C7\u96C6\u4E0E\u8BC6\u522B </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-db70dee0></path></svg> \u6559\u80B2\u57F9\u8BAD\u89C6\u9891\u5F55\u5236 </li><li class="flex items-start" data-v-db70dee0><svg class="w-5 h-5 mr-2 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-db70dee0></path></svg> \u89C6\u9891\u4F1A\u8BAE\u4E0E\u8FDC\u7A0B\u4EA4\u6D41 </li></ul></div></div><div class="mt-8 border-t pt-6" data-v-db70dee0><h3 class="text-lg font-semibold mb-3 text-blue-600" data-v-db70dee0>\u76F8\u5173\u8D44\u6E90</h3><div class="flex flex-wrap gap-4" data-v-db70dee0><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-db70dee0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-db70dee0></path></svg> \u6280\u672F\u6587\u6863 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-db70dee0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" data-v-db70dee0></path></svg> \u89C6\u9891\u6559\u7A0B </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-db70dee0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-db70dee0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" data-v-db70dee0></path></svg> \u5F00\u53D1\u793A\u4F8B\u4E0B\u8F7D </a></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/ImageCapDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ImageCapDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-db70dee0"]]);

export { ImageCapDemo as default };
//# sourceMappingURL=ImageCapDemo-DjDT3NWX.mjs.map
