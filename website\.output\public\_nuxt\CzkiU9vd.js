import{_ as h,r as _,m as f,K as w,o as x,c as y,a as o,h as I,i as E,j as S,p as k,e as D}from"./Dy7juzJL.js";const c=l=>(k("data-v-b6686b1b"),l=l(),D(),l),B={class:"simple-demo"},C={class:"bg-white shadow-lg rounded-lg overflow-hidden p-6"},L=c(()=>o("h2",{class:"text-2xl font-bold mb-4 text-blue-600 border-b pb-2"}," 简易扫描工具 ",-1)),j={class:"mb-8"},M={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},N=c(()=>o("div",{class:"form-control"},[o("label",{class:"font-medium mb-1 block"},"扫描设备"),o("select",{id:"simple-devices",class:"w-full p-2 border rounded"})],-1)),A={class:"form-control"},G=c(()=>o("label",{class:"font-medium mb-1 block"},"色彩模式",-1)),H=c(()=>o("option",{value:"RGB"},"彩色",-1)),T=c(()=>o("option",{value:"GRAY"},"灰色",-1)),W=c(()=>o("option",{value:"BW"},"黑白",-1)),O=[H,T,W],P=S('<div data-v-b6686b1b><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-b6686b1b>扫描结果</h3><div id="simple-imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-b6686b1b></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-b6686b1b><p class="font-bold" data-v-b6686b1b> 注意：本演示需要下载安装托盘扫描服务才可正常工作。 </p><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline mt-2 inline-block" data-v-b6686b1b> 下载扫描服务 </a></div>',2),R={__name:"SimpleDemo",setup(l){const d=_("RGB");let n=null;const m=()=>{n?n.loadDevices():alert("扫描控件未初始化，请先加载扫描控件！")},b=()=>{if(!n){alert("扫描控件未初始化，请先加载扫描控件！");return}const t=document.getElementById("simple-devices");if(t.selectedIndex===-1){alert("请先获取并选择扫描设备！");return}n.scaner_work_config.deviceIndex=t.selectedIndex,n.scaner_work_config.colorMode=d.value,n.scaner_work_config.dpi_x=300,n.scaner_work_config.dpi_y=300,n.startScan()},p=()=>{n&&(n.clearAll(),document.getElementById("simple-imageList").innerHTML="")},u=()=>{if(!n)return;const t=prompt("请输入保存文件路径","d:/scan_result.pdf");t&&(n.saveAllImageToLocal(t),alert("文件已保存到: "+t))},g=()=>{if(typeof ScanOnWeb>"u"){console.error("ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件");return}n=new ScanOnWeb,n.onGetDevicesListEvent=t=>{const a=document.getElementById("simple-devices");a.innerHTML="";for(let e=0;e<t.devices.length;++e){const s=document.createElement("option");s.innerHTML=t.devices[e],e===t.currentIndex&&(s.selected=!0),a.appendChild(s)}},n.onGetImageByIdEvent=t=>{const a=document.getElementById("simple-imageList"),e=document.createElement("div");e.className="image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";const s=document.createElement("img");s.src="data:image/jpg;base64,"+t.imageBase64,s.className="w-full h-64 object-contain",s.setAttribute("imageIndex",t.imageIndex);const i=document.createElement("div");i.className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm",i.textContent="图像 #"+(t.imageIndex+1),e.appendChild(s),e.appendChild(i),a.appendChild(e)},n.onGetAllImageEvent=t=>{const a=document.getElementById("simple-imageList");if(a.innerHTML="",t.images.length!==0)for(let e=0;e<t.images.length;e++){const s=document.createElement("div");s.className="image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";const i=document.createElement("img");i.src="data:image/jpg;base64,"+t.images[e],i.className="w-full h-64 object-contain",i.setAttribute("imageIndex",e);const r=document.createElement("div");r.className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm",r.textContent="图像 #"+(e+1),s.appendChild(i),s.appendChild(r),a.appendChild(s)}}},v=()=>new Promise((t,a)=>{if(document.getElementById("scanonweb-script")){t();return}const e=document.createElement("script");e.id="scanonweb-script",e.src="/scanonweb.js",e.type="text/javascript",e.async=!0,e.onload=t,e.onerror=a,document.head.appendChild(e)});return f(async()=>{try{await v(),g()}catch(t){console.error("加载扫描控件失败:",t),alert("加载扫描控件失败，请确保服务器上存在scanonweb.js文件。")}}),w(()=>{n=null}),(t,a)=>(x(),y("div",B,[o("div",C,[L,o("div",j,[o("div",M,[N,o("div",A,[G,I(o("select",{id:"simple-colorMode","onUpdate:modelValue":a[0]||(a[0]=e=>d.value=e),class:"w-full p-2 border rounded"},O,512),[[E,d.value]])])]),o("div",{class:"flex flex-wrap gap-2"},[o("button",{class:"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",onClick:m}," 获取设备列表 "),o("button",{class:"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",onClick:b}," 开始扫描 "),o("button",{class:"btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors",onClick:p}," 清空结果 "),o("button",{class:"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",onClick:u}," 保存为PDF ")])]),P])]))}},U=h(R,[["__scopeId","data-v-b6686b1b"]]);export{U as default};
