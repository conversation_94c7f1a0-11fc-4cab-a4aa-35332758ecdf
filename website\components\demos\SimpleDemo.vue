<template>
  <div class="simple-demo">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
      <h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2">
        简易扫描工具
      </h2>

      <div class="mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="form-control">
            <label class="font-medium mb-1 block">扫描设备</label>
            <select
              id="simple-devices"
              class="w-full p-2 border rounded"
            ></select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">色彩模式</label>
            <select
              id="simple-colorMode"
              v-model="colorMode"
              class="w-full p-2 border rounded"
            >
              <option value="RGB">彩色</option>
              <option value="GRAY">灰色</option>
              <option value="BW">黑白</option>
            </select>
          </div>
        </div>

        <div class="flex flex-wrap gap-2">
          <button
            class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            @click="loadDevices"
          >
            获取设备列表
          </button>
          <button
            class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            @click="startScan"
          >
            开始扫描
          </button>
          <button
            class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            @click="clearAll"
          >
            清空结果
          </button>
          <button
            class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            @click="saveAsPdf"
          >
            保存为PDF
          </button>
        </div>
      </div>

      <div>
        <h3 class="text-xl font-semibold mb-3 text-blue-600">扫描结果</h3>
        <div
          id="simple-imageList"
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
        ></div>
      </div>

      <div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded">
        <p class="font-bold">
          注意：本演示需要下载安装托盘扫描服务才可正常工作。
        </p>
        <a
          href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe"
          target="_blank"
          class="text-blue-600 hover:underline mt-2 inline-block"
        >
          下载扫描服务
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

// 扫描配置参数
const colorMode = ref("RGB");

// 扫描控件实例
let scanonweb = null;

// 加载扫描设备列表
const loadDevices = () => {
  if (scanonweb) {
    scanonweb.loadDevices();
  } else {
    alert("扫描控件未初始化，请先加载扫描控件！");
  }
};

// 开始扫描
const startScan = () => {
  if (!scanonweb) {
    alert("扫描控件未初始化，请先加载扫描控件！");
    return;
  }

  const deviceSelect = document.getElementById("simple-devices");
  if (deviceSelect.selectedIndex === -1) {
    alert("请先获取并选择扫描设备！");
    return;
  }

  // 设置基本扫描参数
  scanonweb.scaner_work_config.deviceIndex = deviceSelect.selectedIndex;
  scanonweb.scaner_work_config.colorMode = colorMode.value;
  scanonweb.scaner_work_config.dpi_x = 300;
  scanonweb.scaner_work_config.dpi_y = 300;

  // 开始扫描
  scanonweb.startScan();
};

// 清空所有扫描结果
const clearAll = () => {
  if (scanonweb) {
    scanonweb.clearAll();
    document.getElementById("simple-imageList").innerHTML = "";
  }
};

// 保存为PDF
const saveAsPdf = () => {
  if (!scanonweb) return;

  const fileName = prompt("请输入保存文件路径", "d:/scan_result.pdf");
  if (fileName) {
    scanonweb.saveAllImageToLocal(fileName);
    alert("文件已保存到: " + fileName);
  }
};

// 初始化扫描控件和事件
const initScanonweb = () => {
  // 检查是否已加载scanonweb.js
  if (typeof ScanOnWeb === "undefined") {
    console.error("ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件");
    return;
  }

  scanonweb = new ScanOnWeb();

  // 响应返回扫描设备列表的回调函数
  scanonweb.onGetDevicesListEvent = (msg) => {
    const deviceListDom = document.getElementById("simple-devices");

    // 清空设备列表
    deviceListDom.innerHTML = "";

    // 添加设备信息
    for (let i = 0; i < msg.devices.length; ++i) {
      const opt = document.createElement("option");
      opt.innerHTML = msg.devices[i];
      if (i === msg.currentIndex) {
        opt.selected = true;
      }
      deviceListDom.appendChild(opt);
    }
  };

  // 响应获取某一页图像的回调函数
  scanonweb.onGetImageByIdEvent = (msg) => {
    const imageListDom = document.getElementById("simple-imageList");

    // 创建图像容器
    const containerDiv = document.createElement("div");
    containerDiv.className =
      "image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";

    // 创建图像元素
    const imageDom = document.createElement("img");
    imageDom.src = "data:image/jpg;base64," + msg.imageBase64;
    imageDom.className = "w-full h-64 object-contain";
    imageDom.setAttribute("imageIndex", msg.imageIndex);

    // 创建图像信息覆盖层
    const overlayDiv = document.createElement("div");
    overlayDiv.className =
      "absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm";
    overlayDiv.textContent = "图像 #" + (msg.imageIndex + 1);

    // 将图像和覆盖层添加到容器
    containerDiv.appendChild(imageDom);
    containerDiv.appendChild(overlayDiv);

    // 将容器添加到图像列表
    imageListDom.appendChild(containerDiv);
  };

  // 响应获取所有图像的回调函数
  scanonweb.onGetAllImageEvent = (msg) => {
    const imageListDom = document.getElementById("simple-imageList");
    imageListDom.innerHTML = "";

    if (msg.images.length === 0) {
      return;
    }

    for (let i = 0; i < msg.images.length; i++) {
      // 创建图像容器
      const containerDiv = document.createElement("div");
      containerDiv.className =
        "image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";

      // 创建图像元素
      const imageDom = document.createElement("img");
      imageDom.src = "data:image/jpg;base64," + msg.images[i];
      imageDom.className = "w-full h-64 object-contain";
      imageDom.setAttribute("imageIndex", i);

      // 创建图像信息覆盖层
      const overlayDiv = document.createElement("div");
      overlayDiv.className =
        "absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm";
      overlayDiv.textContent = "图像 #" + (i + 1);

      // 将图像和覆盖层添加到容器
      containerDiv.appendChild(imageDom);
      containerDiv.appendChild(overlayDiv);

      // 将容器添加到图像列表
      imageListDom.appendChild(containerDiv);
    }
  };
};

// 加载扫描控件脚本
const loadScanonwebScript = () => {
  return new Promise((resolve, reject) => {
    // 检查是否已加载
    if (document.getElementById("scanonweb-script")) {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.id = "scanonweb-script";
    script.src = "/scanonweb.js";
    script.type = "text/javascript";
    script.async = true;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

onMounted(async () => {
  try {
    await loadScanonwebScript();
    initScanonweb();
  } catch (err) {
    console.error("加载扫描控件失败:", err);
    alert("加载扫描控件失败，请确保服务器上存在scanonweb.js文件。");
  }
});

onUnmounted(() => {
  // 清理资源
  scanonweb = null;
});
</script>

<style scoped>
.simple-demo {
  font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
}

.form-control {
  margin-bottom: 1rem;
}

.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-2px);
}
</style>
