import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { u as useHead } from './index-BabADJUJ.mjs';
import { mergeProps, withCtx, createTextVNode, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderStyle, ssrInterpolate } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import '../_/renderer.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const htmlCode = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScanOnWeb \u626B\u63CF\u793A\u4F8B</title>
</head>
<body>
    <h1>ScanOnWeb \u626B\u63CF\u63A7\u4EF6\u6F14\u793A</h1>

    <!-- \u63A7\u5236\u9762\u677F -->
    <div id="controlPanel">
        <label>\u9009\u62E9\u626B\u63CF\u8BBE\u5907\uFF1A</label>
        <select id="deviceSelect"></select>

        <label>\u5206\u8FA8\u7387\uFF1A</label>
        <input type="number" id="dpiX" value="300" style="width: 60px;"> x
        <input type="number" id="dpiY" value="300" style="width: 60px;">

        <label>\u8272\u5F69\u6A21\u5F0F\uFF1A</label>
        <select id="colorMode">
            <option value="RGB">\u5F69\u8272</option>
            <option value="GRAY">\u7070\u8272</option>
            <option value="BW">\u9ED1\u767D</option>
        </select>

        <br><br>

        <button onclick="loadDevices()">\u83B7\u53D6\u8BBE\u5907\u5217\u8868</button>
        <button onclick="startScan()">\u5F00\u59CB\u626B\u63CF</button>
        <button onclick="getAllImages()">\u83B7\u53D6\u6240\u6709\u56FE\u50CF</button>
        <button onclick="clearAll()">\u6E05\u7A7A\u7ED3\u679C</button>
    </div>

    <!-- \u56FE\u50CF\u663E\u793A\u533A\u57DF -->
    <div id="imageContainer">
        <h3>\u626B\u63CF\u7ED3\u679C\uFF1A</h3>
        <div id="imageList"></div>
    </div>

    <!-- \u5F15\u5165ScanOnWeb\u63A7\u4EF6 -->
    <script src="js/scanonweb.js"><\/script>

    <script>
        // \u521D\u59CB\u5316\u626B\u63CF\u63A7\u4EF6
        let scanonweb = new ScanOnWeb();
        let selectedDeviceIndex = 0;

        // \u83B7\u53D6\u8BBE\u5907\u5217\u8868
        function loadDevices() {
            scanonweb.onGetDevicesListEvent = function(msg) {
                const deviceSelect = document.getElementById('deviceSelect');
                deviceSelect.innerHTML = '';

                msg.devices.forEach((device, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = device;
                    deviceSelect.appendChild(option);
                });

                selectedDeviceIndex = msg.currentIndex;
                deviceSelect.value = selectedDeviceIndex;
            };

            scanonweb.loadDevices();
        }

        // \u5F00\u59CB\u626B\u63CF
        function startScan() {
            if (selectedDeviceIndex === -1) {
                alert('\u8BF7\u5148\u9009\u62E9\u626B\u63CF\u8BBE\u5907\uFF01');
                return;
            }

            // \u914D\u7F6E\u626B\u63CF\u53C2\u6570
            scanonweb.scaner_work_config = {
                deviceIndex: selectedDeviceIndex,
                dpi_x: parseInt(document.getElementById('dpiX').value),
                dpi_y: parseInt(document.getElementById('dpiY').value),
                colorMode: document.getElementById('colorMode').value,
                showDialog: false,
                autoFeedEnable: false,
                autoFeed: false,
                dupxMode: false,
                autoDeskew: false,
                autoBorderDetection: false
            };

            scanonweb.startScan();
        }

        // \u83B7\u53D6\u6240\u6709\u56FE\u50CF
        function getAllImages() {
            scanonweb.onGetAllImageEvent = function(msg) {
                const imageList = document.getElementById('imageList');
                imageList.innerHTML = '';

                msg.images.forEach((imageBase64, index) => {
                    const img = document.createElement('img');
                    img.src = 'data:image/jpg;base64,' + imageBase64;
                    img.style.width = '200px';
                    img.style.height = '200px';
                    img.style.margin = '10px';
                    img.style.border = '1px solid #ccc';
                    imageList.appendChild(img);
                });
            };

            scanonweb.getAllImage();
        }

        // \u6E05\u7A7A\u6240\u6709\u56FE\u50CF
        function clearAll() {
            scanonweb.clearAll();
            document.getElementById('imageList').innerHTML = '';
        }

        // \u8BBE\u5907\u9009\u62E9\u53D8\u5316\u4E8B\u4EF6
        document.getElementById('deviceSelect').addEventListener('change', function(e) {
            selectedDeviceIndex = parseInt(e.target.value);
            scanonweb.selectScanDevice(selectedDeviceIndex);
        });

        // \u626B\u63CF\u5B8C\u6210\u4E8B\u4EF6\u56DE\u8C03
        scanonweb.onScanFinishedEvent = function(msg) {
            console.log('\u626B\u63CF\u5B8C\u6210\uFF0C\u56FE\u50CF\u6570\u91CF\uFF1A', msg.imageAfterCount);
            getAllImages(); // \u81EA\u52A8\u83B7\u53D6\u626B\u63CF\u7ED3\u679C
        };

        // \u9875\u9762\u52A0\u8F7D\u5B8C\u6210\u540E\u81EA\u52A8\u83B7\u53D6\u8BBE\u5907\u5217\u8868
        window.onload = function() {
            loadDevices();
        };
    <\/script>
</body>
</html>`;
const _sfc_main = {
  __name: "scanonweb-getting-started",
  __ssrInlineRender: true,
  setup(__props) {
    useHead({
      title: "ScanOnWeb \u5165\u95E8\u6307\u5357 - \u5FEB\u901F\u4E0A\u624B\u626B\u63CF\u63A7\u4EF6",
      meta: [
        {
          name: "description",
          content: "ScanOnWeb\u626B\u63CF\u63A7\u4EF6\u5165\u95E8\u6307\u5357\uFF0C\u8BE6\u7EC6\u4ECB\u7ECD\u5982\u4F55\u5728HTML\u548CVue3\u9879\u76EE\u4E2D\u96C6\u6210\u4F7F\u7528\u626B\u63CF\u529F\u80FD\uFF0C\u5305\u542B\u5B8C\u6574\u4EE3\u7801\u793A\u4F8B\u548C\u6700\u4F73\u5B9E\u8DF5\u3002"
        },
        {
          name: "keywords",
          content: "ScanOnWeb,\u626B\u63CF\u63A7\u4EF6,\u5165\u95E8\u6307\u5357,HTML,Vue3,JavaScript,WebSocket,\u626B\u63CF\u4EEA,\u96C6\u6210\u6559\u7A0B"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-ec61ad0e>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-ec61ad0e><div class="bg-white border-b border-gray-200" data-v-ec61ad0e><div class="container mx-auto px-4 py-4" data-v-ec61ad0e><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-ec61ad0e>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ec61ad0e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-ec61ad0e></path></svg>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u6587\u6863\u8D44\u6599`);
          } else {
            return [
              createTextVNode("\u6587\u6863\u8D44\u6599")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ec61ad0e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-ec61ad0e></path></svg><span class="text-gray-900 font-medium" data-v-ec61ad0e>ScanOnWeb \u5165\u95E8\u6307\u5357</span></nav></div></div><div class="bg-white py-8" data-v-ec61ad0e><div class="container mx-auto px-4" data-v-ec61ad0e><div class="max-w-4xl" data-v-ec61ad0e><h1 class="heading-primary mb-4" data-v-ec61ad0e>ScanOnWeb \u5165\u95E8\u6307\u5357</h1><p class="text-lg text-gray-600 mb-6" data-v-ec61ad0e> \u5FEB\u901F\u4E0A\u624BScanOnWeb\u626B\u63CF\u63A7\u4EF6\uFF0C\u652F\u6301\u539F\u751FHTML\u548CVue3\u9879\u76EE\u96C6\u6210 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-ec61ad0e><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>5\u5206\u949F\u5FEB\u901F\u4E0A\u624B</span></div><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>\u5B8C\u6574\u4EE3\u7801\u793A\u4F8B</span></div><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>\u591A\u6846\u67B6\u652F\u6301</span></div></div></div></div></div><div class="container mx-auto py-12 px-4" data-v-ec61ad0e><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>\u6982\u8FF0</h2><p class="text-business mb-4" data-v-ec61ad0e> ScanOnWeb\u662F\u4E00\u6B3E\u4E13\u4E1A\u7684Web\u626B\u63CF\u63A7\u4EF6\uFF0C\u901A\u8FC7WebSocket\u4E0E\u672C\u5730\u6258\u76D8\u670D\u52A1\u901A\u4FE1\uFF0C\u5B9E\u73B0\u5728\u6D4F\u89C8\u5668\u4E2D\u76F4\u63A5\u63A7\u5236\u626B\u63CF\u4EEA\u8BBE\u5907\u3002 \u63A7\u4EF6\u652F\u6301\u591A\u79CD\u626B\u63CF\u6A21\u5F0F\u3001\u56FE\u50CF\u5904\u7406\u529F\u80FD\uFF0C\u5E76\u53EF\u65E0\u7F1D\u96C6\u6210\u5230\u5404\u79CDWeb\u5E94\u7528\u4E2D\u3002 </p><div class="feature-highlight" data-v-ec61ad0e><h4 class="font-bold text-orange-700 mb-2" data-v-ec61ad0e>\u91CD\u8981\u63D0\u9192</h4><p class="text-orange-600" data-v-ec61ad0e> ScanOnWeb\u63A7\u4EF6\u9700\u8981\u7528\u6237\u5728\u672C\u5730\u5B89\u88C5\u6258\u76D8\u670D\u52A1\u7A0B\u5E8F\u624D\u80FD\u6B63\u5E38\u5DE5\u4F5C\u3002\u63A7\u4EF6\u6587\u4EF6\u9700\u8981\u624B\u52A8\u590D\u5236\u5230\u9879\u76EE\u4E2D\uFF0C\u672A\u53D1\u5E03\u5230npm\u4ED3\u5E93\u3002 </p></div></div><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>\u7CFB\u7EDF\u8981\u6C42</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-ec61ad0e><div data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u5BA2\u6237\u7AEF\u8981\u6C42</h3><ul class="space-y-2 text-business" data-v-ec61ad0e><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Windows 7/8/10/11 \u6216 Linux </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> \u652F\u6301WebSocket\u7684\u73B0\u4EE3\u6D4F\u89C8\u5668 </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> \u517C\u5BB9TWAIN 1.9+\u6216WIA\u534F\u8BAE\u7684\u626B\u63CF\u4EEA </li></ul></div><div data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u6D4F\u89C8\u5668\u652F\u6301</h3><ul class="space-y-2 text-business" data-v-ec61ad0e><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Chrome 60+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Firefox 55+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Edge 79+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Internet Explorer 11 </li></ul></div></div></div><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>\u5B89\u88C5\u6B65\u9AA4</h2><div class="space-y-6" data-v-ec61ad0e><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u6B65\u9AA41\uFF1A\u4E0B\u8F7D\u6258\u76D8\u670D\u52A1\u7A0B\u5E8F</h3><p class="text-business mb-4" data-v-ec61ad0e> \u9996\u5148\u9700\u8981\u5728\u5BA2\u6237\u7AEF\u8BA1\u7B97\u673A\u4E0A\u5B89\u88C5ScanOnWeb\u6258\u76D8\u670D\u52A1\u7A0B\u5E8F\uFF0C\u8BE5\u7A0B\u5E8F\u8D1F\u8D23\u4E0E\u626B\u63CF\u4EEA\u786C\u4EF6\u901A\u4FE1\u3002 </p><div class="bg-gray-50 p-4 rounded-lg" data-v-ec61ad0e><p class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>\u4E0B\u8F7D\u94FE\u63A5\uFF1A</p><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" class="text-orange-500 hover:text-orange-600 font-medium" target="_blank" data-v-ec61ad0e> ScanOnWebH5Install.exe </a></div></div><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u6B65\u9AA42\uFF1A\u83B7\u53D6\u63A7\u4EF6\u6587\u4EF6</h3><p class="text-business mb-4" data-v-ec61ad0e> \u4ECE\u5B98\u65B9\u83B7\u53D6scanonweb.js\u63A7\u4EF6\u6587\u4EF6\uFF0C\u8BE5\u6587\u4EF6\u5305\u542B\u4E86\u6240\u6709\u626B\u63CF\u529F\u80FD\u7684JavaScript API\u3002 </p><div class="bg-gray-50 p-4 rounded-lg" data-v-ec61ad0e><p class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>\u6587\u4EF6\u8BF4\u660E\uFF1A</p><ul class="text-sm space-y-1" data-v-ec61ad0e><li data-v-ec61ad0e> \u2022 <code class="bg-gray-200 px-2 py-1 rounded" data-v-ec61ad0e>scanonweb.js</code> - \u6838\u5FC3\u63A7\u4EF6\u6587\u4EF6 </li><li data-v-ec61ad0e>\u2022 \u7248\u672C\uFF1A1.0.1</li><li data-v-ec61ad0e>\u2022 \u5927\u5C0F\uFF1A\u7EA615KB</li></ul></div></div><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u6B65\u9AA43\uFF1A\u590D\u5236\u6587\u4EF6\u5230\u9879\u76EE</h3><p class="text-business mb-4" data-v-ec61ad0e> \u5C06scanonweb.js\u6587\u4EF6\u590D\u5236\u5230\u60A8\u7684Web\u9879\u76EE\u76EE\u5F55\u4E2D\u3002\u5EFA\u8BAE\u653E\u5728js\u6216assets\u76EE\u5F55\u4E0B\u3002 </p><div class="bg-gray-100 border border-gray-300 p-4 rounded-lg" data-v-ec61ad0e><div class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>\u9879\u76EE\u7ED3\u6784\u793A\u4F8B\uFF1A</div><div class="font-mono text-sm text-gray-800" data-v-ec61ad0e><div data-v-ec61ad0e>your-project/</div><div data-v-ec61ad0e>\u251C\u2500\u2500 js/</div><div data-v-ec61ad0e> \u2502\xA0\xA0\xA0\u2514\u2500\u2500 scanonweb.js\xA0\xA0<span class="text-orange-600" data-v-ec61ad0e>\u2190 \u63A7\u4EF6\u6587\u4EF6</span></div><div data-v-ec61ad0e>\u251C\u2500\u2500 index.html</div><div data-v-ec61ad0e>\u2514\u2500\u2500 ...</div></div></div></div></div></div><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>\u539F\u751FHTML\u96C6\u6210</h2><div class="space-y-6" data-v-ec61ad0e><p class="text-business" data-v-ec61ad0e> \u5728\u539F\u751FHTML\u9879\u76EE\u4E2D\u4F7F\u7528ScanOnWeb\u63A7\u4EF6\u975E\u5E38\u7B80\u5355\uFF0C\u53EA\u9700\u8981\u5F15\u5165JavaScript\u6587\u4EF6\u5E76\u521D\u59CB\u5316\u5373\u53EF\u3002 </p><div data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>\u5B8C\u6574HTML\u793A\u4F8B</h3><div class="bg-gray-50 border border-gray-300 p-4 rounded-lg" data-v-ec61ad0e><div class="text-sm text-gray-600 mb-3" data-v-ec61ad0e> \u590D\u5236\u4EE5\u4E0B\u4EE3\u7801\u5230\u60A8\u7684HTML\u6587\u4EF6\u4E2D\uFF1A </div><div class="bg-white border border-gray-200 p-4 rounded-lg" style="${ssrRenderStyle({ "max-height": "600px", "overflow-y": "auto" })}" data-v-ec61ad0e><pre class="font-mono text-sm text-gray-800 whitespace-pre-wrap m-0 p-0" data-v-ec61ad0e>${ssrInterpolate(htmlCode)}</pre></div></div></div><div class="bg-blue-50 border border-blue-200 rounded-lg p-6" data-v-ec61ad0e><h4 class="font-bold text-blue-800 mb-3" data-v-ec61ad0e>\u5173\u952E\u4EE3\u7801\u8BF4\u660E</h4><ul class="space-y-2 text-blue-700" data-v-ec61ad0e><li data-v-ec61ad0e><strong data-v-ec61ad0e>\u521D\u59CB\u5316\uFF1A</strong><code data-v-ec61ad0e>let scanonweb = new ScanOnWeb();</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>\u83B7\u53D6\u8BBE\u5907\uFF1A</strong><code data-v-ec61ad0e>scanonweb.loadDevices()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>\u5F00\u59CB\u626B\u63CF\uFF1A</strong> <code data-v-ec61ad0e>scanonweb.startScan()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>\u83B7\u53D6\u56FE\u50CF\uFF1A</strong><code data-v-ec61ad0e>scanonweb.getAllImage()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>\u4E8B\u4EF6\u56DE\u8C03\uFF1A</strong> \u901A\u8FC7 <code data-v-ec61ad0e>onScanFinishedEvent</code> \u7B49\u4E8B\u4EF6\u5904\u7406\u626B\u63CF\u7ED3\u679C </li></ul></div></div></div><div class="card-business p-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>\u6280\u672F\u652F\u6301</h2><div class="bg-orange-50 border border-orange-200 rounded-lg p-6" data-v-ec61ad0e><div class="flex items-start" data-v-ec61ad0e><svg class="w-6 h-6 text-orange-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><div data-v-ec61ad0e><h4 class="font-bold text-orange-800 mb-2" data-v-ec61ad0e>\u9700\u8981\u5E2E\u52A9\uFF1F</h4><p class="text-orange-700 mb-4" data-v-ec61ad0e> \u5982\u679C\u60A8\u5728\u96C6\u6210\u8FC7\u7A0B\u4E2D\u9047\u5230\u95EE\u9898\uFF0C\u6216\u9700\u8981\u66F4\u591A\u6280\u672F\u652F\u6301\uFF0C\u8BF7\u8054\u7CFB\u6211\u4EEC\u7684\u6280\u672F\u56E2\u961F\u3002 </p><div class="space-y-2 text-orange-700" data-v-ec61ad0e><p data-v-ec61ad0e><strong data-v-ec61ad0e>\u6280\u672F\u652F\u6301\u90AE\u7BB1\uFF1A</strong> <EMAIL></p><p data-v-ec61ad0e><strong data-v-ec61ad0e>\u6280\u672F\u652F\u6301QQ\uFF1A</strong> 123456789</p><p data-v-ec61ad0e><strong data-v-ec61ad0e>\u5DE5\u4F5C\u65F6\u95F4\uFF1A</strong> \u5468\u4E00\u81F3\u5468\u4E94 9:00-18:00</p></div><div class="mt-4" data-v-ec61ad0e>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/contact",
        class: "btn-primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u8054\u7CFB\u6280\u672F\u652F\u6301 `);
          } else {
            return [
              createTextVNode(" \u8054\u7CFB\u6280\u672F\u652F\u6301 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/docs/scanonweb-getting-started.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const scanonwebGettingStarted = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-ec61ad0e"]]);

export { scanonwebGettingStarted as default };
//# sourceMappingURL=scanonweb-getting-started-B2N3QRJP.mjs.map
