{"version": 3, "file": "SimpleDemo-C6RzC_wr.js", "sources": ["../../../../components/demos/SimpleDemo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"simple-demo\">\r\n    <div class=\"bg-white shadow-lg rounded-lg overflow-hidden p-6\">\r\n      <h2 class=\"text-2xl font-bold mb-4 text-blue-600 border-b pb-2\">\r\n        简易扫描工具\r\n      </h2>\r\n\r\n      <div class=\"mb-8\">\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">扫描设备</label>\r\n            <select\r\n              id=\"simple-devices\"\r\n              class=\"w-full p-2 border rounded\"\r\n            ></select>\r\n          </div>\r\n          <div class=\"form-control\">\r\n            <label class=\"font-medium mb-1 block\">色彩模式</label>\r\n            <select\r\n              id=\"simple-colorMode\"\r\n              v-model=\"colorMode\"\r\n              class=\"w-full p-2 border rounded\"\r\n            >\r\n              <option value=\"RGB\">彩色</option>\r\n              <option value=\"GRAY\">灰色</option>\r\n              <option value=\"BW\">黑白</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap gap-2\">\r\n          <button\r\n            class=\"btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\"\r\n            @click=\"loadDevices\"\r\n          >\r\n            获取设备列表\r\n          </button>\r\n          <button\r\n            class=\"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n            @click=\"startScan\"\r\n          >\r\n            开始扫描\r\n          </button>\r\n          <button\r\n            class=\"btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\"\r\n            @click=\"clearAll\"\r\n          >\r\n            清空结果\r\n          </button>\r\n          <button\r\n            class=\"btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n            @click=\"saveAsPdf\"\r\n          >\r\n            保存为PDF\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <h3 class=\"text-xl font-semibold mb-3 text-blue-600\">扫描结果</h3>\r\n        <div\r\n          id=\"simple-imageList\"\r\n          class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\"\r\n        ></div>\r\n      </div>\r\n\r\n      <div class=\"mt-6 p-4 bg-yellow-100 text-yellow-800 rounded\">\r\n        <p class=\"font-bold\">\r\n          注意：本演示需要下载安装托盘扫描服务才可正常工作。\r\n        </p>\r\n        <a\r\n          href=\"https://www.brainysoft.cn/download/ScanOnWebH5Install.exe\"\r\n          target=\"_blank\"\r\n          class=\"text-blue-600 hover:underline mt-2 inline-block\"\r\n        >\r\n          下载扫描服务\r\n        </a>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from \"vue\";\r\n\r\n// 扫描配置参数\r\nconst colorMode = ref(\"RGB\");\r\n\r\n// 扫描控件实例\r\nlet scanonweb = null;\r\n\r\n// 加载扫描设备列表\r\nconst loadDevices = () => {\r\n  if (scanonweb) {\r\n    scanonweb.loadDevices();\r\n  } else {\r\n    alert(\"扫描控件未初始化，请先加载扫描控件！\");\r\n  }\r\n};\r\n\r\n// 开始扫描\r\nconst startScan = () => {\r\n  if (!scanonweb) {\r\n    alert(\"扫描控件未初始化，请先加载扫描控件！\");\r\n    return;\r\n  }\r\n\r\n  const deviceSelect = document.getElementById(\"simple-devices\");\r\n  if (deviceSelect.selectedIndex === -1) {\r\n    alert(\"请先获取并选择扫描设备！\");\r\n    return;\r\n  }\r\n\r\n  // 设置基本扫描参数\r\n  scanonweb.scaner_work_config.deviceIndex = deviceSelect.selectedIndex;\r\n  scanonweb.scaner_work_config.colorMode = colorMode.value;\r\n  scanonweb.scaner_work_config.dpi_x = 300;\r\n  scanonweb.scaner_work_config.dpi_y = 300;\r\n\r\n  // 开始扫描\r\n  scanonweb.startScan();\r\n};\r\n\r\n// 清空所有扫描结果\r\nconst clearAll = () => {\r\n  if (scanonweb) {\r\n    scanonweb.clearAll();\r\n    document.getElementById(\"simple-imageList\").innerHTML = \"\";\r\n  }\r\n};\r\n\r\n// 保存为PDF\r\nconst saveAsPdf = () => {\r\n  if (!scanonweb) return;\r\n\r\n  const fileName = prompt(\"请输入保存文件路径\", \"d:/scan_result.pdf\");\r\n  if (fileName) {\r\n    scanonweb.saveAllImageToLocal(fileName);\r\n    alert(\"文件已保存到: \" + fileName);\r\n  }\r\n};\r\n\r\n// 初始化扫描控件和事件\r\nconst initScanonweb = () => {\r\n  // 检查是否已加载scanonweb.js\r\n  if (typeof ScanOnWeb === \"undefined\") {\r\n    console.error(\"ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件\");\r\n    return;\r\n  }\r\n\r\n  scanonweb = new ScanOnWeb();\r\n\r\n  // 响应返回扫描设备列表的回调函数\r\n  scanonweb.onGetDevicesListEvent = (msg) => {\r\n    const deviceListDom = document.getElementById(\"simple-devices\");\r\n\r\n    // 清空设备列表\r\n    deviceListDom.innerHTML = \"\";\r\n\r\n    // 添加设备信息\r\n    for (let i = 0; i < msg.devices.length; ++i) {\r\n      const opt = document.createElement(\"option\");\r\n      opt.innerHTML = msg.devices[i];\r\n      if (i === msg.currentIndex) {\r\n        opt.selected = true;\r\n      }\r\n      deviceListDom.appendChild(opt);\r\n    }\r\n  };\r\n\r\n  // 响应获取某一页图像的回调函数\r\n  scanonweb.onGetImageByIdEvent = (msg) => {\r\n    const imageListDom = document.getElementById(\"simple-imageList\");\r\n\r\n    // 创建图像容器\r\n    const containerDiv = document.createElement(\"div\");\r\n    containerDiv.className =\r\n      \"image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg\";\r\n\r\n    // 创建图像元素\r\n    const imageDom = document.createElement(\"img\");\r\n    imageDom.src = \"data:image/jpg;base64,\" + msg.imageBase64;\r\n    imageDom.className = \"w-full h-64 object-contain\";\r\n    imageDom.setAttribute(\"imageIndex\", msg.imageIndex);\r\n\r\n    // 创建图像信息覆盖层\r\n    const overlayDiv = document.createElement(\"div\");\r\n    overlayDiv.className =\r\n      \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm\";\r\n    overlayDiv.textContent = \"图像 #\" + (msg.imageIndex + 1);\r\n\r\n    // 将图像和覆盖层添加到容器\r\n    containerDiv.appendChild(imageDom);\r\n    containerDiv.appendChild(overlayDiv);\r\n\r\n    // 将容器添加到图像列表\r\n    imageListDom.appendChild(containerDiv);\r\n  };\r\n\r\n  // 响应获取所有图像的回调函数\r\n  scanonweb.onGetAllImageEvent = (msg) => {\r\n    const imageListDom = document.getElementById(\"simple-imageList\");\r\n    imageListDom.innerHTML = \"\";\r\n\r\n    if (msg.images.length === 0) {\r\n      return;\r\n    }\r\n\r\n    for (let i = 0; i < msg.images.length; i++) {\r\n      // 创建图像容器\r\n      const containerDiv = document.createElement(\"div\");\r\n      containerDiv.className =\r\n        \"image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg\";\r\n\r\n      // 创建图像元素\r\n      const imageDom = document.createElement(\"img\");\r\n      imageDom.src = \"data:image/jpg;base64,\" + msg.images[i];\r\n      imageDom.className = \"w-full h-64 object-contain\";\r\n      imageDom.setAttribute(\"imageIndex\", i);\r\n\r\n      // 创建图像信息覆盖层\r\n      const overlayDiv = document.createElement(\"div\");\r\n      overlayDiv.className =\r\n        \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm\";\r\n      overlayDiv.textContent = \"图像 #\" + (i + 1);\r\n\r\n      // 将图像和覆盖层添加到容器\r\n      containerDiv.appendChild(imageDom);\r\n      containerDiv.appendChild(overlayDiv);\r\n\r\n      // 将容器添加到图像列表\r\n      imageListDom.appendChild(containerDiv);\r\n    }\r\n  };\r\n};\r\n\r\n// 加载扫描控件脚本\r\nconst loadScanonwebScript = () => {\r\n  return new Promise((resolve, reject) => {\r\n    // 检查是否已加载\r\n    if (document.getElementById(\"scanonweb-script\")) {\r\n      resolve();\r\n      return;\r\n    }\r\n\r\n    const script = document.createElement(\"script\");\r\n    script.id = \"scanonweb-script\";\r\n    script.src = \"/scanonweb.js\";\r\n    script.type = \"text/javascript\";\r\n    script.async = true;\r\n    script.onload = resolve;\r\n    script.onerror = reject;\r\n    document.head.appendChild(script);\r\n  });\r\n};\r\n\r\nonMounted(async () => {\r\n  try {\r\n    await loadScanonwebScript();\r\n    initScanonweb();\r\n  } catch (err) {\r\n    console.error(\"加载扫描控件失败:\", err);\r\n    alert(\"加载扫描控件失败，请确保服务器上存在scanonweb.js文件。\");\r\n  }\r\n});\r\n\r\nonUnmounted(() => {\r\n  // 清理资源\r\n  scanonweb = null;\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.simple-demo {\r\n  font-family: \"Microsoft YaHei\", \"PingFang SC\", Arial, sans-serif;\r\n}\r\n\r\n.form-control {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.btn {\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.btn:hover {\r\n  transform: translateY(-2px);\r\n}\r\n</style>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAsFM,UAAA,YAAY,IAAI,KAAK;;;;;;;;;;;;;"}