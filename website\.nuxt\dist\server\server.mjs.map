{"version": 3, "file": "server.mjs", "sources": ["../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/fetch.mjs", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/nuxt.config.mjs", "../../../node_modules/nuxt/dist/app/nuxt.js", "../../../node_modules/nuxt/dist/app/components/injections.js", "../../../node_modules/nuxt/dist/app/composables/router.js", "../../../node_modules/nuxt/dist/app/composables/error.js", "../../../node_modules/@unhead/vue/dist/shared/vue.cf295fb1.mjs", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/unhead-plugins.mjs", "../../../node_modules/nuxt/dist/head/runtime/plugins/unhead.js", "../../../node_modules/unctx/dist/index.mjs", "../../../node_modules/nuxt/dist/pages/runtime/utils.js", "../../../node_modules/nuxt/dist/app/composables/manifest.js", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/routes.mjs", "../../../node_modules/nuxt/dist/app/components/utils.js", "../../../node_modules/nuxt/dist/pages/runtime/router.options.js", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/router.options.mjs", "../../../node_modules/nuxt/dist/pages/runtime/validate.js", "../../../node_modules/nuxt/dist/app/middleware/manifest-route-rule.js", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/middleware.mjs", "../../../node_modules/nuxt/dist/pages/runtime/plugins/router.js", "../../../node_modules/nuxt/dist/app/composables/payload.js", "../../../node_modules/nuxt/dist/app/plugins/revive-payload.server.js", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/components.plugin.mjs", "../../../virtual:nuxt:D:/peihexian/website/website/.nuxt/plugins/server.mjs", "../../../node_modules/nuxt/dist/app/components/route-provider.js", "../../../node_modules/nuxt/dist/pages/runtime/page.js", "../../../app.vue", "../../../node_modules/nuxt/dist/app/components/nuxt-error-page.vue", "../../../node_modules/nuxt/dist/app/components/nuxt-root.vue", "../../../node_modules/nuxt/dist/app/entry.js"], "sourcesContent": ["import { $fetch } from 'ofetch'\nimport { baseURL } from '#internal/nuxt/paths'\nif (!globalThis.$fetch) {\n  globalThis.$fetch = $fetch.create({\n    baseURL: baseURL()\n  })\n}", "export const appHead = {\"meta\":[{\"charset\":\"utf-8\"},{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},{\"name\":\"description\",\"content\":\"专业的Web控件解决方案提供商，提供ScanOnWeb扫描仪控件、ImageCapOnWeb摄像头控件、GaoPaiYi高拍仪控件。支持Windows和Linux平台，兼容信创国产化环境。\"},{\"name\":\"keywords\",\"content\":\"Web控件,扫描仪控件,摄像头控件,高拍仪控件,ScanOnWeb,ImageCapOnWeb,GaoPaiYi,信创,国产化,JavaScript,Vue,React,Angular\"},{\"name\":\"author\",\"content\":\"brainysoft.cn\"},{\"name\":\"robots\",\"content\":\"index, follow\"},{\"name\":\"googlebot\",\"content\":\"index, follow\"},{\"name\":\"bingbot\",\"content\":\"index, follow\"},{\"name\":\"baidu-site-verification\",\"content\":\"\"},{\"name\":\"google-site-verification\",\"content\":\"\"},{\"property\":\"og:type\",\"content\":\"website\"},{\"property\":\"og:url\",\"content\":\"https://brainysoft.cn/\"},{\"property\":\"og:title\",\"content\":\"brainysoft.cn - 专业Web控件解决方案\"},{\"property\":\"og:description\",\"content\":\"专业的Web控件解决方案提供商，提供扫描仪、摄像头、高拍仪控件，支持多平台，兼容信创国产化环境。\"},{\"property\":\"og:image\",\"content\":\"https://brainysoft.cn/images/og-image.jpg\"},{\"property\":\"twitter:card\",\"content\":\"summary_large_image\"},{\"property\":\"twitter:url\",\"content\":\"https://brainysoft.cn/\"},{\"property\":\"twitter:title\",\"content\":\"brainysoft.cn - 专业Web控件解决方案\"},{\"property\":\"twitter:description\",\"content\":\"专业的Web控件解决方案提供商，提供扫描仪、摄像头、高拍仪控件，支持多平台，兼容信创国产化环境。\"},{\"property\":\"twitter:image\",\"content\":\"https://brainysoft.cn/images/og-image.jpg\"}],\"link\":[{\"rel\":\"icon\",\"type\":\"image/x-icon\",\"href\":\"/favicon.ico\"},{\"rel\":\"canonical\",\"href\":\"https://brainysoft.cn/\"},{\"rel\":\"sitemap\",\"type\":\"application/xml\",\"href\":\"/sitemap.xml\"}],\"style\":[],\"script\":[],\"noscript\":[],\"title\":\"brainysoft.cn - 专业Web控件解决方案\"}\n\nexport const appBaseURL = \"/\"\n\nexport const appBuildAssetsDir = \"/_nuxt/\"\n\nexport const appCdnURL = \"\"\n\nexport const appLayoutTransition = false\n\nexport const appPageTransition = false\n\nexport const appViewTransition = false\n\nexport const appKeepalive = false\n\nexport const appRootId = \"__nuxt\"\n\nexport const appRootTag = \"div\"\n\nexport const appRootAttrs = {\"id\":\"__nuxt\"}\n\nexport const appTeleportTag = \"div\"\n\nexport const appTeleportId = \"teleports\"\n\nexport const appTeleportAttrs = {\"id\":\"teleports\"}\n\nexport const renderJsonPayloads = true\n\nexport const componentIslands = false\n\nexport const payloadExtraction = true\n\nexport const cookieStore = true\n\nexport const appManifest = true\n\nexport const remoteComponentIslands = false\n\nexport const selectiveClient = false\n\nexport const devPagesDir = null\n\nexport const devRootDir = null\n\nexport const devLogs = false\n\nexport const nuxtLinkDefaults = {\"componentName\":\"NuxtLink\"}\n\nexport const asyncDataDefaults = {\"value\":null,\"errorValue\":null,\"deep\":true}\n\nexport const resetAsyncDataToUndefined = true\n\nexport const nuxtDefaultErrorValue = null\n\nexport const fetchDefaults = {}\n\nexport const vueAppRootContainer = '#__nuxt'\n\nexport const viewTransition = false\n\nexport const appId = \"nuxt-app\"\n\nexport const outdatedBuildInterval = 3600000", "import { effectScope, getCurrentInstance, getCurrentScope, hasInjectionContext, reactive, shallowReactive } from \"vue\";\nimport { createHooks } from \"hookable\";\nimport { getContext } from \"unctx\";\nimport { appId } from \"#build/nuxt.config.mjs\";\nfunction getNuxtAppCtx(appName = appId || \"nuxt-app\") {\n  return getContext(appName, {\n    asyncContext: !!__NUXT_ASYNC_CONTEXT__ && import.meta.server\n  });\n}\nexport const NuxtPluginIndicator = \"__nuxt_plugin\";\nexport function createNuxtApp(options) {\n  let hydratingCount = 0;\n  const nuxtApp = {\n    _name: appId || \"nuxt-app\",\n    _scope: effectScope(),\n    provide: void 0,\n    globalName: \"nuxt\",\n    versions: {\n      get nuxt() {\n        return __NUXT_VERSION__;\n      },\n      get vue() {\n        return nuxtApp.vueApp.version;\n      }\n    },\n    payload: shallowReactive({\n      data: shallowReactive({}),\n      state: reactive({}),\n      once: /* @__PURE__ */ new Set(),\n      _errors: shallowReactive({})\n    }),\n    static: {\n      data: {}\n    },\n    runWithContext(fn) {\n      if (nuxtApp._scope.active && !getCurrentScope()) {\n        return nuxtApp._scope.run(() => callWithNuxt(nuxtApp, fn));\n      }\n      return callWithNuxt(nuxtApp, fn);\n    },\n    isHydrating: import.meta.client,\n    deferHydration() {\n      if (!nuxtApp.isHydrating) {\n        return () => {\n        };\n      }\n      hydratingCount++;\n      let called = false;\n      return () => {\n        if (called) {\n          return;\n        }\n        called = true;\n        hydratingCount--;\n        if (hydratingCount === 0) {\n          nuxtApp.isHydrating = false;\n          return nuxtApp.callHook(\"app:suspense:resolve\");\n        }\n      };\n    },\n    _asyncDataPromises: {},\n    _asyncData: shallowReactive({}),\n    _payloadRevivers: {},\n    ...options\n  };\n  if (import.meta.server) {\n    nuxtApp.payload.serverRendered = true;\n  }\n  if (import.meta.client && window.__NUXT__) {\n    for (const key in window.__NUXT__) {\n      switch (key) {\n        case \"data\":\n        case \"state\":\n        case \"_errors\":\n          Object.assign(nuxtApp.payload[key], window.__NUXT__[key]);\n          break;\n        default:\n          nuxtApp.payload[key] = window.__NUXT__[key];\n      }\n    }\n  }\n  nuxtApp.hooks = createHooks();\n  nuxtApp.hook = nuxtApp.hooks.hook;\n  if (import.meta.server) {\n    const contextCaller = async function(hooks, args) {\n      for (const hook of hooks) {\n        await nuxtApp.runWithContext(() => hook(...args));\n      }\n    };\n    nuxtApp.hooks.callHook = (name, ...args) => nuxtApp.hooks.callHookWith(contextCaller, name, ...args);\n  }\n  nuxtApp.callHook = nuxtApp.hooks.callHook;\n  nuxtApp.provide = (name, value) => {\n    const $name = \"$\" + name;\n    defineGetter(nuxtApp, $name, value);\n    defineGetter(nuxtApp.vueApp.config.globalProperties, $name, value);\n  };\n  defineGetter(nuxtApp.vueApp, \"$nuxt\", nuxtApp);\n  defineGetter(nuxtApp.vueApp.config.globalProperties, \"$nuxt\", nuxtApp);\n  if (import.meta.server) {\n    if (nuxtApp.ssrContext) {\n      nuxtApp.ssrContext.nuxt = nuxtApp;\n      nuxtApp.ssrContext._payloadReducers = {};\n      nuxtApp.payload.path = nuxtApp.ssrContext.url;\n    }\n    nuxtApp.ssrContext = nuxtApp.ssrContext || {};\n    if (nuxtApp.ssrContext.payload) {\n      Object.assign(nuxtApp.payload, nuxtApp.ssrContext.payload);\n    }\n    nuxtApp.ssrContext.payload = nuxtApp.payload;\n    nuxtApp.ssrContext.config = {\n      public: options.ssrContext.runtimeConfig.public,\n      app: options.ssrContext.runtimeConfig.app\n    };\n  }\n  if (import.meta.client) {\n    window.addEventListener(\"nuxt.preloadError\", (event) => {\n      nuxtApp.callHook(\"app:chunkError\", { error: event.payload });\n    });\n    window.useNuxtApp = window.useNuxtApp || useNuxtApp;\n    const unreg = nuxtApp.hook(\"app:error\", (...args) => {\n      console.error(\"[nuxt] error caught during app initialization\", ...args);\n    });\n    nuxtApp.hook(\"app:mounted\", unreg);\n  }\n  const runtimeConfig = import.meta.server ? options.ssrContext.runtimeConfig : nuxtApp.payload.config;\n  nuxtApp.provide(\"config\", import.meta.client && import.meta.dev ? wrappedConfig(runtimeConfig) : runtimeConfig);\n  return nuxtApp;\n}\nexport function registerPluginHooks(nuxtApp, plugin) {\n  if (plugin.hooks) {\n    nuxtApp.hooks.addHooks(plugin.hooks);\n  }\n}\nexport async function applyPlugin(nuxtApp, plugin) {\n  if (typeof plugin === \"function\") {\n    const { provide } = await nuxtApp.runWithContext(() => plugin(nuxtApp)) || {};\n    if (provide && typeof provide === \"object\") {\n      for (const key in provide) {\n        nuxtApp.provide(key, provide[key]);\n      }\n    }\n  }\n}\nexport async function applyPlugins(nuxtApp, plugins) {\n  const resolvedPlugins = [];\n  const unresolvedPlugins = [];\n  const parallels = [];\n  const errors = [];\n  let promiseDepth = 0;\n  async function executePlugin(plugin) {\n    const unresolvedPluginsForThisPlugin = plugin.dependsOn?.filter((name) => plugins.some((p) => p._name === name) && !resolvedPlugins.includes(name)) ?? [];\n    if (unresolvedPluginsForThisPlugin.length > 0) {\n      unresolvedPlugins.push([new Set(unresolvedPluginsForThisPlugin), plugin]);\n    } else {\n      const promise = applyPlugin(nuxtApp, plugin).then(async () => {\n        if (plugin._name) {\n          resolvedPlugins.push(plugin._name);\n          await Promise.all(unresolvedPlugins.map(async ([dependsOn, unexecutedPlugin]) => {\n            if (dependsOn.has(plugin._name)) {\n              dependsOn.delete(plugin._name);\n              if (dependsOn.size === 0) {\n                promiseDepth++;\n                await executePlugin(unexecutedPlugin);\n              }\n            }\n          }));\n        }\n      });\n      if (plugin.parallel) {\n        parallels.push(promise.catch((e) => errors.push(e)));\n      } else {\n        await promise;\n      }\n    }\n  }\n  for (const plugin of plugins) {\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext && plugin.env?.islands === false) {\n      continue;\n    }\n    registerPluginHooks(nuxtApp, plugin);\n  }\n  for (const plugin of plugins) {\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext && plugin.env?.islands === false) {\n      continue;\n    }\n    await executePlugin(plugin);\n  }\n  await Promise.all(parallels);\n  if (promiseDepth) {\n    for (let i = 0; i < promiseDepth; i++) {\n      await Promise.all(parallels);\n    }\n  }\n  if (errors.length) {\n    throw errors[0];\n  }\n}\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtPlugin(plugin) {\n  if (typeof plugin === \"function\") {\n    return plugin;\n  }\n  const _name = plugin._name || plugin.name;\n  delete plugin.name;\n  return Object.assign(plugin.setup || (() => {\n  }), plugin, { [NuxtPluginIndicator]: true, _name });\n}\nexport const definePayloadPlugin = defineNuxtPlugin;\nexport function isNuxtPlugin(plugin) {\n  return typeof plugin === \"function\" && NuxtPluginIndicator in plugin;\n}\nexport function callWithNuxt(nuxt, setup, args) {\n  const fn = () => args ? setup(...args) : setup();\n  const nuxtAppCtx = getNuxtAppCtx(nuxt._name);\n  if (import.meta.server) {\n    return nuxt.vueApp.runWithContext(() => nuxtAppCtx.callAsync(nuxt, fn));\n  } else {\n    nuxtAppCtx.set(nuxt);\n    return nuxt.vueApp.runWithContext(fn);\n  }\n}\nexport function tryUseNuxtApp(appName) {\n  let nuxtAppInstance;\n  if (hasInjectionContext()) {\n    nuxtAppInstance = getCurrentInstance()?.appContext.app.$nuxt;\n  }\n  nuxtAppInstance = nuxtAppInstance || getNuxtAppCtx(appName).tryUse();\n  return nuxtAppInstance || null;\n}\nexport function useNuxtApp(appName) {\n  const nuxtAppInstance = tryUseNuxtApp(appName);\n  if (!nuxtAppInstance) {\n    if (import.meta.dev) {\n      throw new Error(\"[nuxt] A composable that requires access to the Nuxt instance was called outside of a plugin, Nuxt hook, Nuxt middleware, or Vue setup function. This is probably not a Nuxt bug. Find out more at `https://nuxt.com/docs/guide/concepts/auto-imports#vue-and-nuxt-composables`.\");\n    } else {\n      throw new Error(\"[nuxt] instance unavailable\");\n    }\n  }\n  return nuxtAppInstance;\n}\n// @__NO_SIDE_EFFECTS__\nexport function useRuntimeConfig(_event) {\n  return useNuxtApp().$config;\n}\nfunction defineGetter(obj, key, val) {\n  Object.defineProperty(obj, key, { get: () => val });\n}\nexport function defineAppConfig(config) {\n  return config;\n}\nconst loggedKeys = /* @__PURE__ */ new Set();\nfunction wrappedConfig(runtimeConfig) {\n  if (!import.meta.dev || import.meta.server) {\n    return runtimeConfig;\n  }\n  const keys = Object.keys(runtimeConfig).map((key) => `\\`${key}\\``);\n  const lastKey = keys.pop();\n  return new Proxy(runtimeConfig, {\n    get(target, p, receiver) {\n      if (typeof p === \"string\" && p !== \"public\" && !(p in target) && !p.startsWith(\"__v\")) {\n        if (!loggedKeys.has(p)) {\n          loggedKeys.add(p);\n          console.warn(`[nuxt] Could not access \\`${p}\\`. The only available runtime config keys on the client side are ${keys.join(\", \")} and ${lastKey}. See https://nuxt.com/docs/guide/going-further/runtime-config for more information.`);\n        }\n      }\n      return Reflect.get(target, p, receiver);\n    }\n  });\n}\n", "export const LayoutMetaSymbol = Symbol(\"layout-meta\");\nexport const PageRouteSymbol = Symbol(\"route\");\n", "import { getCurrentInstance, hasInjectionContext, inject, onScopeDispose } from \"vue\";\nimport { sanitizeStatusCode } from \"h3\";\nimport { hasProtocol, isScriptProtocol, joinURL, withQuery } from \"ufo\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { PageRouteSymbol } from \"../components/injections.js\";\nimport { createError, showError } from \"./error.js\";\nexport const useRouter = () => {\n  return useNuxtApp()?.$router;\n};\nexport const useRoute = () => {\n  if (import.meta.dev && isProcessingMiddleware()) {\n    console.warn(\"[nuxt] Calling `useRoute` within middleware may lead to misleading results. Instead, use the (to, from) arguments passed to the middleware to access the new and old routes.\");\n  }\n  if (hasInjectionContext()) {\n    return inject(PageRouteSymbol, useNuxtApp()._route);\n  }\n  return useNuxtApp()._route;\n};\nexport const onBeforeRouteLeave = (guard) => {\n  const unsubscribe = useRouter().beforeEach((to, from, next) => {\n    if (to === from) {\n      return;\n    }\n    return guard(to, from, next);\n  });\n  onScopeDispose(unsubscribe);\n};\nexport const onBeforeRouteUpdate = (guard) => {\n  const unsubscribe = useRouter().beforeEach(guard);\n  onScopeDispose(unsubscribe);\n};\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtRouteMiddleware(middleware) {\n  return middleware;\n}\nexport const addRouteMiddleware = (name, middleware, options = {}) => {\n  const nuxtApp = useNuxtApp();\n  const global = options.global || typeof name !== \"string\";\n  const mw = typeof name !== \"string\" ? name : middleware;\n  if (!mw) {\n    console.warn(\"[nuxt] No route middleware passed to `addRouteMiddleware`.\", name);\n    return;\n  }\n  if (global) {\n    nuxtApp._middleware.global.push(mw);\n  } else {\n    nuxtApp._middleware.named[name] = mw;\n  }\n};\nconst isProcessingMiddleware = () => {\n  try {\n    if (useNuxtApp()._processingMiddleware) {\n      return true;\n    }\n  } catch {\n    return false;\n  }\n  return false;\n};\nexport const navigateTo = (to, options) => {\n  if (!to) {\n    to = \"/\";\n  }\n  const toPath = typeof to === \"string\" ? to : \"path\" in to ? resolveRouteObject(to) : useRouter().resolve(to).href;\n  if (import.meta.client && options?.open) {\n    const { target = \"_blank\", windowFeatures = {} } = options.open;\n    const features = Object.entries(windowFeatures).filter(([_, value]) => value !== void 0).map(([feature, value]) => `${feature.toLowerCase()}=${value}`).join(\", \");\n    open(toPath, target, features);\n    return Promise.resolve();\n  }\n  const isExternalHost = hasProtocol(toPath, { acceptRelative: true });\n  const isExternal = options?.external || isExternalHost;\n  if (isExternal) {\n    if (!options?.external) {\n      throw new Error(\"Navigating to an external URL is not allowed by default. Use `navigateTo(url, { external: true })`.\");\n    }\n    const { protocol } = new URL(toPath, import.meta.client ? window.location.href : \"http://localhost\");\n    if (protocol && isScriptProtocol(protocol)) {\n      throw new Error(`Cannot navigate to a URL with '${protocol}' protocol.`);\n    }\n  }\n  const inMiddleware = isProcessingMiddleware();\n  if (import.meta.client && !isExternal && inMiddleware) {\n    return to;\n  }\n  const router = useRouter();\n  const nuxtApp = useNuxtApp();\n  if (import.meta.server) {\n    if (nuxtApp.ssrContext) {\n      const fullPath = typeof to === \"string\" || isExternal ? toPath : router.resolve(to).fullPath || \"/\";\n      const location2 = isExternal ? toPath : joinURL(useRuntimeConfig().app.baseURL, fullPath);\n      const redirect = async function(response) {\n        await nuxtApp.callHook(\"app:redirected\");\n        const encodedLoc = location2.replace(/\"/g, \"%22\");\n        const encodedHeader = encodeURL(location2, isExternalHost);\n        nuxtApp.ssrContext._renderResponse = {\n          statusCode: sanitizeStatusCode(options?.redirectCode || 302, 302),\n          body: `<!DOCTYPE html><html><head><meta http-equiv=\"refresh\" content=\"0; url=${encodedLoc}\"></head></html>`,\n          headers: { location: encodedHeader }\n        };\n        return response;\n      };\n      if (!isExternal && inMiddleware) {\n        router.afterEach((final) => final.fullPath === fullPath ? redirect(false) : void 0);\n        return to;\n      }\n      return redirect(!inMiddleware ? void 0 : (\n        /* abort route navigation */\n        false\n      ));\n    }\n  }\n  if (isExternal) {\n    nuxtApp._scope.stop();\n    if (options?.replace) {\n      location.replace(toPath);\n    } else {\n      location.href = toPath;\n    }\n    if (inMiddleware) {\n      if (!nuxtApp.isHydrating) {\n        return false;\n      }\n      return new Promise(() => {\n      });\n    }\n    return Promise.resolve();\n  }\n  return options?.replace ? router.replace(to) : router.push(to);\n};\nexport const abortNavigation = (err) => {\n  if (import.meta.dev && !isProcessingMiddleware()) {\n    throw new Error(\"abortNavigation() is only usable inside a route middleware handler.\");\n  }\n  if (!err) {\n    return false;\n  }\n  err = createError(err);\n  if (err.fatal) {\n    useNuxtApp().runWithContext(() => showError(err));\n  }\n  throw err;\n};\nexport const setPageLayout = (layout) => {\n  const nuxtApp = useNuxtApp();\n  if (import.meta.server) {\n    if (import.meta.dev && getCurrentInstance() && nuxtApp.payload.state._layout !== layout) {\n      console.warn(\"[warn] [nuxt] `setPageLayout` should not be called to change the layout on the server within a component as this will cause hydration errors.\");\n    }\n    nuxtApp.payload.state._layout = layout;\n  }\n  if (import.meta.dev && nuxtApp.isHydrating && nuxtApp.payload.serverRendered && nuxtApp.payload.state._layout !== layout) {\n    console.warn(\"[warn] [nuxt] `setPageLayout` should not be called to change the layout during hydration as this will cause hydration errors.\");\n  }\n  const inMiddleware = isProcessingMiddleware();\n  if (inMiddleware || import.meta.server || nuxtApp.isHydrating) {\n    const unsubscribe = useRouter().beforeResolve((to) => {\n      to.meta.layout = layout;\n      unsubscribe();\n    });\n  }\n  if (!inMiddleware) {\n    useRoute().meta.layout = layout;\n  }\n};\nexport function resolveRouteObject(to) {\n  return withQuery(to.path || \"\", to.query || {}) + (to.hash || \"\");\n}\nexport function encodeURL(location2, isExternalHost = false) {\n  const url = new URL(location2, \"http://localhost\");\n  if (!isExternalHost) {\n    return url.pathname + url.search + url.hash;\n  }\n  if (location2.startsWith(\"//\")) {\n    return url.toString().replace(url.protocol, \"\");\n  }\n  return url.toString();\n}\n", "import { createError as createH3Error } from \"h3\";\nimport { toRef } from \"vue\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { useRouter } from \"./router.js\";\nimport { nuxtDefaultErrorValue } from \"#build/nuxt.config.mjs\";\nexport const NUXT_ERROR_SIGNATURE = \"__nuxt_error\";\nexport const useError = () => toRef(useNuxtApp().payload, \"error\");\nexport const showError = (error) => {\n  const nuxtError = createError(error);\n  try {\n    const nuxtApp = useNuxtApp();\n    const error2 = useError();\n    if (import.meta.client) {\n      nuxtApp.hooks.callHook(\"app:error\", nuxtError);\n    }\n    error2.value = error2.value || nuxtError;\n  } catch {\n    throw nuxtError;\n  }\n  return nuxtError;\n};\nexport const clearError = async (options = {}) => {\n  const nuxtApp = useNuxtApp();\n  const error = useError();\n  nuxtApp.callHook(\"app:error:cleared\", options);\n  if (options.redirect) {\n    await useRouter().replace(options.redirect);\n  }\n  error.value = nuxtDefaultErrorValue;\n};\nexport const isNuxtError = (error) => !!error && typeof error === \"object\" && NUXT_ERROR_SIGNATURE in error;\nexport const createError = (error) => {\n  const nuxtError = createH3Error(error);\n  Object.defineProperty(nuxtError, NUXT_ERROR_SIGNATURE, {\n    value: true,\n    configurable: false,\n    writable: false\n  });\n  return nuxtError;\n};\n", "import { version, unref, nextTick, inject } from 'vue';\nimport { createServerHead as createServerHead$1, createHead as createHead$1, getActiveHead } from 'unhead';\nimport { defineHeadPlugin } from '@unhead/shared';\n\nconst Vue3 = version.startsWith(\"3\");\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\nfunction resolveUnrefHeadInput(ref, lastKey = \"\") {\n  if (ref instanceof Promise)\n    return ref;\n  const root = resolveUnref(ref);\n  if (!ref || !root)\n    return root;\n  if (Array.isArray(root))\n    return root.map((r) => resolveUnrefHeadInput(r, lastKey));\n  if (typeof root === \"object\") {\n    return Object.fromEntries(\n      Object.entries(root).map(([k, v]) => {\n        if (k === \"titleTemplate\" || k.startsWith(\"on\"))\n          return [k, unref(v)];\n        return [k, resolveUnrefHeadInput(v, k)];\n      })\n    );\n  }\n  return root;\n}\n\nconst VueReactivityPlugin = defineHeadPlugin({\n  hooks: {\n    \"entries:resolve\": function(ctx) {\n      for (const entry of ctx.entries)\n        entry.resolvedInput = resolveUnrefHeadInput(entry.input);\n    }\n  }\n});\n\nconst headSymbol = \"usehead\";\nfunction vueInstall(head) {\n  const plugin = {\n    install(app) {\n      if (Vue3) {\n        app.config.globalProperties.$unhead = head;\n        app.config.globalProperties.$head = head;\n        app.provide(headSymbol, head);\n      }\n    }\n  };\n  return plugin.install;\n}\nfunction createServerHead(options = {}) {\n  const head = createServerHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\nfunction createHead(options = {}) {\n  options.domDelayFn = options.domDelayFn || ((fn) => nextTick(() => setTimeout(() => fn(), 0)));\n  const head = createHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__unhead_injection_handler__\";\nfunction setHeadInjectionHandler(handler) {\n  _global[globalKey] = handler;\n}\nfunction injectHead() {\n  if (globalKey in _global) {\n    return _global[globalKey]();\n  }\n  const head = inject(headSymbol);\n  if (!head && process.env.NODE_ENV !== \"production\")\n    console.warn(\"Unhead is missing Vue context, falling back to shared context. This may have unexpected results.\");\n  return head || getActiveHead();\n}\n\nexport { Vue3 as V, createServerHead as a, createHead as c, headSymbol as h, injectHead as i, resolveUnrefHeadInput as r, setHeadInjectionHandler as s };\n", "import { CapoPlugin } from \"D:/peihexian/website/website/node_modules/@unhead/vue/dist/index.mjs\";\nexport default import.meta.server ? [CapoPlugin({ track: true })] : [];", "import { createHead as createClientHead, setHeadIn<PERSON><PERSON><PERSON><PERSON> } from \"@unhead/vue\";\nimport { renderDOMHead } from \"@unhead/dom\";\nimport { defineNuxtPlugin, useNuxtApp } from \"#app/nuxt\";\nimport unheadPlugins from \"#build/unhead-plugins.mjs\";\nexport default defineNuxtPlugin({\n  name: \"nuxt:head\",\n  enforce: \"pre\",\n  setup(nuxtApp) {\n    const head = import.meta.server ? nuxtApp.ssrContext.head : createClientHead({\n      plugins: unheadPlugins\n    });\n    setHeadInjectionHandler(\n      // need a fresh instance of the nuxt app to avoid parallel requests interfering with each other\n      () => useNuxtApp().vueApp._context.provides.usehead\n    );\n    nuxtApp.vueApp.use(head);\n    if (import.meta.client) {\n      let pauseDOMUpdates = true;\n      const syncHead = async () => {\n        pauseDOMUpdates = false;\n        await renderDOMHead(head);\n      };\n      head.hooks.hook(\"dom:beforeRender\", (context) => {\n        context.shouldRender = !pauseDOMUpdates;\n      });\n      nuxtApp.hooks.hook(\"page:start\", () => {\n        pauseDOMUpdates = true;\n      });\n      nuxtApp.hooks.hook(\"page:finish\", () => {\n        if (!nuxtApp.isHydrating) {\n          syncHead();\n        }\n      });\n      nuxtApp.hooks.hook(\"app:error\", syncHead);\n      nuxtApp.hooks.hook(\"app:suspense:resolve\", syncHead);\n    }\n  }\n});\n", "function createContext(opts = {}) {\n  let currentInstance;\n  let isSingleton = false;\n  const checkConflict = (instance) => {\n    if (currentInstance && currentInstance !== instance) {\n      throw new Error(\"Context conflict\");\n    }\n  };\n  let als;\n  if (opts.asyncContext) {\n    const _AsyncLocalStorage = opts.AsyncLocalStorage || globalThis.AsyncLocalStorage;\n    if (_AsyncLocalStorage) {\n      als = new _AsyncLocalStorage();\n    } else {\n      console.warn(\"[unctx] `AsyncLocalStorage` is not provided.\");\n    }\n  }\n  const _getCurrentInstance = () => {\n    if (als && currentInstance === void 0) {\n      const instance = als.getStore();\n      if (instance !== void 0) {\n        return instance;\n      }\n    }\n    return currentInstance;\n  };\n  return {\n    use: () => {\n      const _instance = _getCurrentInstance();\n      if (_instance === void 0) {\n        throw new Error(\"Context is not available\");\n      }\n      return _instance;\n    },\n    tryUse: () => {\n      return _getCurrentInstance();\n    },\n    set: (instance, replace) => {\n      if (!replace) {\n        checkConflict(instance);\n      }\n      currentInstance = instance;\n      isSingleton = true;\n    },\n    unset: () => {\n      currentInstance = void 0;\n      isSingleton = false;\n    },\n    call: (instance, callback) => {\n      checkConflict(instance);\n      currentInstance = instance;\n      try {\n        return als ? als.run(instance, callback) : callback();\n      } finally {\n        if (!isSingleton) {\n          currentInstance = void 0;\n        }\n      }\n    },\n    async callAsync(instance, callback) {\n      currentInstance = instance;\n      const onRestore = () => {\n        currentInstance = instance;\n      };\n      const onLeave = () => currentInstance === instance ? onRestore : void 0;\n      asyncHandlers.add(onLeave);\n      try {\n        const r = als ? als.run(instance, callback) : callback();\n        if (!isSingleton) {\n          currentInstance = void 0;\n        }\n        return await r;\n      } finally {\n        asyncHandlers.delete(onLeave);\n      }\n    }\n  };\n}\nfunction createNamespace(defaultOpts = {}) {\n  const contexts = {};\n  return {\n    get(key, opts = {}) {\n      if (!contexts[key]) {\n        contexts[key] = createContext({ ...defaultOpts, ...opts });\n      }\n      contexts[key];\n      return contexts[key];\n    }\n  };\n}\nconst _globalThis = typeof globalThis !== \"undefined\" ? globalThis : typeof self !== \"undefined\" ? self : typeof global !== \"undefined\" ? global : typeof window !== \"undefined\" ? window : {};\nconst globalKey = \"__unctx__\";\nconst defaultNamespace = _globalThis[globalKey] || (_globalThis[globalKey] = createNamespace());\nconst getContext = (key, opts = {}) => defaultNamespace.get(key, opts);\nconst useContext = (key, opts = {}) => getContext(key, opts).use;\nconst asyncHandlersKey = \"__unctx_async_handlers__\";\nconst asyncHandlers = _globalThis[asyncHandlersKey] || (_globalThis[asyncHandlersKey] = /* @__PURE__ */ new Set());\nfunction executeAsync(function_) {\n  const restores = [];\n  for (const leaveHandler of asyncHandlers) {\n    const restore2 = leaveHandler();\n    if (restore2) {\n      restores.push(restore2);\n    }\n  }\n  const restore = () => {\n    for (const restore2 of restores) {\n      restore2();\n    }\n  };\n  let awaitable = function_();\n  if (awaitable && typeof awaitable === \"object\" && \"catch\" in awaitable) {\n    awaitable = awaitable.catch((error) => {\n      restore();\n      throw error;\n    });\n  }\n  return [awaitable, restore];\n}\nfunction withAsyncContext(function_, transformed) {\n  if (!transformed) {\n    console.warn(\n      \"[unctx] `withAsyncContext` needs transformation for async context support in\",\n      function_,\n      \"\\n\",\n      function_.toString()\n    );\n  }\n  return function_;\n}\n\nexport { createContext, createNamespace, defaultNamespace, executeAsync, getContext, useContext, withAsyncContext };\n", "import { KeepAlive, h } from \"vue\";\nconst interpolatePath = (route, match) => {\n  return match.path.replace(/(:\\w+)\\([^)]+\\)/g, \"$1\").replace(/(:\\w+)[?+*]/g, \"$1\").replace(/:\\w+/g, (r) => route.params[r.slice(1)]?.toString() || \"\");\n};\nexport const generateRouteKey = (routeProps, override) => {\n  const matchedRoute = routeProps.route.matched.find((m) => m.components?.default === routeProps.Component.type);\n  const source = override ?? matchedRoute?.meta.key ?? (matchedRoute && interpolatePath(routeProps.route, matchedRoute));\n  return typeof source === \"function\" ? source(routeProps.route) : source;\n};\nexport const wrapInKeepAlive = (props, children) => {\n  return { default: () => import.meta.client && props ? h(KeepAlive, props === true ? {} : props, children) : children };\n};\nexport function toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n", "import { createMatcherFromExport, createRouter as createRadixRouter, toRouteMatcher } from \"radix3\";\nimport { defu } from \"defu\";\nimport { useRuntimeConfig } from \"../nuxt.js\";\nimport { appManifest as isAppManifestEnabled } from \"#build/nuxt.config.mjs\";\nimport { buildAssetsURL } from \"#internal/nuxt/paths\";\nlet manifest;\nlet matcher;\nfunction fetchManifest() {\n  if (!isAppManifestEnabled) {\n    throw new Error(\"[nuxt] app manifest should be enabled with `experimental.appManifest`\");\n  }\n  manifest = $fetch(buildAssetsURL(`builds/meta/${useRuntimeConfig().app.buildId}.json`), {\n    responseType: \"json\"\n  });\n  manifest.then((m) => {\n    matcher = createMatcherFromExport(m.matcher);\n  }).catch((e) => {\n    console.error(\"[nuxt] Error fetching app manifest.\", e);\n  });\n  return manifest;\n}\nexport function getAppManifest() {\n  if (!isAppManifestEnabled) {\n    throw new Error(\"[nuxt] app manifest should be enabled with `experimental.appManifest`\");\n  }\n  return manifest || fetchManifest();\n}\nexport async function getRouteRules(url) {\n  if (import.meta.server) {\n    const _routeRulesMatcher = toRouteMatcher(\n      createRadixRouter({ routes: useRuntimeConfig().nitro.routeRules })\n    );\n    return defu({}, ..._routeRulesMatcher.matchAll(url).reverse());\n  }\n  await getAppManifest();\n  if (!matcher) {\n    console.error(\"[nuxt] Error creating app manifest matcher.\", matcher);\n    return {};\n  }\n  try {\n    return defu({}, ...matcher.matchAll(url).reverse());\n  } catch (e) {\n    console.error(\"[nuxt] Error matching route rules.\", e);\n    return {};\n  }\n}\n", "import { default as contacttRlcXbDquVMeta } from \"D:/peihexian/website/website/pages/contact.vue?macro=true\";\nimport { default as demonoQicmU9wqMeta } from \"D:/peihexian/website/website/pages/demo.vue?macro=true\";\nimport { default as scanonweb_45apiz73j2zcUZRMeta } from \"D:/peihexian/website/website/pages/docs/scanonweb-api.vue?macro=true\";\nimport { default as scanonweb_45faq1TmBiYvPYwMeta } from \"D:/peihexian/website/website/pages/docs/scanonweb-faq.vue?macro=true\";\nimport { default as scanonweb_45featuresA2GOEnVzGwMeta } from \"D:/peihexian/website/website/pages/docs/scanonweb-features.vue?macro=true\";\nimport { default as scanonweb_45getting_45startedCHS67RF9vCMeta } from \"D:/peihexian/website/website/pages/docs/scanonweb-getting-started.vue?macro=true\";\nimport { default as documentsU1dVGL4q92Meta } from \"D:/peihexian/website/website/pages/documents.vue?macro=true\";\nimport { default as downloadgLNIsEzVeZMeta } from \"D:/peihexian/website/website/pages/download.vue?macro=true\";\nimport { default as indexALvIzfM0V7Meta } from \"D:/peihexian/website/website/pages/index.vue?macro=true\";\nimport { default as productsTzdqXCtMWeMeta } from \"D:/peihexian/website/website/pages/products.vue?macro=true\";\nimport { default as purchaseyDf8raDMt3Meta } from \"D:/peihexian/website/website/pages/purchase.vue?macro=true\";\nexport default [\n  {\n    name: \"contact\",\n    path: \"/contact\",\n    component: () => import(\"D:/peihexian/website/website/pages/contact.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"demo\",\n    path: \"/demo\",\n    component: () => import(\"D:/peihexian/website/website/pages/demo.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"docs-scanonweb-api\",\n    path: \"/docs/scanonweb-api\",\n    component: () => import(\"D:/peihexian/website/website/pages/docs/scanonweb-api.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"docs-scanonweb-faq\",\n    path: \"/docs/scanonweb-faq\",\n    component: () => import(\"D:/peihexian/website/website/pages/docs/scanonweb-faq.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"docs-scanonweb-features\",\n    path: \"/docs/scanonweb-features\",\n    component: () => import(\"D:/peihexian/website/website/pages/docs/scanonweb-features.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"docs-scanonweb-getting-started\",\n    path: \"/docs/scanonweb-getting-started\",\n    component: () => import(\"D:/peihexian/website/website/pages/docs/scanonweb-getting-started.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"documents\",\n    path: \"/documents\",\n    component: () => import(\"D:/peihexian/website/website/pages/documents.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"download\",\n    path: \"/download\",\n    component: () => import(\"D:/peihexian/website/website/pages/download.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"index\",\n    path: \"/\",\n    component: () => import(\"D:/peihexian/website/website/pages/index.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"products\",\n    path: \"/products\",\n    component: () => import(\"D:/peihexian/website/website/pages/products.vue\").then(m => m.default || m)\n  },\n  {\n    name: \"purchase\",\n    path: \"/purchase\",\n    component: () => import(\"D:/peihexian/website/website/pages/purchase.vue\").then(m => m.default || m)\n  }\n]", "import { h } from \"vue\";\nimport { isString, isPromise, isArray, isObject } from \"@vue/shared\";\nimport { START_LOCATION } from \"#build/pages\";\nexport const _wrapIf = (component, props, slots) => {\n  props = props === true ? {} : props;\n  return { default: () => props ? h(component, props, slots) : slots.default?.() };\n};\nfunction generateRouteKey(route) {\n  const source = route?.meta.key ?? route.path.replace(/(:\\w+)\\([^)]+\\)/g, \"$1\").replace(/(:\\w+)[?+*]/g, \"$1\").replace(/:\\w+/g, (r) => route.params[r.slice(1)]?.toString() || \"\");\n  return typeof source === \"function\" ? source(route) : source;\n}\nexport function isChangingPage(to, from) {\n  if (to === from || from === START_LOCATION) {\n    return false;\n  }\n  if (generateRoute<PERSON>ey(to) !== generateRoute<PERSON>ey(from)) {\n    return true;\n  }\n  const areComponentsSame = to.matched.every(\n    (comp, index) => comp.components && comp.components.default === from.matched[index]?.components?.default\n  );\n  if (areComponentsSame) {\n    return false;\n  }\n  return true;\n}\nexport function createBuffer() {\n  let appendable = false;\n  const buffer = [];\n  return {\n    getBuffer() {\n      return buffer;\n    },\n    push(item) {\n      const isStringItem = isString(item);\n      if (appendable && isStringItem) {\n        buffer[buffer.length - 1] += item;\n      } else {\n        buffer.push(item);\n      }\n      appendable = isStringItem;\n      if (isPromise(item) || isArray(item) && item.hasAsync) {\n        buffer.hasAsync = true;\n      }\n    }\n  };\n}\nexport function vforToArray(source) {\n  if (isArray(source)) {\n    return source;\n  } else if (isString(source)) {\n    return source.split(\"\");\n  } else if (typeof source === \"number\") {\n    if (import.meta.dev && !Number.isInteger(source)) {\n      console.warn(`The v-for range expect an integer value but got ${source}.`);\n    }\n    const array = [];\n    for (let i = 0; i < source; i++) {\n      array[i] = i;\n    }\n    return array;\n  } else if (isObject(source)) {\n    if (source[Symbol.iterator]) {\n      return Array.from(\n        source,\n        (item) => item\n      );\n    } else {\n      const keys = Object.keys(source);\n      const array = new Array(keys.length);\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const key = keys[i];\n        array[i] = source[key];\n      }\n      return array;\n    }\n  }\n  return [];\n}\nexport function getFragmentHTML(element, withoutSlots = false) {\n  if (element) {\n    if (element.nodeName === \"#comment\" && element.nodeValue === \"[\") {\n      return getFragmentChildren(element, [], withoutSlots);\n    }\n    if (withoutSlots) {\n      const clone = element.cloneNode(true);\n      clone.querySelectorAll(\"[data-island-slot]\").forEach((n) => {\n        n.innerHTML = \"\";\n      });\n      return [clone.outerHTML];\n    }\n    return [element.outerHTML];\n  }\n  return null;\n}\nfunction getFragmentChildren(element, blocks = [], withoutSlots = false) {\n  if (element && element.nodeName) {\n    if (isEndFragment(element)) {\n      return blocks;\n    } else if (!isStartFragment(element)) {\n      const clone = element.cloneNode(true);\n      if (withoutSlots) {\n        clone.querySelectorAll(\"[data-island-slot]\").forEach((n) => {\n          n.innerHTML = \"\";\n        });\n      }\n      blocks.push(clone.outerHTML);\n    }\n    getFragmentChildren(element.nextSibling, blocks, withoutSlots);\n  }\n  return blocks;\n}\nfunction isStartFragment(element) {\n  return element.nodeName === \"#comment\" && element.nodeValue === \"[\";\n}\nfunction isEndFragment(element) {\n  return element.nodeName === \"#comment\" && element.nodeValue === \"]\";\n}\n", "import { useNuxtApp } from \"#app/nuxt\";\nimport { isChangingPage } from \"#app/components/utils\";\nimport { useRouter } from \"#app/composables/router\";\nimport { appPageTransition as defaultPageTransition } from \"#build/nuxt.config.mjs\";\nexport default {\n  scrollBehavior(to, from, savedPosition) {\n    const nuxtApp = useNuxtApp();\n    const behavior = useRouter().options?.scrollBehaviorType ?? \"auto\";\n    let position = savedPosition || void 0;\n    const routeAllowsScrollToTop = typeof to.meta.scrollToTop === \"function\" ? to.meta.scrollToTop(to, from) : to.meta.scrollToTop;\n    if (!position && from && to && routeAllowsScrollToTop !== false && isChangingPage(to, from)) {\n      position = { left: 0, top: 0 };\n    }\n    if (to.path === from.path) {\n      if (from.hash && !to.hash) {\n        return { left: 0, top: 0 };\n      }\n      if (to.hash) {\n        return { el: to.hash, top: _getHashElementScrollMarginTop(to.hash), behavior };\n      }\n      return false;\n    }\n    const hasTransition = (route) => !!(route.meta.pageTransition ?? defaultPageTransition);\n    const hookToWait = hasTransition(from) && hasTransition(to) ? \"page:transition:finish\" : \"page:finish\";\n    return new Promise((resolve) => {\n      nuxtApp.hooks.hookOnce(hookToWait, async () => {\n        await new Promise((resolve2) => setTimeout(resolve2, 0));\n        if (to.hash) {\n          position = { el: to.hash, top: _getHashElementScrollMarginTop(to.hash), behavior };\n        }\n        resolve(position);\n      });\n    });\n  }\n};\nfunction _getHashElementScrollMarginTop(selector) {\n  try {\n    const elem = document.querySelector(selector);\n    if (elem) {\n      return Number.parseFloat(getComputedStyle(elem).scrollMarginTop) + Number.parseFloat(getComputedStyle(document.documentElement).scrollPaddingTop);\n    }\n  } catch {\n  }\n  return 0;\n}\n", "import routerOptions0 from \"D:/peihexian/website/website/node_modules/nuxt/dist/pages/runtime/router.options\";\nconst configRouterOptions = {\n  hashMode: false,\n  scrollBehaviorType: \"auto\"\n}\nexport default {\n...configRouterOptions,\n...routerOptions0,\n}", "import { createError, showError } from \"#app/composables/error\";\nimport { useNuxtApp } from \"#app/nuxt\";\nimport { defineNuxtRouteMiddleware, useRouter } from \"#app/composables/router\";\nexport default defineNuxtRouteMiddleware(async (to) => {\n  if (!to.meta?.validate) {\n    return;\n  }\n  const nuxtApp = useNuxtApp();\n  const router = useRouter();\n  const result = await Promise.resolve(to.meta.validate(to));\n  if (result === true) {\n    return;\n  }\n  if (import.meta.server) {\n    return result;\n  }\n  const error = createError({\n    statusCode: 404,\n    statusMessage: `Page Not Found: ${to.fullPath}`,\n    data: {\n      path: to.fullPath\n    }\n  });\n  const unsub = router.beforeResolve((final) => {\n    unsub();\n    if (final === to) {\n      const unsub2 = router.afterEach(async () => {\n        unsub2();\n        await nuxtApp.runWithContext(() => showError(error));\n        window.history.pushState({}, \"\", to.fullPath);\n      });\n      return false;\n    }\n  });\n});\n", "import { hasProtocol } from \"ufo\";\nimport { defineNuxtRouteMiddleware } from \"../composables/router.js\";\nimport { getRouteRules } from \"../composables/manifest.js\";\nexport default defineNuxtRouteMiddleware(async (to) => {\n  if (import.meta.server || import.meta.test) {\n    return;\n  }\n  const rules = await getRouteRules(to.path);\n  if (rules.redirect) {\n    if (hasProtocol(rules.redirect, { acceptRelative: true })) {\n      window.location.href = rules.redirect;\n      return false;\n    }\n    return rules.redirect;\n  }\n});\n", "import validate from \"D:/peihexian/website/website/node_modules/nuxt/dist/pages/runtime/validate.js\";\nimport manifest_45route_45rule from \"D:/peihexian/website/website/node_modules/nuxt/dist/app/middleware/manifest-route-rule.js\";\nexport const globalMiddleware = [\n  validate,\n  manifest_45route_45rule\n]\nexport const namedMiddleware = {}", "import { isReadonly, reactive, shallowReactive, shallowRef } from \"vue\";\nimport { START_LOCATION, createMemoryHistory, createRouter, createWebHashHistory, createWebHistory } from \"vue-router\";\nimport { createError } from \"h3\";\nimport { isEqual, withoutBase } from \"ufo\";\nimport { toArray } from \"../utils.js\";\nimport { getRouteRules } from \"#app/composables/manifest\";\nimport { defineNuxtPlugin, useRuntimeConfig } from \"#app/nuxt\";\nimport { clearError, showError, useError } from \"#app/composables/error\";\nimport { navigateTo } from \"#app/composables/router\";\nimport { appManifest as isAppManifestEnabled } from \"#build/nuxt.config.mjs\";\nimport _routes from \"#build/routes\";\nimport routerOptions from \"#build/router.options\";\nimport { globalMiddleware, namedMiddleware } from \"#build/middleware\";\nfunction createCurrentLocation(base, location, renderedPath) {\n  const { pathname, search, hash } = location;\n  const hashPos = base.indexOf(\"#\");\n  if (hashPos > -1) {\n    const slicePos = hash.includes(base.slice(hashPos)) ? base.slice(hashPos).length : 1;\n    let pathFromHash = hash.slice(slicePos);\n    if (pathFromHash[0] !== \"/\") {\n      pathFromHash = \"/\" + pathFromHash;\n    }\n    return withoutBase(pathFromHash, \"\");\n  }\n  const displayedPath = withoutBase(pathname, base);\n  const path = !renderedPath || isEqual(displayedPath, renderedPath, { trailingSlash: true }) ? displayedPath : renderedPath;\n  return path + (path.includes(\"?\") ? \"\" : search) + hash;\n}\nconst plugin = defineNuxtPlugin({\n  name: \"nuxt:router\",\n  enforce: \"pre\",\n  async setup(nuxtApp) {\n    let routerBase = useRuntimeConfig().app.baseURL;\n    if (routerOptions.hashMode && !routerBase.includes(\"#\")) {\n      routerBase += \"#\";\n    }\n    const history = routerOptions.history?.(routerBase) ?? (import.meta.client ? routerOptions.hashMode ? createWebHashHistory(routerBase) : createWebHistory(routerBase) : createMemoryHistory(routerBase));\n    const routes = routerOptions.routes?.(_routes) ?? _routes;\n    let startPosition;\n    const router = createRouter({\n      ...routerOptions,\n      scrollBehavior: (to, from, savedPosition) => {\n        if (from === START_LOCATION) {\n          startPosition = savedPosition;\n          return;\n        }\n        if (routerOptions.scrollBehavior) {\n          router.options.scrollBehavior = routerOptions.scrollBehavior;\n          if (\"scrollRestoration\" in window.history) {\n            const unsub = router.beforeEach(() => {\n              unsub();\n              window.history.scrollRestoration = \"manual\";\n            });\n          }\n          return routerOptions.scrollBehavior(to, START_LOCATION, startPosition || savedPosition);\n        }\n      },\n      history,\n      routes\n    });\n    if (import.meta.client && \"scrollRestoration\" in window.history) {\n      window.history.scrollRestoration = \"auto\";\n    }\n    nuxtApp.vueApp.use(router);\n    const previousRoute = shallowRef(router.currentRoute.value);\n    router.afterEach((_to, from) => {\n      previousRoute.value = from;\n    });\n    Object.defineProperty(nuxtApp.vueApp.config.globalProperties, \"previousRoute\", {\n      get: () => previousRoute.value\n    });\n    const initialURL = import.meta.server ? nuxtApp.ssrContext.url : createCurrentLocation(routerBase, window.location, nuxtApp.payload.path);\n    const _route = shallowRef(router.currentRoute.value);\n    const syncCurrentRoute = () => {\n      _route.value = router.currentRoute.value;\n    };\n    nuxtApp.hook(\"page:finish\", syncCurrentRoute);\n    router.afterEach((to, from) => {\n      if (to.matched[0]?.components?.default === from.matched[0]?.components?.default) {\n        syncCurrentRoute();\n      }\n    });\n    const route = {};\n    for (const key in _route.value) {\n      Object.defineProperty(route, key, {\n        get: () => _route.value[key]\n      });\n    }\n    nuxtApp._route = shallowReactive(route);\n    nuxtApp._middleware = nuxtApp._middleware || {\n      global: [],\n      named: {}\n    };\n    const error = useError();\n    if (import.meta.client || !nuxtApp.ssrContext?.islandContext) {\n      router.afterEach(async (to, _from, failure) => {\n        delete nuxtApp._processingMiddleware;\n        if (import.meta.client && !nuxtApp.isHydrating && error.value) {\n          await nuxtApp.runWithContext(clearError);\n        }\n        if (failure) {\n          await nuxtApp.callHook(\"page:loading:end\");\n        }\n        if (import.meta.server && failure?.type === 4) {\n          return;\n        }\n        if (to.matched.length === 0) {\n          await nuxtApp.runWithContext(() => showError(createError({\n            statusCode: 404,\n            fatal: false,\n            statusMessage: `Page not found: ${to.fullPath}`,\n            data: {\n              path: to.fullPath\n            }\n          })));\n        } else if (import.meta.server && to.redirectedFrom && to.fullPath !== initialURL) {\n          await nuxtApp.runWithContext(() => navigateTo(to.fullPath || \"/\"));\n        }\n      });\n    }\n    try {\n      if (import.meta.server) {\n        await router.push(initialURL);\n      }\n      await router.isReady();\n    } catch (error2) {\n      await nuxtApp.runWithContext(() => showError(error2));\n    }\n    const resolvedInitialRoute = import.meta.client && initialURL !== router.currentRoute.value.fullPath ? router.resolve(initialURL) : router.currentRoute.value;\n    syncCurrentRoute();\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext) {\n      return { provide: { router } };\n    }\n    const initialLayout = nuxtApp.payload.state._layout;\n    router.beforeEach(async (to, from) => {\n      await nuxtApp.callHook(\"page:loading:start\");\n      to.meta = reactive(to.meta);\n      if (nuxtApp.isHydrating && initialLayout && !isReadonly(to.meta.layout)) {\n        to.meta.layout = initialLayout;\n      }\n      nuxtApp._processingMiddleware = true;\n      if (import.meta.client || !nuxtApp.ssrContext?.islandContext) {\n        const middlewareEntries = /* @__PURE__ */ new Set([...globalMiddleware, ...nuxtApp._middleware.global]);\n        for (const component of to.matched) {\n          const componentMiddleware = component.meta.middleware;\n          if (!componentMiddleware) {\n            continue;\n          }\n          for (const entry of toArray(componentMiddleware)) {\n            middlewareEntries.add(entry);\n          }\n        }\n        if (isAppManifestEnabled) {\n          const routeRules = await nuxtApp.runWithContext(() => getRouteRules(to.path));\n          if (routeRules.appMiddleware) {\n            for (const key in routeRules.appMiddleware) {\n              if (routeRules.appMiddleware[key]) {\n                middlewareEntries.add(key);\n              } else {\n                middlewareEntries.delete(key);\n              }\n            }\n          }\n        }\n        for (const entry of middlewareEntries) {\n          const middleware = typeof entry === \"string\" ? nuxtApp._middleware.named[entry] || await namedMiddleware[entry]?.().then((r) => r.default || r) : entry;\n          if (!middleware) {\n            if (import.meta.dev) {\n              throw new Error(`Unknown route middleware: '${entry}'. Valid middleware: ${Object.keys(namedMiddleware).map((mw) => `'${mw}'`).join(\", \")}.`);\n            }\n            throw new Error(`Unknown route middleware: '${entry}'.`);\n          }\n          const result = await nuxtApp.runWithContext(() => middleware(to, from));\n          if (import.meta.server || !nuxtApp.payload.serverRendered && nuxtApp.isHydrating) {\n            if (result === false || result instanceof Error) {\n              const error2 = result || createError({\n                statusCode: 404,\n                statusMessage: `Page Not Found: ${initialURL}`\n              });\n              await nuxtApp.runWithContext(() => showError(error2));\n              return false;\n            }\n          }\n          if (result === true) {\n            continue;\n          }\n          if (result || result === false) {\n            return result;\n          }\n        }\n      }\n    });\n    router.onError(async () => {\n      delete nuxtApp._processingMiddleware;\n      await nuxtApp.callHook(\"page:loading:end\");\n    });\n    nuxtApp.hooks.hookOnce(\"app:created\", async () => {\n      try {\n        if (\"name\" in resolvedInitialRoute) {\n          resolvedInitialRoute.name = void 0;\n        }\n        await router.replace({\n          ...resolvedInitialRoute,\n          force: true\n        });\n        router.options.scrollBehavior = routerOptions.scrollBehavior;\n      } catch (error2) {\n        await nuxtApp.runWithContext(() => showError(error2));\n      }\n    });\n    return { provide: { router } };\n  }\n});\nexport default plugin;\n", "import { hasProtocol, joinUR<PERSON>, withoutTrailingSlash } from \"ufo\";\nimport { parse } from \"devalue\";\nimport { useHead } from \"@unhead/vue\";\nimport { getCurrentInstance, onServerPrefetch } from \"vue\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { useRoute } from \"./router.js\";\nimport { getAppManifest, getRouteRules } from \"./manifest.js\";\nimport { appManifest, payloadExtraction, renderJsonPayloads } from \"#build/nuxt.config.mjs\";\nexport async function loadPayload(url, opts = {}) {\n  if (import.meta.server || !payloadExtraction) {\n    return null;\n  }\n  const payloadURL = await _getPayloadURL(url, opts);\n  const nuxtApp = useNuxtApp();\n  const cache = nuxtApp._payloadCache = nuxtApp._payloadCache || {};\n  if (payloadURL in cache) {\n    return cache[payloadURL];\n  }\n  cache[payloadURL] = isPrerendered(url).then((prerendered) => {\n    if (!prerendered) {\n      cache[payloadURL] = null;\n      return null;\n    }\n    return _importPayload(payloadURL).then((payload) => {\n      if (payload) {\n        return payload;\n      }\n      delete cache[payloadURL];\n      return null;\n    });\n  });\n  return cache[payloadURL];\n}\nexport function preloadPayload(url, opts = {}) {\n  const nuxtApp = useNuxtApp();\n  const promise = _getPayloadURL(url, opts).then((payloadURL) => {\n    nuxtApp.runWithContext(() => useHead({\n      link: [\n        { rel: \"modulepreload\", href: payloadURL }\n      ]\n    }));\n  });\n  if (import.meta.server) {\n    onServerPrefetch(() => promise);\n  }\n  return promise;\n}\nconst filename = renderJsonPayloads ? \"_payload.json\" : \"_payload.js\";\nasync function _getPayloadURL(url, opts = {}) {\n  const u = new URL(url, \"http://localhost\");\n  if (u.host !== \"localhost\" || hasProtocol(u.pathname, { acceptRelative: true })) {\n    throw new Error(\"Payload URL must not include hostname: \" + url);\n  }\n  const config = useRuntimeConfig();\n  const hash = opts.hash || (opts.fresh ? Date.now() : config.app.buildId);\n  const cdnURL = config.app.cdnURL;\n  const baseOrCdnURL = cdnURL && await isPrerendered(url) ? cdnURL : config.app.baseURL;\n  return joinURL(baseOrCdnURL, u.pathname, filename + (hash ? `?${hash}` : \"\"));\n}\nasync function _importPayload(payloadURL) {\n  if (import.meta.server || !payloadExtraction) {\n    return null;\n  }\n  const payloadPromise = renderJsonPayloads ? fetch(payloadURL).then((res) => res.text().then(parsePayload)) : import(\n    /* webpackIgnore: true */\n    /* @vite-ignore */\n    payloadURL\n  ).then((r) => r.default || r);\n  try {\n    return await payloadPromise;\n  } catch (err) {\n    console.warn(\"[nuxt] Cannot load payload \", payloadURL, err);\n  }\n  return null;\n}\nexport async function isPrerendered(url = useRoute().path) {\n  if (!appManifest) {\n    return !!useNuxtApp().payload.prerenderedAt;\n  }\n  url = withoutTrailingSlash(url);\n  const manifest = await getAppManifest();\n  if (manifest.prerendered.includes(url)) {\n    return true;\n  }\n  const rules = await getRouteRules(url);\n  return !!rules.prerender && !rules.redirect;\n}\nlet payloadCache = null;\nexport async function getNuxtClientPayload() {\n  if (import.meta.server) {\n    return null;\n  }\n  if (payloadCache) {\n    return payloadCache;\n  }\n  const el = document.getElementById(\"__NUXT_DATA__\");\n  if (!el) {\n    return {};\n  }\n  const inlineData = await parsePayload(el.textContent || \"\");\n  const externalData = el.dataset.src ? await _importPayload(el.dataset.src) : void 0;\n  payloadCache = {\n    ...inlineData,\n    ...externalData,\n    ...window.__NUXT__\n  };\n  return payloadCache;\n}\nexport async function parsePayload(payload) {\n  return await parse(payload, useNuxtApp()._payloadRevivers);\n}\nexport function definePayloadReducer(name, reduce) {\n  if (import.meta.server) {\n    useNuxtApp().ssrContext._payloadReducers[name] = reduce;\n  }\n}\nexport function definePayloadReviver(name, revive) {\n  if (import.meta.dev && getCurrentInstance()) {\n    console.warn(\"[nuxt] [definePayloadReviver] This function must be called in a Nuxt plugin that is `unshift`ed to the beginning of the Nuxt plugins array.\");\n  }\n  if (import.meta.client) {\n    useNuxtApp()._payloadRevivers[name] = revive;\n  }\n}\n", "import { isReactive, isRef, isShallow, toRaw } from \"vue\";\nimport { definePayloadReducer } from \"../composables/payload.js\";\nimport { isNuxtError } from \"../composables/error.js\";\nimport { defineNuxtPlugin } from \"../nuxt.js\";\nimport { componentIslands } from \"#build/nuxt.config.mjs\";\nconst reducers = {\n  NuxtError: (data) => isNuxtError(data) && data.toJSON(),\n  EmptyShallowRef: (data) => isRef(data) && isShallow(data) && !data.value && (typeof data.value === \"bigint\" ? \"0n\" : JSON.stringify(data.value) || \"_\"),\n  EmptyRef: (data) => isRef(data) && !data.value && (typeof data.value === \"bigint\" ? \"0n\" : JSON.stringify(data.value) || \"_\"),\n  ShallowRef: (data) => isRef(data) && isShallow(data) && data.value,\n  ShallowReactive: (data) => isReactive(data) && isShallow(data) && toRaw(data),\n  Ref: (data) => isRef(data) && data.value,\n  Reactive: (data) => isReactive(data) && toRaw(data)\n};\nif (componentIslands) {\n  reducers.Island = (data) => data && data?.__nuxt_island;\n}\nexport default defineNuxtPlugin({\n  name: \"nuxt:revive-payload:server\",\n  setup() {\n    for (const reducer in reducers) {\n      definePayloadReducer(reducer, reducers[reducer]);\n    }\n  }\n});\n", "\nimport { defineNuxtPlugin } from '#app/nuxt'\nexport default defineNuxtPlugin({\n  name: 'nuxt:global-components',\n})\n", "import unhead_KgADcZ0jPj from \"D:/peihexian/website/website/node_modules/nuxt/dist/head/runtime/plugins/unhead.js\";\nimport router_jmwsqit4Rs from \"D:/peihexian/website/website/node_modules/nuxt/dist/pages/runtime/plugins/router.js\";\nimport revive_payload_server_eJ33V7gbc6 from \"D:/peihexian/website/website/node_modules/nuxt/dist/app/plugins/revive-payload.server.js\";\nimport components_plugin_KR1HBZs4kY from \"D:/peihexian/website/website/.nuxt/components.plugin.mjs\";\nexport default [\n  unhead_KgADcZ0jPj,\n  router_jmwsqit4Rs,\n  revive_payload_server_eJ33V7gbc6,\n  components_plugin_KR1HBZs4kY\n]", "import { defineComponent, h, nextTick, onMounted, provide, shallowReactive } from \"vue\";\nimport { PageRouteSymbol } from \"./injections.js\";\nexport const RouteProvider = defineComponent({\n  props: {\n    vnode: {\n      type: Object,\n      required: true\n    },\n    route: {\n      type: Object,\n      required: true\n    },\n    vnodeRef: Object,\n    renderKey: String,\n    trackRootNodes: Boolean\n  },\n  setup(props) {\n    const previousKey = props.renderKey;\n    const previousRoute = props.route;\n    const route = {};\n    for (const key in props.route) {\n      Object.defineProperty(route, key, {\n        get: () => previousKey === props.renderKey ? props.route[key] : previousRoute[key]\n      });\n    }\n    provide(PageRouteSymbol, shallowReactive(route));\n    let vnode;\n    if (import.meta.dev && import.meta.client && props.trackRootNodes) {\n      onMounted(() => {\n        nextTick(() => {\n          if ([\"#comment\", \"#text\"].includes(vnode?.el?.nodeName)) {\n            const filename = (vnode?.type).__file;\n            console.warn(`[nuxt] \\`${filename}\\` does not have a single root node and will cause errors when navigating between routes.`);\n          }\n        });\n      });\n    }\n    return () => {\n      if (import.meta.dev && import.meta.client) {\n        vnode = h(props.vnode, { ref: props.vnodeRef });\n        return vnode;\n      }\n      return h(props.vnode, { ref: props.vnodeRef });\n    };\n  }\n});\n", "import { Fragment, Suspense, Transition, defineComponent, h, inject, nextTick, ref, watch } from \"vue\";\nimport { RouterView } from \"vue-router\";\nimport { defu } from \"defu\";\nimport { generateRouteKey, toArray, wrapInKeepAlive } from \"./utils.js\";\nimport { RouteProvider } from \"#app/components/route-provider\";\nimport { useNuxtApp } from \"#app/nuxt\";\nimport { useRouter } from \"#app/composables/router\";\nimport { _wrapIf } from \"#app/components/utils\";\nimport { LayoutMetaSymbol, PageRouteSymbol } from \"#app/components/injections\";\nimport { appKeepalive as defaultKeepaliveConfig, appPageTransition as defaultPageTransition } from \"#build/nuxt.config.mjs\";\nexport default defineComponent({\n  name: \"NuxtPage\",\n  inheritAttrs: false,\n  props: {\n    name: {\n      type: String\n    },\n    transition: {\n      type: [Boolean, Object],\n      default: void 0\n    },\n    keepalive: {\n      type: [Boolean, Object],\n      default: void 0\n    },\n    route: {\n      type: Object\n    },\n    pageKey: {\n      type: [Function, String],\n      default: null\n    }\n  },\n  setup(props, { attrs, slots, expose }) {\n    const nuxtApp = useNuxtApp();\n    const pageRef = ref();\n    const forkRoute = inject(PageRouteSymbol, null);\n    let previousPageKey;\n    expose({ pageRef });\n    const _layoutMeta = inject(LayoutMetaSymbol, null);\n    let vnode;\n    const done = nuxtApp.deferHydration();\n    if (import.meta.client && nuxtApp.isHydrating) {\n      const removeErrorHook = nuxtApp.hooks.hookOnce(\"app:error\", done);\n      useRouter().beforeEach(removeErrorHook);\n    }\n    if (props.pageKey) {\n      watch(() => props.pageKey, (next, prev) => {\n        if (next !== prev) {\n          nuxtApp.callHook(\"page:loading:start\");\n        }\n      });\n    }\n    if (import.meta.dev) {\n      nuxtApp._isNuxtPageUsed = true;\n    }\n    return () => {\n      return h(RouterView, { name: props.name, route: props.route, ...attrs }, {\n        default: (routeProps) => {\n          const isRenderingNewRouteInOldFork = import.meta.client && haveParentRoutesRendered(forkRoute, routeProps.route, routeProps.Component);\n          const hasSameChildren = import.meta.client && forkRoute && forkRoute.matched.length === routeProps.route.matched.length;\n          if (!routeProps.Component) {\n            if (import.meta.client && vnode && !hasSameChildren) {\n              return vnode;\n            }\n            done();\n            return;\n          }\n          if (import.meta.client && vnode && _layoutMeta && !_layoutMeta.isCurrent(routeProps.route)) {\n            return vnode;\n          }\n          if (import.meta.client && isRenderingNewRouteInOldFork && forkRoute && (!_layoutMeta || _layoutMeta?.isCurrent(forkRoute))) {\n            if (hasSameChildren) {\n              return vnode;\n            }\n            return null;\n          }\n          const key = generateRouteKey(routeProps, props.pageKey);\n          if (!nuxtApp.isHydrating && !hasChildrenRoutes(forkRoute, routeProps.route, routeProps.Component) && previousPageKey === key) {\n            nuxtApp.callHook(\"page:loading:end\");\n          }\n          previousPageKey = key;\n          const hasTransition = !!(props.transition ?? routeProps.route.meta.pageTransition ?? defaultPageTransition);\n          const transitionProps = hasTransition && _mergeTransitionProps([\n            props.transition,\n            routeProps.route.meta.pageTransition,\n            defaultPageTransition,\n            { onAfterLeave: () => {\n              nuxtApp.callHook(\"page:transition:finish\", routeProps.Component);\n            } }\n          ].filter(Boolean));\n          const keepaliveConfig = props.keepalive ?? routeProps.route.meta.keepalive ?? defaultKeepaliveConfig;\n          vnode = _wrapIf(\n            Transition,\n            hasTransition && transitionProps,\n            wrapInKeepAlive(\n              keepaliveConfig,\n              h(Suspense, {\n                suspensible: true,\n                onPending: () => nuxtApp.callHook(\"page:start\", routeProps.Component),\n                onResolve: () => {\n                  nextTick(() => nuxtApp.callHook(\"page:finish\", routeProps.Component).then(() => nuxtApp.callHook(\"page:loading:end\")).finally(done));\n                }\n              }, {\n                default: () => {\n                  const providerVNode = h(RouteProvider, {\n                    key: key || void 0,\n                    vnode: slots.default ? h(Fragment, void 0, slots.default(routeProps)) : routeProps.Component,\n                    route: routeProps.route,\n                    renderKey: key || void 0,\n                    trackRootNodes: hasTransition,\n                    vnodeRef: pageRef\n                  });\n                  if (import.meta.client && keepaliveConfig) {\n                    providerVNode.type.name = routeProps.Component.type.name || routeProps.Component.type.__name || \"RouteProvider\";\n                  }\n                  return providerVNode;\n                }\n              })\n            )\n          ).default();\n          return vnode;\n        }\n      });\n    };\n  }\n});\nfunction _mergeTransitionProps(routeProps) {\n  const _props = routeProps.map((prop) => ({\n    ...prop,\n    onAfterLeave: prop.onAfterLeave ? toArray(prop.onAfterLeave) : void 0\n  }));\n  return defu(..._props);\n}\nfunction haveParentRoutesRendered(fork, newRoute, Component) {\n  if (!fork) {\n    return false;\n  }\n  const index = newRoute.matched.findIndex((m) => m.components?.default === Component?.type);\n  if (!index || index === -1) {\n    return false;\n  }\n  return newRoute.matched.slice(0, index).some(\n    (c, i) => c.components?.default !== fork.matched[i]?.components?.default\n  ) || Component && generateRouteKey({ route: newRoute, Component }) !== generateRouteKey({ route: fork, Component });\n}\nfunction hasChildrenRoutes(fork, newRoute, Component) {\n  if (!fork) {\n    return false;\n  }\n  const index = newRoute.matched.findIndex((m) => m.components?.default === Component?.type);\n  return index < newRoute.matched.length - 1;\n}\n", "<template>\r\n  <div>\r\n    <NuxtPage />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\n</script>", "<template>\n  <ErrorTemplate v-bind=\"{ statusCode, statusMessage, description, stack }\" />\n</template>\n\n<script setup>\nimport { defineAsyncComponent } from 'vue'\n\nconst props = defineProps({\n  error: Object,\n})\n\n// Deliberately prevent reactive update when error is cleared\nconst _error = props.error\n\n// TODO: extract to a separate utility\nconst stacktrace = _error.stack\n  ? _error.stack\n    .split('\\n')\n    .splice(1)\n    .map((line) => {\n      const text = line\n        .replace('webpack:/', '')\n        .replace('.vue', '.js') // TODO: Support sourcemap\n        .trim()\n      return {\n        text,\n        internal: (line.includes('node_modules') && !line.includes('.cache')) ||\n          line.includes('internal') ||\n          line.includes('new Promise'),\n      }\n    }).map(i => `<span class=\"stack${i.internal ? ' internal' : ''}\">${i.text}</span>`).join('\\n')\n  : ''\n\n// Error page props\nconst statusCode = Number(_error.statusCode || 500)\nconst is404 = statusCode === 404\n\nconst statusMessage = _error.statusMessage ?? (is404 ? 'Page Not Found' : 'Internal Server Error')\nconst description = _error.message || _error.toString()\nconst stack = import.meta.dev && !is404 ? _error.description || `<pre>${stacktrace}</pre>` : undefined\n\n// TODO: Investigate side-effect issue with imports\nconst _Error404 = defineAsyncComponent(() => import('./error-404.vue').then(r => r.default || r))\nconst _Error = import.meta.dev\n  ? defineAsyncComponent(() => import('./error-dev.vue').then(r => r.default || r))\n  : defineAsyncComponent(() => import('./error-500.vue').then(r => r.default || r))\n\nconst ErrorTemplate = is404 ? _Error404 : _Error\n</script>\n", "<template>\n  <Suspense @resolve=\"onResolve\">\n    <div v-if=\"abortRender\" />\n    <ErrorComponent\n      v-else-if=\"error\"\n      :error=\"error\"\n    />\n    <IslandRenderer\n      v-else-if=\"islandContext\"\n      :context=\"islandContext\"\n    />\n    <component\n      :is=\"SingleRenderer\"\n      v-else-if=\"SingleRenderer\"\n    />\n    <AppComponent v-else />\n  </Suspense>\n</template>\n\n<script setup>\nimport { defineAsyncComponent, onErrorCaptured, onServerPrefetch, provide } from 'vue'\nimport { useNuxtApp } from '../nuxt'\nimport { isNuxtError, showError, useError } from '../composables/error'\nimport { useRoute, useRouter } from '../composables/router'\nimport { PageRouteSymbol } from '../components/injections'\nimport AppComponent from '#build/app-component.mjs'\nimport ErrorComponent from '#build/error-component.mjs'\n// @ts-expect-error virtual file\nimport { componentIslands } from '#build/nuxt.config.mjs'\n\nconst IslandRenderer = import.meta.server && componentIslands\n  ? defineAsyncComponent(() => import('./island-renderer').then(r => r.default || r))\n  : () => null\n\nconst nuxtApp = useNuxtApp()\nconst onResolve = nuxtApp.deferHydration()\nif (import.meta.client && nuxtApp.isHydrating) {\n  const removeErrorHook = nuxtApp.hooks.hookOnce('app:error', onResolve)\n  useRouter().beforeEach(removeErrorHook)\n}\n\nconst url = import.meta.server ? nuxtApp.ssrContext.url : window.location.pathname\nconst SingleRenderer = import.meta.test && import.meta.dev && import.meta.server && url.startsWith('/__nuxt_component_test__/') && defineAsyncComponent(() => import('#build/test-component-wrapper.mjs')\n  .then(r => r.default(import.meta.server ? url : window.location.href)))\n\n// Inject default route (outside of pages) as active route\nprovide(PageRouteSymbol, useRoute())\n\n// vue:setup hook\nconst results = nuxtApp.hooks.callHookWith(hooks => hooks.map(hook => hook()), 'vue:setup')\nif (import.meta.dev && results && results.some(i => i && 'then' in i)) {\n  console.error('[nuxt] Error in `vue:setup`. Callbacks must be synchronous.')\n}\n\n// error handling\nconst error = useError()\n// render an empty <div> when plugins have thrown an error but we're not yet rendering the error page\nconst abortRender = import.meta.server && error.value && !nuxtApp.ssrContext.error\nonErrorCaptured((err, target, info) => {\n  nuxtApp.hooks.callHook('vue:error', err, target, info).catch(hookError => console.error('[nuxt] Error in `vue:error` hook', hookError))\n  if (import.meta.server || (isNuxtError(err) && (err.fatal || err.unhandled))) {\n    const p = nuxtApp.runWithContext(() => showError(err))\n    onServerPrefetch(() => p)\n    return false // suppress error from breaking render\n  }\n})\n\n// Component islands context\nconst islandContext = import.meta.server && nuxtApp.ssrContext.islandContext\n</script>\n", "import { createApp, create<PERSON><PERSON><PERSON>, nextTick } from \"vue\";\nimport \"#build/fetch.mjs\";\nimport { applyPlugins, createNuxtApp } from \"./nuxt.js\";\nimport { createError } from \"./composables/error.js\";\nimport \"#build/css\";\nimport plugins from \"#build/plugins\";\nimport RootComponent from \"#build/root-component.mjs\";\nimport { vueAppRootContainer } from \"#build/nuxt.config.mjs\";\nlet entry;\nif (import.meta.server) {\n  entry = async function createNuxtAppServer(ssrContext) {\n    const vueApp = createApp(RootComponent);\n    const nuxt = createNuxtApp({ vueApp, ssrContext });\n    try {\n      await applyPlugins(nuxt, plugins);\n      await nuxt.hooks.callHook(\"app:created\", vueApp);\n    } catch (error) {\n      await nuxt.hooks.callHook(\"app:error\", error);\n      nuxt.payload.error = nuxt.payload.error || createError(error);\n    }\n    if (ssrContext?._renderResponse) {\n      throw new Error(\"skipping render\");\n    }\n    return vueApp;\n  };\n}\nif (import.meta.client) {\n  if (import.meta.dev && import.meta.webpackHot) {\n    import.meta.webpackHot.accept();\n  }\n  let vueAppPromise;\n  entry = async function initApp() {\n    if (vueAppPromise) {\n      return vueAppPromise;\n    }\n    const isSSR = Boolean(\n      window.__NUXT__?.serverRendered || document.getElementById(\"__NUXT_DATA__\")?.dataset.ssr === \"true\"\n    );\n    const vueApp = isSSR ? createSSRApp(RootComponent) : createApp(RootComponent);\n    const nuxt = createNuxtApp({ vueApp });\n    async function handleVueError(error) {\n      await nuxt.callHook(\"app:error\", error);\n      nuxt.payload.error = nuxt.payload.error || createError(error);\n    }\n    vueApp.config.errorHandler = handleVueError;\n    try {\n      await applyPlugins(nuxt, plugins);\n    } catch (err) {\n      handleVueError(err);\n    }\n    try {\n      await nuxt.hooks.callHook(\"app:created\", vueApp);\n      await nuxt.hooks.callHook(\"app:beforeMount\", vueApp);\n      vueApp.mount(vueAppRootContainer);\n      await nuxt.hooks.callHook(\"app:mounted\", vueApp);\n      await nextTick();\n    } catch (err) {\n      handleVueError(err);\n    }\n    if (vueApp.config.errorHandler === handleVueError) {\n      vueApp.config.errorHandler = void 0;\n    }\n    return vueApp;\n  };\n  vueAppPromise = entry().catch((error) => {\n    console.error(\"Error while mounting app:\", error);\n    throw error;\n  });\n}\nexport default (ssrContext) => entry(ssrContext);\n"], "names": ["plugin", "provide", "plugins", "_a", "createH3Error", "ref", "entry", "globalKey", "generateRouteKey", "createRadixRouter", "defaultPageTransition", "__executeAsync", "createRouter", "_b", "_d", "_c", "createError", "router_jmwsqit4Rs", "defaultKeepaliveConfig", "_ssrRenderAttrs", "_ssrRenderComponent", "RootComponent"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAI,CAAC,WAAW,QAAQ;AACtB,aAAW,SAAS,OAAO,OAAO;AAAA,IAChC,SAAS,QAAS;AAAA,EACtB,CAAG;AACH;ACIO,MAAM,oBAAoB;AAI1B,MAAM,eAAe;AAkChB,MAAC,mBAAmB,EAAC,iBAAgB,WAAU;AAcpD,MAAM,QAAQ;AC1DrB,SAAS,cAAc,UAAU,OAAqB;AACpD,SAAO,WAAW,SAAS;AAAA,IACzB,cAAc;AAAA,EAAA,CACf;AACH;AACO,MAAM,sBAAsB;AAC5B,SAAS,cAAc,SAAS;AACrC,MAAI,iBAAiB;AACrB,QAAM,UAAU;AAAA,IACd,OAAO;AAAA,IACP,QAAQ,YAAY;AAAA,IACpB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,MACR,IAAI,OAAO;AACF,eAAA;AAAA,MACT;AAAA,MACA,IAAI,MAAM;AACR,eAAO,QAAQ,OAAO;AAAA,MACxB;AAAA,IACF;AAAA,IACA,SAAS,gBAAgB;AAAA,MACvB,MAAM,gBAAgB,EAAE;AAAA,MACxB,OAAO,SAAS,EAAE;AAAA,MAClB,0BAA0B,IAAI;AAAA,MAC9B,SAAS,gBAAgB,EAAE;AAAA,IAAA,CAC5B;AAAA,IACD,QAAQ;AAAA,MACN,MAAM,CAAC;AAAA,IACT;AAAA,IACA,eAAe,IAAI;AACjB,UAAI,QAAQ,OAAO,UAAU,CAAC,mBAAmB;AAC/C,eAAO,QAAQ,OAAO,IAAI,MAAM,aAAa,SAAS,EAAE,CAAC;AAAA,MAC3D;AACO,aAAA,aAAa,SAAS,EAAE;AAAA,IACjC;AAAA,IACA,aAAa;AAAA,IACb,iBAAiB;AACX,UAAA,CAAC,QAAQ,aAAa;AACxB,eAAO,MAAM;AAAA,QAAA;AAAA,MAEf;AACA;AACA,UAAI,SAAS;AACb,aAAO,MAAM;AACX,YAAI,QAAQ;AACV;AAAA,QACF;AACS,iBAAA;AACT;AACA,YAAI,mBAAmB,GAAG;AACxB,kBAAQ,cAAc;AACf,iBAAA,QAAQ,SAAS,sBAAsB;AAAA,QAChD;AAAA,MAAA;AAAA,IAEJ;AAAA,IACA,oBAAoB,CAAC;AAAA,IACrB,YAAY,gBAAgB,EAAE;AAAA,IAC9B,kBAAkB,CAAC;AAAA,IACnB,GAAG;AAAA,EAAA;AAEmB;AACtB,YAAQ,QAAQ,iBAAiB;AAAA,EACnC;AAcA,UAAQ,QAAQ;AACR,UAAA,OAAO,QAAQ,MAAM;AACL;AAChB,UAAA,gBAAgB,eAAe,OAAO,MAAM;AAChD,iBAAW,QAAQ,OAAO;AACxB,cAAM,QAAQ,eAAe,MAAM,KAAK,GAAG,IAAI,CAAC;AAAA,MAClD;AAAA,IAAA;AAEM,YAAA,MAAM,WAAW,CAAC,SAAS,SAAS,QAAQ,MAAM,aAAa,eAAe,MAAM,GAAG,IAAI;AAAA,EACrG;AACQ,UAAA,WAAW,QAAQ,MAAM;AACzB,UAAA,UAAU,CAAC,MAAM,UAAU;AACjC,UAAM,QAAQ,MAAM;AACP,iBAAA,SAAS,OAAO,KAAK;AAClC,iBAAa,QAAQ,OAAO,OAAO,kBAAkB,OAAO,KAAK;AAAA,EAAA;AAEtD,eAAA,QAAQ,QAAQ,SAAS,OAAO;AAC7C,eAAa,QAAQ,OAAO,OAAO,kBAAkB,SAAS,OAAO;AAC7C;AACtB,QAAI,QAAQ,YAAY;AACtB,cAAQ,WAAW,OAAO;AAClB,cAAA,WAAW,mBAAmB;AAC9B,cAAA,QAAQ,OAAO,QAAQ,WAAW;AAAA,IAC5C;AACQ,YAAA,aAAa,QAAQ,cAAc,CAAA;AACvC,QAAA,QAAQ,WAAW,SAAS;AAC9B,aAAO,OAAO,QAAQ,SAAS,QAAQ,WAAW,OAAO;AAAA,IAC3D;AACQ,YAAA,WAAW,UAAU,QAAQ;AACrC,YAAQ,WAAW,SAAS;AAAA,MAC1B,QAAQ,QAAQ,WAAW,cAAc;AAAA,MACzC,KAAK,QAAQ,WAAW,cAAc;AAAA,IAAA;AAAA,EAE1C;AAWA,QAAM,gBAAqC,QAAQ,WAAW;AAC9D,UAAQ,QAAQ,UAAiF,aAAa;AACvG,SAAA;AACT;AACgB,SAAA,oBAAoB,SAASA,SAAQ;AACnD,MAAIA,QAAO,OAAO;AACR,YAAA,MAAM,SAASA,QAAO,KAAK;AAAA,EACrC;AACF;AACsB,eAAA,YAAY,SAASA,SAAQ;AAC7C,MAAA,OAAOA,YAAW,YAAY;AAC1B,UAAA,EAAE,SAAAC,aAAY,MAAM,QAAQ,eAAe,MAAMD,QAAO,OAAO,CAAC,KAAK;AACvE,QAAAC,YAAW,OAAOA,aAAY,UAAU;AAC1C,iBAAW,OAAOA,UAAS;AACzB,gBAAQ,QAAQ,KAAKA,SAAQ,GAAG,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AACsB,eAAA,aAAa,SAASC,UAAS;;AACnD,QAAM,kBAAkB,CAAA;AACxB,QAAM,oBAAoB,CAAA;AAC1B,QAAM,YAAY,CAAA;AAClB,QAAM,SAAS,CAAA;AACf,MAAI,eAAe;AACnB,iBAAe,cAAcF,SAAQ;;AAC7B,UAAA,mCAAiCG,MAAAH,QAAO,cAAP,gBAAAG,IAAkB,OAAO,CAAC,SAASD,SAAQ,KAAK,CAAC,MAAM,EAAE,UAAU,IAAI,KAAK,CAAC,gBAAgB,SAAS,IAAI,OAAM;AACnJ,QAAA,+BAA+B,SAAS,GAAG;AAC7C,wBAAkB,KAAK,CAAC,IAAI,IAAI,8BAA8B,GAAGF,OAAM,CAAC;AAAA,IAAA,OACnE;AACL,YAAM,UAAU,YAAY,SAASA,OAAM,EAAE,KAAK,YAAY;AAC5D,YAAIA,QAAO,OAAO;AACA,0BAAA,KAAKA,QAAO,KAAK;AAC3B,gBAAA,QAAQ,IAAI,kBAAkB,IAAI,OAAO,CAAC,WAAW,gBAAgB,MAAM;AAC/E,gBAAI,UAAU,IAAIA,QAAO,KAAK,GAAG;AACrB,wBAAA,OAAOA,QAAO,KAAK;AACzB,kBAAA,UAAU,SAAS,GAAG;AACxB;AACA,sBAAM,cAAc,gBAAgB;AAAA,cACtC;AAAA,YACF;AAAA,UACD,CAAA,CAAC;AAAA,QACJ;AAAA,MAAA,CACD;AACD,UAAIA,QAAO,UAAU;AACT,kBAAA,KAAK,QAAQ,MAAM,CAAC,MAAM,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,MAAA,OAC9C;AACC,cAAA;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,aAAWA,WAAUE,UAAS;AAC5B,UAA0B,aAAQ,eAAR,mBAAoB,oBAAiB,KAAAF,QAAO,QAAP,mBAAY,aAAY,OAAO;AAC5F;AAAA,IACF;AACA,wBAAoB,SAASA,OAAM;AAAA,EACrC;AACA,aAAWA,WAAUE,UAAS;AAC5B,UAA0B,aAAQ,eAAR,mBAAoB,oBAAiB,KAAAF,QAAO,QAAP,mBAAY,aAAY,OAAO;AAC5F;AAAA,IACF;AACA,UAAM,cAAcA,OAAM;AAAA,EAC5B;AACM,QAAA,QAAQ,IAAI,SAAS;AAC3B,MAAI,cAAc;AAChB,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AAC/B,YAAA,QAAQ,IAAI,SAAS;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,OAAO,QAAQ;AACjB,UAAM,OAAO,CAAC;AAAA,EAChB;AACF;AAAA;AAEO,SAAS,iBAAiBA,SAAQ;AACnC,MAAA,OAAOA,YAAW,YAAY;AACzB,WAAAA;AAAA,EACT;AACM,QAAA,QAAQA,QAAO,SAASA,QAAO;AACrC,SAAOA,QAAO;AACd,SAAO,OAAO,OAAOA,QAAO,UAAU,MAAM;AAAA,EAAA,IACxCA,SAAQ,EAAE,CAAC,mBAAmB,GAAG,MAAM,OAAO;AACpD;AAKgB,SAAA,aAAa,MAAM,OAAO,MAAM;AAC9C,QAAM,KAAK,MAA8B;AACnC,QAAA,aAAa,cAAc,KAAK,KAAK;AACnB;AACf,WAAA,KAAK,OAAO,eAAe,MAAM,WAAW,UAAU,MAAM,EAAE,CAAC;AAAA,EAIxE;AACF;AACO,SAAS,cAAc,SAAS;;AACjC,MAAA;AACJ,MAAI,uBAAuB;AACP,uBAAA,wBAAsB,MAAtB,mBAAsB,WAAW,IAAI;AAAA,EACzD;AACA,oBAAkB,mBAAmB,cAAc,OAAO,EAAE,OAAO;AACnE,SAAO,mBAAmB;AAC5B;AACO,SAAS,WAAW,SAAS;AAC5B,QAAA,kBAAkB,cAAc,OAAO;AAC7C,MAAI,CAAC,iBAAiB;AAGb;AACC,YAAA,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAAA,EACF;AACO,SAAA;AACT;AAAA;AAEO,SAAS,iBAAiB,QAAQ;AACvC,SAAO,WAAa,EAAA;AACtB;AACA,SAAS,aAAa,KAAK,KAAK,KAAK;AACnC,SAAO,eAAe,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK;AACpD;ACvPO,MAAM,mBAAmB,OAAO,aAAa;AAC7C,MAAM,kBAAkB,OAAO,OAAO;ACKtC,MAAM,YAAY,MAAM;;AAC7B,UAAO,gBAAc,MAAd,mBAAc;AACvB;AACO,MAAM,WAAW,MAAM;AAI5B,MAAI,uBAAuB;AACzB,WAAO,OAAO,iBAAiB,WAAW,EAAE,MAAM;AAAA,EACpD;AACA,SAAO,WAAa,EAAA;AACtB;AAAA;AAeO,SAAS,0BAA0B,YAAY;AAC7C,SAAA;AACT;AAeA,MAAM,yBAAyB,MAAM;AAC/B,MAAA;AACE,QAAA,aAAa,uBAAuB;AAC/B,aAAA;AAAA,IACT;AAAA,EAAA,QACM;AACC,WAAA;AAAA,EACT;AACO,SAAA;AACT;AACa,MAAA,aAAa,CAAC,IAAI,YAAY;AACzC,MAAI,CAAC,IAAI;AACF,SAAA;AAAA,EACP;AACA,QAAM,SAAS,OAAO,OAAO,WAAW,KAAK,UAAU,KAAK,mBAAmB,EAAE,IAAI,UAAY,EAAA,QAAQ,EAAE,EAAE;AAO7G,QAAM,iBAAiB,YAAY,QAAQ,EAAE,gBAAgB,MAAM;AAC7D,QAAA,cAAa,mCAAS,aAAY;AACxC,MAAI,YAAY;AACV,QAAA,EAAC,mCAAS,WAAU;AAChB,YAAA,IAAI,MAAM,qGAAqG;AAAA,IACvH;AACM,UAAA,EAAE,SAAS,IAAI,IAAI,IAAI,QAAoD,kBAAkB;AAC/F,QAAA,YAAY,iBAAiB,QAAQ,GAAG;AAC1C,YAAM,IAAI,MAAM,kCAAkC,QAAQ,aAAa;AAAA,IACzE;AAAA,EACF;AACA,QAAM,eAAe;AAIrB,QAAM,SAAS;AACf,QAAM,UAAU;AACQ;AACtB,QAAI,QAAQ,YAAY;AAChB,YAAA,WAAW,OAAO,OAAO,YAAY,aAAa,SAAS,OAAO,QAAQ,EAAE,EAAE,YAAY;AAC1F,YAAA,YAAY,aAAa,SAAS,SAAQ,oCAAmB,IAAI,SAAS,QAAQ;AAClF,YAAA,WAAW,eAAe,UAAU;AAClC,cAAA,QAAQ,SAAS,gBAAgB;AACvC,cAAM,aAAa,UAAU,QAAQ,MAAM,KAAK;AAC1C,cAAA,gBAAgB,UAAU,WAAW,cAAc;AACzD,gBAAQ,WAAW,kBAAkB;AAAA,UACnC,YAAY,oBAAmB,mCAAS,iBAAgB,KAAK,GAAG;AAAA,UAChE,MAAM,yEAAyE,UAAU;AAAA,UACzF,SAAS,EAAE,UAAU,cAAc;AAAA,QAAA;AAE9B,eAAA;AAAA,MAAA;AAEL,UAAA,CAAC,cAAc,cAAc;AACxB,eAAA,UAAU,CAAC,UAAU,MAAM,aAAa,WAAW,SAAS,KAAK,IAAI,MAAM;AAC3E,eAAA;AAAA,MACT;AACO,aAAA,SAAS,CAAC,eAAe;AAAA;AAAA,QAE9B;AAAA,OACD;AAAA,IACH;AAAA,EACF;AACA,MAAI,YAAY;AACd,YAAQ,OAAO;AACf,QAAI,mCAAS,SAAS;AACpB,MAAA,SAAS,QAAQ,MAAM;AAAA,IAAA,OAClB;AACL,MAAA,SAAS,OAAO;AAAA,IAClB;AACA,QAAI,cAAc;AACZ,UAAA,CAAC,QAAQ,aAAa;AACjB,eAAA;AAAA,MACT;AACO,aAAA,IAAI,QAAQ,MAAM;AAAA,MAAA,CACxB;AAAA,IACH;AACA,WAAO,QAAQ;EACjB;AACO,UAAA,mCAAS,WAAU,OAAO,QAAQ,EAAE,IAAI,OAAO,KAAK,EAAE;AAC/D;AAoCO,SAAS,mBAAmB,IAAI;AAC9B,SAAA,UAAU,GAAG,QAAQ,IAAI,GAAG,SAAS,CAAA,CAAE,KAAK,GAAG,QAAQ;AAChE;AACgB,SAAA,UAAU,WAAW,iBAAiB,OAAO;AAC3D,QAAM,MAAM,IAAI,IAAI,WAAW,kBAAkB;AACjD,MAAI,CAAC,gBAAgB;AACnB,WAAO,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,EACzC;AACI,MAAA,UAAU,WAAW,IAAI,GAAG;AAC9B,WAAO,IAAI,WAAW,QAAQ,IAAI,UAAU,EAAE;AAAA,EAChD;AACA,SAAO,IAAI;AACb;AC5KO,MAAM,uBAAuB;AAC7B,MAAM,WAAW,MAAM,MAAM,WAAY,EAAC,SAAS,OAAO;AAC1D,MAAM,YAAY,CAAC,UAAU;AAClC,QAAM,YAAY,YAAY,KAAK;AACnC,MAAI;AACF,UAAM,UAAU;AAChB,UAAM,SAAS;AACf,QAAI,MAAoB;AAGxB,WAAO,QAAQ,OAAO,SAAS;AAAA,EACnC,QAAU;AACN,UAAM;AAAA,EACP;AACD,SAAO;AACT;AAUO,MAAM,cAAc,CAAC,UAAU,CAAC,CAAC,SAAS,OAAO,UAAU,YAAY,wBAAwB;AAC/F,MAAM,cAAc,CAAC,UAAU;AACpC,QAAM,YAAYI,cAAc,KAAK;AACrC,SAAO,eAAe,WAAW,sBAAsB;AAAA,IACrD,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,EACd,CAAG;AACD,SAAO;AACT;ACnCa,QAAQ,WAAW,GAAG;AAEnC,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AACA,SAAS,sBAAsBC,MAAK,UAAU,IAAI;AAChD,MAAIA,gBAAe;AACV,WAAAA;AACH,QAAA,OAAO,aAAaA,IAAG;AACzB,MAAA,CAACA,QAAO,CAAC;AACJ,WAAA;AACL,MAAA,MAAM,QAAQ,IAAI;AACpB,WAAO,KAAK,IAAI,CAAC,MAAM,sBAAsB,GAAG,OAAO,CAAC;AACtD,MAAA,OAAO,SAAS,UAAU;AAC5B,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;AACnC,YAAI,MAAM,mBAAmB,EAAE,WAAW,IAAI;AAC5C,iBAAO,CAAC,GAAG,MAAM,CAAC,CAAC;AACrB,eAAO,CAAC,GAAG,sBAAsB,GAAG,CAAC,CAAC;AAAA,MAAA,CACvC;AAAA,IAAA;AAAA,EAEL;AACO,SAAA;AACT;AAE4B,iBAAiB;AAAA,EAC3C,OAAO;AAAA,IACL,mBAAmB,SAAS,KAAK;AAC/B,iBAAWC,UAAS,IAAI;AAChB,QAAAA,OAAA,gBAAgB,sBAAsBA,OAAM,KAAK;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;AAED,MAAM,aAAa;AA2BnB,MAAM,UAAU,OAAO,eAAe,cAAc,aAAsD,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAA;AACxL,MAAMC,cAAY;AAClB,SAAS,wBAAwB,SAAS;AACxC,UAAQA,WAAS,IAAI;AACvB;AACA,SAAS,aAAa;AACpB,MAAIA,eAAa,SAAS;AACjB,WAAA,QAAQA,WAAS;EAC1B;AACM,QAAA,OAAO,OAAO,UAAU;AAC9B,MAAI,CAAC,QAAQ,QAAQ,IAAI,aAAa;AACpC,YAAQ,KAAK,kGAAkG;AACjH,SAAO,QAAQ;AACjB;AC7EoC,CAAC,WAAW,EAAE,OAAO,KAAI,CAAE,CAAC;ACGhE,MAAA,oBAAe,iCAAiB;AAAA,EAC9B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM,SAAS;AACb,UAAM,OAA4B,QAAQ,WAAW;AAGrD;AAAA;AAAA,MAEE,MAAM,WAAU,EAAG,OAAO,SAAS,SAAS;AAAA,IAClD;AACI,YAAQ,OAAO,IAAI,IAAI;AAAA,EAqBxB;AACH,CAAC;ACrCD,SAAS,cAAc,OAAO,IAAI;AAC5B,MAAA;AACJ,MAAI,cAAc;AACZ,QAAA,gBAAgB,CAAC,aAAa;AAC9B,QAAA,mBAAmB,oBAAoB,UAAU;AAC7C,YAAA,IAAI,MAAM,kBAAkB;AAAA,IACpC;AAAA,EAAA;AAEE,MAAA;AACJ,MAAI,KAAK,cAAc;AACf,UAAA,qBAAqB,KAAK,qBAAqB,WAAW;AAChE,QAAI,oBAAoB;AACtB,YAAM,IAAI;IAAmB,OACxB;AACL,cAAQ,KAAK,8CAA8C;AAAA,IAC7D;AAAA,EACF;AACA,QAAM,sBAAsB,MAAM;AAC5B,QAAA,OAAO,oBAAoB,QAAQ;AAC/B,YAAA,WAAW,IAAI;AACrB,UAAI,aAAa,QAAQ;AAChB,eAAA;AAAA,MACT;AAAA,IACF;AACO,WAAA;AAAA,EAAA;AAEF,SAAA;AAAA,IACL,KAAK,MAAM;AACT,YAAM,YAAY;AAClB,UAAI,cAAc,QAAQ;AAClB,cAAA,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AACO,aAAA;AAAA,IACT;AAAA,IACA,QAAQ,MAAM;AACZ,aAAO,oBAAoB;AAAA,IAC7B;AAAA,IACA,KAAK,CAAC,UAAU,YAAY;AAC1B,UAAI,CAAC,SAAS;AACZ,sBAAc,QAAQ;AAAA,MACxB;AACkB,wBAAA;AACJ,oBAAA;AAAA,IAChB;AAAA,IACA,OAAO,MAAM;AACO,wBAAA;AACJ,oBAAA;AAAA,IAChB;AAAA,IACA,MAAM,CAAC,UAAU,aAAa;AAC5B,oBAAc,QAAQ;AACJ,wBAAA;AACd,UAAA;AACF,eAAO,MAAM,IAAI,IAAI,UAAU,QAAQ,IAAI;MAAS,UACpD;AACA,YAAI,CAAC,aAAa;AACE,4BAAA;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,UAAU,UAAU,UAAU;AAChB,wBAAA;AAClB,YAAM,YAAY,MAAM;AACJ,0BAAA;AAAA,MAAA;AAEpB,YAAM,UAAU,MAAM,oBAAoB,WAAW,YAAY;AACjE,oBAAc,IAAI,OAAO;AACrB,UAAA;AACF,cAAM,IAAI,MAAM,IAAI,IAAI,UAAU,QAAQ,IAAI;AAC9C,YAAI,CAAC,aAAa;AACE,4BAAA;AAAA,QACpB;AACA,eAAO,MAAM;AAAA,MAAA,UACb;AACA,sBAAc,OAAO,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EAAA;AAEJ;AACA,SAAS,gBAAgB,cAAc,IAAI;AACzC,QAAM,WAAW,CAAA;AACV,SAAA;AAAA,IACL,IAAI,KAAK,OAAO,IAAI;AACd,UAAA,CAAC,SAAS,GAAG,GAAG;AACT,iBAAA,GAAG,IAAI,cAAc,EAAE,GAAG,aAAa,GAAG,MAAM;AAAA,MAC3D;AACA,eAAS,GAAG;AACZ,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EAAA;AAEJ;AACA,MAAM,cAAc,OAAO,eAAe,cAAc,aAAa,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAkD,CAAA;AAC5L,MAAM,YAAY;AACO,YAAY,SAAS,MAAM,YAAY,SAAS,IAAI,gBAAgB;AAG7F,MAAM,mBAAmB;AACzB,MAAM,gBAAgB,YAAY,gBAAgB,MAAM,YAAY,gBAAgB,wBAAwB,IAAI;AAChH,SAAS,aAAa,WAAW;AAC/B,QAAM,WAAW,CAAA;AACjB,aAAW,gBAAgB,eAAe;AACxC,UAAM,WAAW;AACjB,QAAI,UAAU;AACZ,eAAS,KAAK,QAAQ;AAAA,IACxB;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,eAAW,YAAY,UAAU;AACtB;IACX;AAAA,EAAA;AAEF,MAAI,YAAY;AAChB,MAAI,aAAa,OAAO,cAAc,YAAY,WAAW,WAAW;AAC1D,gBAAA,UAAU,MAAM,CAAC,UAAU;AAC7B;AACF,YAAA;AAAA,IAAA,CACP;AAAA,EACH;AACO,SAAA,CAAC,WAAW,OAAO;AAC5B;ACrHA,MAAM,kBAAkB,CAAC,OAAO,UAAU;AACxC,SAAO,MAAM,KAAK,QAAQ,oBAAoB,IAAI,EAAE,QAAQ,gBAAgB,IAAI,EAAE,QAAQ,SAAS,CAAC,MAAM;;AAAA,wBAAM,OAAO,EAAE,MAAM,CAAC,CAAC,MAAvB,mBAA0B,eAAc;AAAA,GAAE;AACtJ;AACO,MAAMC,qBAAmB,CAAC,YAAY,aAAa;AACxD,QAAM,eAAe,WAAW,MAAM,QAAQ,KAAK,CAAC;;AAAM,oBAAE,eAAF,mBAAc,aAAY,WAAW,UAAU;AAAA,GAAI;AAC7G,QAAM,SAAS,aAAY,6CAAc,KAAK,SAAQ,gBAAgB,gBAAgB,WAAW,OAAO,YAAY;AACpH,SAAO,OAAO,WAAW,aAAa,OAAO,WAAW,KAAK,IAAI;AACnE;AACO,MAAM,kBAAkB,CAAC,OAAO,aAAa;AAClD,SAAO,EAAE,SAAS,MAA0F;AAC9G;AACO,SAAS,QAAQ,OAAO;AAC7B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;ACaO,eAAe,cAAc,KAAK;AACf;AACtB,UAAM,qBAAqB;AAAA,MACzBC,aAAkB,EAAE,SAAQ,iCAAkB,GAAC,MAAM,WAAU,CAAE;AAAA,IACvE;AACI,WAAO,KAAK,CAAE,GAAE,GAAG,mBAAmB,SAAS,GAAG,EAAE,QAAO,CAAE;AAAA,EAC9D;AAYH;AClCA,MAAe,UAAA;AAAA,EACb;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,6BAAgD,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACpG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,0BAA6C,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACjG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,mCAA2D,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EAC/G;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,mCAA2D,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EAC/G;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,wCAAgE,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACpH;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,+CAAuE,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EAC3H;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,+BAAkD,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACtG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,8BAAiD,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACrG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,2BAA8C,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EAClG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,8BAAiD,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACrG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW,MAAM,OAAO,8BAAiD,EAAE,KAAK,CAAA,MAAK,EAAE,WAAW,CAAC;AAAA,EACrG;AACF;AChEO,MAAM,UAAU,CAAC,WAAW,OAAO,UAAU;AAClD,UAAQ,UAAU,OAAO,CAAA,IAAK;AAC9B,SAAO,EAAE,SAAS;;AAAM,mBAAQ,EAAE,WAAW,OAAO,KAAK,KAAI,WAAM,YAAN;AAAA,IAAiB;AAChF;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,UAAS,+BAAO,KAAK,QAAO,MAAM,KAAK,QAAQ,oBAAoB,IAAI,EAAE,QAAQ,gBAAgB,IAAI,EAAE,QAAQ,SAAS,CAAC,MAAM;;AAAA,wBAAM,OAAO,EAAE,MAAM,CAAC,CAAC,MAAvB,mBAA0B,eAAc;AAAA,GAAE;AAC/K,SAAO,OAAO,WAAW,aAAa,OAAO,KAAK,IAAI;AACxD;AACO,SAAS,eAAe,IAAI,MAAM;AACvC,MAAI,OAAO,QAAQ,SAAS,gBAAgB;AAC1C,WAAO;AAAA,EACR;AACD,MAAI,iBAAiB,EAAE,MAAM,iBAAiB,IAAI,GAAG;AACnD,WAAO;AAAA,EACR;AACD,QAAM,oBAAoB,GAAG,QAAQ;AAAA,IACnC,CAAC,MAAM,UAAK;;AAAK,kBAAK,cAAc,KAAK,WAAW,cAAY,gBAAK,QAAQ,KAAK,MAAlB,mBAAqB,eAArB,mBAAiC;AAAA;AAAA,EACrG;AACE,MAAI,mBAAmB;AACrB,WAAO;AAAA,EACR;AACD,SAAO;AACT;ACrBA,MAAe,iBAAA;AAAA,EACb,eAAe,IAAI,MAAM,eAAe;;AACtC,UAAM,UAAU;AAChB,UAAM,aAAW,eAAA,EAAY,YAAZ,mBAAqB,uBAAsB;AAC5D,QAAI,WAAW,iBAAiB;AAChC,UAAM,yBAAyB,OAAO,GAAG,KAAK,gBAAgB,aAAa,GAAG,KAAK,YAAY,IAAI,IAAI,IAAI,GAAG,KAAK;AAC/G,QAAA,CAAC,YAAY,QAAQ,MAAM,2BAA2B,SAAS,eAAe,IAAI,IAAI,GAAG;AAC3F,iBAAW,EAAE,MAAM,GAAG,KAAK,EAAE;AAAA,IAC/B;AACI,QAAA,GAAG,SAAS,KAAK,MAAM;AACzB,UAAI,KAAK,QAAQ,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,MAAM,GAAG,KAAK,EAAE;AAAA,MAC3B;AACA,UAAI,GAAG,MAAM;AACJ,eAAA,EAAE,IAAI,GAAG,MAAM,KAAK,+BAA+B,GAAG,IAAI,GAAG;MACtE;AACO,aAAA;AAAA,IACT;AACA,UAAM,gBAAgB,CAAC,UAAU,CAAC,EAAE,MAAM,KAAK,kBAAkBC;AACjE,UAAM,aAAa,cAAc,IAAI,KAAK,cAAc,EAAE,IAAI,2BAA2B;AAClF,WAAA,IAAI,QAAQ,CAAC,YAAY;AACtB,cAAA,MAAM,SAAS,YAAY,YAAY;AAC7C,cAAM,IAAI,QAAQ,CAAC,aAAa,WAAW,UAAU,CAAC,CAAC;AACvD,YAAI,GAAG,MAAM;AACA,qBAAA,EAAE,IAAI,GAAG,MAAM,KAAK,+BAA+B,GAAG,IAAI,GAAG;QAC1E;AACA,gBAAQ,QAAQ;AAAA,MAAA,CACjB;AAAA,IAAA,CACF;AAAA,EACH;AACF;AACA,SAAS,+BAA+B,UAAU;AAC5C,MAAA;AACI,UAAA,OAAgB,SAAA,cAAc,QAAQ;AAC5C,QAAI,MAAM;AACR,aAAO,OAAO,WAAW,iBAAiB,IAAI,EAAE,eAAe,IAAI,OAAO,WAAW,iBAA0B,SAAA,eAAe,EAAE,gBAAgB;AAAA,IAClJ;AAAA,EAAA,QACM;AAAA,EACR;AACO,SAAA;AACT;AC3CA,MAAM,sBAAsB;AAAA,EAC1B,UAAU;AAAA,EACV,oBAAoB;AACtB;AACA,MAAe,gBAAA;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AACH;ACJE,MAAQ,WAAA,0CAAgB,OAAA,OAAA;;AAAA,MAAA,QAAA;AACtB,MAAA,GAAA,QAAA,SAAA,mBAAA,WAAA;AACF;AAAA,EACA;AACyB,aAAA;AACJ,YAAA;AACrB,kBAAqB,CAAA,QAAA,SAAA,IAAAC,aAAA,MAAA,QAAA,QAAA,GAAA,KAAA,SAAA,EAAA,CAAA,CAAA,GAAA,SAAA,MAAA,QAAA,aAAA;AACnB,MAAA,WAAA,MAAA;AACF;AAAA,EACA;AACS;AACT,WAAA;AAAA,EACA;;ACZA,MAAA,0BAA4C,0CAAA,OAAA,OAAA;AAC1C;AACF;AAAA,EACA;;ACLK,MAAM,mBAAmB;AAAA,EAC9B;AAAA,EACA;AACF;AACO,MAAM,kBAAkB,CAAA;ACsBC,MACxB,SAAA,iCAAA;AAAA,EACN,MAAS;AAAA,EACT;EACM,MAAA,MAAA,SAA8B;;AAAA,QAAA,QAAA;AAClC,QAAI,kDAAsC,IAAA;AAC1B,QAAA,cAAA,YAAA,CAAA,WAAA,SAAA,GAAA,GAAA;AAChB,oBAAA;AAAA,IACA;AACA,UAAM,YAAS,mBAAuB,YAAvB,uCAAuB,gBAAY,oBAAA,UAAA;AAC9C,UAAA,WAAA,mBAAA,WAAA,uCAAA,aAAA;AACJ,QAAA;AAA4B,UACvB,SAAAC,eAAA;AAAA,MACH,GAAgB;AAAA,MACd,gBAA6B,CAAA,IAAA,MAAA,kBAAA;AACX,YAAA,SAAA,gBAAA;AAChB,0BAAA;AACF;AAAA,QACA;AACS,YAAA,8BAAyB;AAC5B,iBAAA,QAAA,+BAAuC;AACnC,cAAA,uBAAe,SAAiB,SAAA;AAC9B,kBAAA,QAAA,OAAA,WAAA,MAAA;AACN;AACD,cAAA,SAAA,QAAA,oBAAA;AAAA,YAAA,CACH;AAAA,UACA;AACF,iBAAA,cAAA,eAAA,IAAA,gBAAA,iBAAA,aAAA;AAAA,QACF;AAAA,MACA;AAAA,MACA;AAAA,MACD;AAAA,IACD,CAAA;AAIA,YAAsB,OAAA,IAAA,MAAA;AACf,UAAA,gBAAW,WAAc,OAAA,aAAA,KAAA;AAC9B,WAAA,UAAsB,CAAA,KAAA,SAAA;AACvB,oBAAA,QAAA;AAAA,IACD,CAAA;AACE,WAAK,eAAoB,QAAA,OAAA,OAAA,kBAAA,iBAAA;AAAA,MAC1B,KAAA,MAAA,cAAA;AAAA,IACD,CAAM;AACN,UAAM,aAAoB,QAAO,WAAA;AACjC,UAAM,oBAAyB,OAAA,aAAA,KAAA;AACtB,UAAA,mBAA4B,MAAA;AACrC,aAAA,QAAA,OAAA,aAAA;AAAA,IACA;AACO,YAAA,KAAA,eAAwB,gBAAA;AACzB,WAAA,UAAW,CAAC,IAAG,SAAA;;AACA,YAAAC,OAAAV,MAAA,GAAA,QAAA,CAAA,MAAA,gBAAAA,IAAA,eAAA,gBAAAU,IAAA,eAAAC,OAAAC,MAAA,KAAA,QAAA,CAAA,MAAA,gBAAAA,IAAA,eAAA,gBAAAD,IAAA,UAAA;AACnB;MACD;AAAA,IACD,CAAA;AACW,UAAA,QAAA,CAAA;AACF,eAAA,OAAA,cAA2B;AAAA,aAC3B,eAAa,OAAS,KAAA;AAAA,QAC5B,KAAA,MAAA,OAAA,MAAA,GAAA;AAAA,MAAA,CACH;AAAA,IACQ;AACA,YAAA,SAAA,gBAAqC,KAAA;AAAA,0BAClC,QAAA,eAAA;AAAA,MACT,QAAQ,CAAA;AAAA,MACV,OAAA,CAAA;AAAA,IACA;AACmC,aAAY;AAC7C,QAAA,GAAA,aAAiB,eAAjB,mBAAiB,gBAA8B;AAC7C,aAAA,UAAe,OAAA,IAAA,OAAA,YAAA;AACf,eAA+D,QAAA;AAIvD,YAAA,SAAA;AACR,gBAAA,QAAA,SAAA,kBAAA;AAAA,QAC0B;AACxB,aAAA,mCAAA,UAAA,GAAA;AACF;AAAA,QACI;AACF,YAAA,GAAA,QAAc,WAAA,GAAA;AAA2C,gBAC3C,QAAA,eAAA,MAAA,UAAAE,cAAA;AAAA,YACZ,YAAO;AAAA,YACP,OAAA;AAAA,YACA,eAAM,mBAAA,GAAA,QAAA;AAAA,YAAA;cAEN,MAAA,GAAA;AAAA,YACC;AAAA,UAC4B,CAAA,CAAA,CAAA;AAAA,QAC/B,gCAA6B,GAAA,aAAiB,YAAG;AACnD,gBAAA,QAAA,eAAA,MAAA,WAAA,GAAA,YAAA,GAAA,CAAA;AAAA,QACD;AAAA,MAAA,CACH;AAAA,IACI;AACF,QAAA;AACQ,UAAA,MAAA;AACR;AAAA,QAAA,CAAA,QAAA,SAAA,IAAAL,aAAA,MAAA,OAAA,KAAA,UAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA;AAAA;;eAEe,SAAA,IAAAA,aAAA,MAAA,OAAA,QAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA;AAAA,IACf,SAAA,QAAA;AACF,MAAA,CAAA,QAAA,SAAA,IAAAA,aAAA,MAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA,IACA;AACiB,UAAA,uBAAA,OAAA,aAAA;AACS;AACxB,SAAA,aAAS,eAAT,mBAAoB,eAAS;AAC/B,aAAA,EAAA,SAAA,EAAA,OAAA;IACM;AACC,UAAA,gBAAkB,QAAa,QAAA,MAAA;AAC9B,WAAA,kBAAiB,IAAoB,SAAA;;AACxC,YAAA,QAAgB,SAAO,oBAAA;AACtB,SAAA,OAAA;AACF,UAAA,QAAiB,eAAA,iBAAA,CAAA,WAAA,GAAA,KAAA,MAAA,GAAA;AACnB,WAAA,KAAA,SAAA;AAAA,MACA;AAC0B,cAAS,wBAA2B;AACtD,UAAA,GAAAR,MAAA,QAAA,eAAA,gBAAAA,IAAA,gBAAwC;AACnC,cAAA,oBAAyB,oBAAA,IAAA,CAAA,GAAA,kBAAA,GAAA,QAAA,YAAA,MAAA,CAAA;AAC5B,mBAAA,aAAA,GAAA;AACN,gBAA0B,sBAAA,UAAA,KAAA;AACxB,cAAA,CAAA,qBAAA;AACF;AAAA,UACW;AACT,qBAAAG,UAAkB,QAAS,mBAAA,GAAA;AAC7B,8BAAA,IAAAA,MAAA;AAAA,UACF;AAAA,QACA;;AAEE,6BAA8B,MAAA,QAAA,eAAA,MAAA,cAAA,GAAA,IAAA,CAAA;AACjB,cAAA,WAAA;AACL,uBAAA,OAAyB,WAAA,eAAM;AACjC,kBAAA,WAAA,cAAyB,GAAA,GAAA;AACpB,kCAAA,IAAA,GAAA;AAAA,cACL,OAAA;AACF,kCAAA,OAAA,GAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACA;AACQ,mBAAAA,UAAA,mBAA8B;AACpC,gBAAiB,aAAA,OAAAA,WAAA,WAAA,QAAA,YAAA,MAAAA,MAAA,KAAA,QAAAO,MAAA,gBAAAP,YAAA,gBAAAO,IAAA,sBAAA,KAAA,CAAA,MAAA,EAAA,WAAA,MAAAP;AACf,cAAA,CAAA,YAAqB;AAIvB,kBAAA,IAAA,MAAA,8BAAAA,MAAA,IAAA;AAAA,UACM;AACN,gBAAkF,SAAA,MAAA,QAAA,eAAA,MAAA,WAAA,IAAA,IAAA,CAAA;AAC5E;AACI,gBAAA,WAAA,2BAA+B,OAAA;AAAA,oBACvB,SAAA,UAAAU,cAAA;AAAA,gBACZ,YAAA;AAAA,gBACD,eAAA,mBAAA,UAAA;AAAA,cACD,CAAA;AACO,oBAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA;AACT,qBAAA;AAAA,YACF;AAAA,UACA;AACE,cAAA,WAAA,MAAA;AACF;AAAA,UACI;AACK,cAAA,UAAA,WAAA,OAAA;AACT,mBAAA;AAAA,UACF;AAAA,QACF;AAAA,MACD;AAAA,IACD,CAAA;AACE,WAAA,QAAe,YAAA;AACT,aAAA;AACP,YAAA,QAAA,SAAA,kBAAA;AAAA,IACD,CAAQ;AACF,YAAA,MAAA,SAAA,eAAA,YAAA;AACF,UAAA;AACE,YAAA,UAAA,sBAA4B;AAC9B,+BAAA,OAAA;AAAA,QACA;AAAqB,cAChB,OAAA,QAAA;AAAA,UACH,GAAO;AAAA,UACR,OAAA;AAAA,QACD,CAAO;eACA,QAAQ,iBAAA,cAAA;AAAA,MACf,SAAc,QAAA;AAChB,cAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA;AAAA,MACD;AAAA,IACD,CAAA;AACF,WAAA,EAAA,SAAA,EAAA,OAAA;EACD;AACD,CAAe;ACtGC,SAAA,qBAAqB,MAAM,QAAQ;AACzB;AACtB,eAAa,EAAA,WAAW,iBAAiB,IAAI,IAAI;AAAA,EACnD;AACF;AC9GA,MAAM,WAAW;AAAA,EACf,WAAW,CAAC,SAAS,YAAY,IAAI,KAAK,KAAK,OAAQ;AAAA,EACvD,iBAAiB,CAAC,SAAS,MAAM,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,KAAK,UAAU,OAAO,KAAK,UAAU,WAAW,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK;AAAA,EACnJ,UAAU,CAAC,SAAS,MAAM,IAAI,KAAK,CAAC,KAAK,UAAU,OAAO,KAAK,UAAU,WAAW,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK;AAAA,EACzH,YAAY,CAAC,SAAS,MAAM,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK;AAAA,EAC7D,iBAAiB,CAAC,SAAS,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI;AAAA,EAC5E,KAAK,CAAC,SAAS,MAAM,IAAI,KAAK,KAAK;AAAA,EACnC,UAAU,CAAC,SAAS,WAAW,IAAI,KAAK,MAAM,IAAI;AACpD;AAIA,MAAA,mCAAe,iCAAiB;AAAA,EAC9B,MAAM;AAAA,EACN,QAAQ;AACN,eAAW,WAAW,UAAU;AAC9B,2BAAqB,SAAS,SAAS,OAAO,CAAC;AAAA,IAChD;AAAA,EACF;AACH,CAAC;ACtBD,MAAA,+BAAe,iCAAiB;AAAA,EAC9B,MAAM;AACR,CAAC;ACAD,MAAe,UAAA;AAAA,EACb;AAAA,EACAC;AAAAA,EACA;AAAA,EACA;AACF;ACPO,MAAM,gBAAgB,gBAAgB;AAAA,EAC3C,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,EACjB;AAAA,EACD,MAAM,OAAO;AACX,UAAM,cAAc,MAAM;AAC1B,UAAM,gBAAgB,MAAM;AAC5B,UAAM,QAAQ,CAAA;AACd,eAAW,OAAO,MAAM,OAAO;AAC7B,aAAO,eAAe,OAAO,KAAK;AAAA,QAChC,KAAK,MAAM,gBAAgB,MAAM,YAAY,MAAM,MAAM,GAAG,IAAI,cAAc,GAAG;AAAA,MACzF,CAAO;AAAA,IACF;AACD,YAAQ,iBAAiB,gBAAgB,KAAK,CAAC;AAY/C,WAAO,MAAM;AAKX,aAAO,EAAE,MAAM,OAAO,EAAE,KAAK,MAAM,SAAQ,CAAE;AAAA,IACnD;AAAA,EACG;AACH,CAAC;ACnCD,MAAA,qBAAe,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,IACP;AAAA,IACD,YAAY;AAAA,MACV,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACP;AAAA,IACD,SAAS;AAAA,MACP,MAAM,CAAC,UAAU,MAAM;AAAA,MACvB,SAAS;AAAA,IACV;AAAA,EACF;AAAA,EACD,MAAM,OAAO,EAAE,OAAO,OAAO,OAAM,GAAI;AACrC,UAAM,UAAU;AAChB,UAAM,UAAU;AAChB,UAAM,YAAY,OAAO,iBAAiB,IAAI;AAC9C,QAAI;AACJ,WAAO,EAAE,QAAO,CAAE;AACE,WAAO,kBAAkB,IAAI;AACjD,QAAI;AACJ,UAAM,OAAO,QAAQ;AAKrB,QAAI,MAAM,SAAS;AACjB,YAAM,MAAM,MAAM,SAAS,CAAC,MAAM,SAAS;AACzC,YAAI,SAAS,MAAM;AACjB,kBAAQ,SAAS,oBAAoB;AAAA,QACtC;AAAA,MACT,CAAO;AAAA,IACF;AAID,WAAO,MAAM;AACX,aAAO,EAAE,YAAY,EAAE,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,GAAG,MAAK,GAAI;AAAA,QACvE,SAAS,CAAC,eAAe;AAGvB,cAAI,CAAC,WAAW,WAAW;AAIzB;AACA;AAAA,UACD;AAUD,gBAAM,MAAMT,mBAAiB,YAAY,MAAM,OAAO;AACtD,cAAI,CAAC,QAAQ,eAAe,CAAC,kBAAkB,WAAW,WAAW,OAAO,WAAW,SAAS,KAAK,oBAAoB,KAAK;AAC5H,oBAAQ,SAAS,kBAAkB;AAAA,UACpC;AACD,4BAAkB;AAClB,gBAAM,gBAAgB,CAAC,EAAE,MAAM,cAAc,WAAW,MAAM,KAAK,kBAAkBE;AACrF,gBAAM,kBAAkB,iBAAiB,sBAAsB;AAAA,YAC7D,MAAM;AAAA,YACN,WAAW,MAAM,KAAK;AAAA,YACtBA;AAAAA,YACA,EAAE,cAAc,MAAM;AACpB,sBAAQ,SAAS,0BAA0B,WAAW,SAAS;AAAA,YAC7E,EAAe;AAAA,UACf,EAAY,OAAO,OAAO,CAAC;AACjB,gBAAM,kBAAkB,MAAM,aAAa,WAAW,MAAM,KAAK,aAAaQ;AAC9E,kBAAQ;AAAA,YACN;AAAA,YACA,iBAAiB;AAAA,YACjB;AAAA,cACE;AAAA,cACA,EAAE,UAAU;AAAA,gBACV,aAAa;AAAA,gBACb,WAAW,MAAM,QAAQ,SAAS,cAAc,WAAW,SAAS;AAAA,gBACpE,WAAW,MAAM;AACf,2BAAS,MAAM,QAAQ,SAAS,eAAe,WAAW,SAAS,EAAE,KAAK,MAAM,QAAQ,SAAS,kBAAkB,CAAC,EAAE,QAAQ,IAAI,CAAC;AAAA,gBACpI;AAAA,cACjB,GAAiB;AAAA,gBACD,SAAS,MAAM;AACb,wBAAM,gBAAgB,EAAE,eAAe;AAAA,oBACrC,KAAK,OAAO;AAAA,oBACZ,OAAO,MAAM,UAAU,EAAE,UAAU,QAAQ,MAAM,QAAQ,UAAU,CAAC,IAAI,WAAW;AAAA,oBACnF,OAAO,WAAW;AAAA,oBAClB,WAAW,OAAO;AAAA,oBAClB,gBAAgB;AAAA,oBAChB,UAAU;AAAA,kBAC9B,CAAmB;AAID,yBAAO;AAAA,gBACR;AAAA,cACjB,CAAe;AAAA,YACF;AAAA,UACF,EAAC,QAAO;AACT,iBAAO;AAAA,QACR;AAAA,MACT,CAAO;AAAA,IACP;AAAA,EACG;AACH,CAAC;AACD,SAAS,sBAAsB,YAAY;AACzC,QAAM,SAAS,WAAW,IAAI,CAAC,UAAU;AAAA,IACvC,GAAG;AAAA,IACH,cAAc,KAAK,eAAe,QAAQ,KAAK,YAAY,IAAI;AAAA,EAChE,EAAC;AACF,SAAO,KAAK,GAAG,MAAM;AACvB;AAaA,SAAS,kBAAkB,MAAM,UAAU,WAAW;AACpD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACR;AACD,QAAM,QAAQ,SAAS,QAAQ,UAAU,CAAC;;AAAM,oBAAE,eAAF,mBAAc,cAAY,uCAAW;AAAA,GAAI;AACzF,SAAO,QAAQ,SAAS,QAAQ,SAAS;AAC3C;;;;;;;;;;;ACxJA,QAAA,OAAAC,eAAA,MAAA,CAAA,GAAA;AAAA,QAAAC,mBAAA,qBAAA,MAAA,MAAA,OAAA,CAAA;;;;;;;;;;;;;;;;;ACOA,UAAM,QAAQ;AAKd,UAAM,SAAS,MAAM;AAGF,WAAO,QACtB,OAAO,MACN,MAAM,IAAI,EACV,OAAO,CAAC,EACR,IAAI,CAAC,SAAS;AACb,YAAM,OAAO,KACV,QAAQ,aAAa,EAAE,EACvB,QAAQ,QAAQ,KAAK,EACrB,KAAM;AACT,aAAO;AAAA,QACL;AAAA,QACA,UAAW,KAAK,SAAS,cAAc,KAAK,CAAC,KAAK,SAAS,QAAQ,KACjE,KAAK,SAAS,UAAU,KACxB,KAAK,SAAS,aAAa;AAAA,MAC9B;AAAA,IACP,CAAK,EAAE,IAAI,OAAK,qBAAqB,EAAE,WAAW,cAAc,EAAE,KAAK,EAAE,IAAI,SAAS,EAAE,KAAK,IAAI,IAC7F;AAGJ,UAAM,aAAa,OAAO,OAAO,cAAc,GAAG;AAClD,UAAM,QAAQ,eAAe;AAE7B,UAAM,gBAAgB,OAAO,kBAAkB,QAAQ,mBAAmB;AAC1E,UAAM,cAAc,OAAO,WAAW,OAAO,SAAU;AACvD,UAAM,QAAuF;AAG7F,UAAM,YAAY,qBAAqB,MAAM,OAAO,+BAAiB,EAAE,KAAK,OAAK,EAAE,WAAW,CAAC,CAAC;AAChG,UAAM,SAEF,qBAAqB,MAAM,OAAO,+BAAiB,EAAE,KAAK,OAAK,EAAE,WAAW,CAAC,CAAC;AAElF,UAAM,gBAAgB,QAAQ,YAAY;;;;;;;;;;;;;;;;ACjB1C,UAAM,iBAEF,MAAM;AAEV,UAAM,UAAU;AACE,YAAQ,eAAe;AAMR,YAAQ,WAAW;AACpD,UAAM,iBAAiB;AAIf,YAAA,iBAAiB,UAAU;AAGnB,YAAQ,MAAM,aAAa,CAAA,UAAS,MAAM,IAAI,CAAQ,SAAA,KAAM,CAAA,GAAG,WAAW;AAM1F,UAAM,QAAQ;AAEd,UAAM,cAAoC,MAAM,SAAS,CAAC,QAAQ,WAAW;AAC7D,oBAAA,CAAC,KAAK,QAAQ,SAAS;AACrC,cAAQ,MAAM,SAAS,aAAa,KAAK,QAAQ,IAAI,EAAE,MAAM,CAAa,cAAA,QAAQ,MAAM,oCAAoC,SAAS,CAAC;AACxD;AAC5E,cAAM,IAAI,QAAQ,eAAe,MAAM,UAAU,GAAG,CAAC;AACrD,yBAAiB,MAAM,CAAC;AACjB,eAAA;AAAA,MACT;AAAA,IAAA,CACD;AAGK,UAAA,gBAAsC,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5D/D,IAAI;AACoB;AACd,UAAA,eAAe,oBAAoB,YAAY;AAC/C,UAAA,SAAS,UAAUC,SAAa;AACtC,UAAM,OAAO,cAAc,EAAE,QAAQ,WAAY,CAAA;AAC7C,QAAA;AACI,YAAA,aAAa,MAAM,OAAO;AAChC,YAAM,KAAK,MAAM,SAAS,eAAe,MAAM;AAAA,aACxC,OAAO;AACd,YAAM,KAAK,MAAM,SAAS,aAAa,KAAK;AAC5C,WAAK,QAAQ,QAAQ,KAAK,QAAQ,SAAS,YAAY,KAAK;AAAA,IAC9D;AACA,QAAI,yCAAY,iBAAiB;AACzB,YAAA,IAAI,MAAM,iBAAiB;AAAA,IACnC;AACO,WAAA;AAAA,EAAA;AAEX;AA4CA,MAAA,UAAe,CAAC,eAAe,MAAM,UAAU;", "x_google_ignoreList": [2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 16, 17, 19, 20, 21, 24, 25, 27, 28, 29]}