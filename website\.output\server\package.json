{"name": "nuxt-app-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/parser": "7.25.3", "@unhead/dom": "1.9.16", "@unhead/shared": "1.9.16", "@unhead/ssr": "1.9.16", "@vue/compiler-core": "3.4.38", "@vue/compiler-dom": "3.4.38", "@vue/compiler-ssr": "3.4.38", "@vue/devtools-api": "6.6.3", "@vue/reactivity": "3.4.38", "@vue/runtime-core": "3.4.38", "@vue/runtime-dom": "3.4.38", "@vue/server-renderer": "3.4.38", "@vue/shared": "3.4.38", "devalue": "5.0.0", "entities": "4.5.0", "estree-walker": "2.0.2", "hookable": "5.5.3", "source-map-js": "1.2.0", "ufo": "1.5.4", "unhead": "1.9.16", "vue": "3.4.38", "vue-bundle-renderer": "2.1.0", "vue-router": "4.4.3"}}