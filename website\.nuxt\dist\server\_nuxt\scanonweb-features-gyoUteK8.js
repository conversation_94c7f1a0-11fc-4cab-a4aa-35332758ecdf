import { _ as __nuxt_component_0 } from "./nuxt-link-2X8I7ISh.js";
import { u as useHead } from "./index-BabADJUJ.js";
import { mergeProps, withCtx, createTextVNode, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderComponent } from "vue/server-renderer";
import { _ as _sfc_main$1, a as _sfc_main$2 } from "./Footer-C3PwX65Z.js";
import { _ as _export_sfc } from "../server.mjs";
import "ufo";
import "@unhead/shared";
import "#internal/nuxt/paths";
import "ofetch";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "vue-router";
import "radix3";
import "defu";
import "devalue";
const _sfc_main = {
  __name: "scanonweb-features",
  __ssrInlineRender: true,
  setup(__props) {
    useHead({
      title: "ScanOnWeb 技术特性表格 - 全面的平台支持和功能规格",
      meta: [
        {
          name: "description",
          content: "ScanOnWeb扫描控件完整技术特性表格，包含平台兼容性、扫描协议支持、国产化平台支持、多架构CPU支持等详细规格信息。"
        },
        {
          name: "keywords",
          content: "ScanOnWeb,技术特性,平台支持,国产化,LoongArch,鲲鹏960,统信UOS,麒麟操作系统,TWAIN,SANE,扫描协议"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-b2103ab5>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-b2103ab5><div class="bg-white border-b border-gray-200" data-v-b2103ab5><div class="container mx-auto px-4 py-4" data-v-b2103ab5><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-b2103ab5>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`首页`);
          } else {
            return [
              createTextVNode("首页")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b2103ab5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-b2103ab5></path></svg>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`文档资料`);
          } else {
            return [
              createTextVNode("文档资料")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b2103ab5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-b2103ab5></path></svg><span class="text-gray-900 font-medium" data-v-b2103ab5>ScanOnWeb 技术特性</span></nav></div></div><div class="bg-white py-8" data-v-b2103ab5><div class="container mx-auto px-4" data-v-b2103ab5><div class="max-w-6xl" data-v-b2103ab5><h1 class="heading-primary mb-4" data-v-b2103ab5>ScanOnWeb 技术特性表格</h1><p class="text-lg text-gray-600 mb-6" data-v-b2103ab5> 全面了解ScanOnWeb扫描控件的技术规格、平台支持和功能特性，包含独有的国产化平台支持 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-b2103ab5><div class="flex items-center" data-v-b2103ab5><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-b2103ab5><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-b2103ab5></path></svg><span data-v-b2103ab5>国产化平台支持</span></div><div class="flex items-center" data-v-b2103ab5><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-b2103ab5><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-b2103ab5></path></svg><span data-v-b2103ab5>多架构CPU支持</span></div><div class="flex items-center" data-v-b2103ab5><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-b2103ab5><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-b2103ab5></path></svg><span data-v-b2103ab5>全面技术规格</span></div></div></div></div></div><div class="container mx-auto py-8 px-4" data-v-b2103ab5><div class="card-business p-8 mb-8" data-v-b2103ab5><h2 class="heading-secondary mb-6 text-center" data-v-b2103ab5>平台兼容性概览</h2><div class="overflow-x-auto" data-v-b2103ab5><table class="w-full border-collapse" data-v-b2103ab5><thead data-v-b2103ab5><tr class="bg-orange-500 text-white" data-v-b2103ab5><th class="border border-orange-600 px-4 py-3 text-left font-semibold" data-v-b2103ab5> 支持的客户端操作系统 </th><th class="border border-orange-600 px-4 py-3 text-left font-semibold" data-v-b2103ab5> 扫描技术 </th><th class="border border-orange-600 px-4 py-3 text-left font-semibold" data-v-b2103ab5> 支持的浏览器 </th><th class="border border-orange-600 px-4 py-3 text-left font-semibold" data-v-b2103ab5> 支持的框架 </th><th class="border border-orange-600 px-4 py-3 text-left font-semibold" data-v-b2103ab5> 服务器端支持 </th></tr></thead><tbody data-v-b2103ab5><tr class="bg-white hover:bg-gray-50" data-v-b2103ab5><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-2" data-v-b2103ab5><div class="font-medium text-gray-900" data-v-b2103ab5> Windows 7/8/10/11 </div><div class="text-sm text-gray-600" data-v-b2103ab5>32位和64位</div></div></td><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="feature-tag" data-v-b2103ab5>TWAIN 1.9+</div><div class="feature-tag" data-v-b2103ab5>WIA 2.0</div><div class="feature-tag" data-v-b2103ab5>eSCL</div></div></td><td class="border border-gray-300 px-4 py-3" rowspan="3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="browser-tag" data-v-b2103ab5>Chrome 68+</div><div class="browser-tag" data-v-b2103ab5>Firefox 68+</div><div class="browser-tag" data-v-b2103ab5>Edge 79+</div><div class="browser-tag" data-v-b2103ab5>IE 11</div><div class="browser-tag" data-v-b2103ab5>Safari 7+</div></div></td><td class="border border-gray-300 px-4 py-3" rowspan="3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="framework-tag" data-v-b2103ab5>Vue.js</div><div class="framework-tag" data-v-b2103ab5>React</div><div class="framework-tag" data-v-b2103ab5>Angular</div><div class="framework-tag" data-v-b2103ab5>原生JavaScript</div><div class="framework-tag" data-v-b2103ab5>jQuery</div></div></td><td class="border border-gray-300 px-4 py-3" rowspan="3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="server-tag" data-v-b2103ab5>Java Spring Boot</div><div class="server-tag" data-v-b2103ab5>ASP.NET Core</div><div class="server-tag" data-v-b2103ab5>PHP</div><div class="server-tag" data-v-b2103ab5>Node.js</div><div class="server-tag" data-v-b2103ab5>Python</div><div class="server-tag" data-v-b2103ab5>Go</div><div class="server-tag" data-v-b2103ab5>Rust</div></div></td></tr><tr class="bg-blue-50 hover:bg-blue-100" data-v-b2103ab5><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-2" data-v-b2103ab5><div class="font-medium text-blue-900" data-v-b2103ab5>macOS 10.15+</div><div class="text-sm text-blue-700" data-v-b2103ab5> x86-64, ARM64 (M1/M2) </div></div></td><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="feature-tag" data-v-b2103ab5>TWAIN 1.9</div><div class="feature-tag" data-v-b2103ab5>ICA</div><div class="feature-tag" data-v-b2103ab5>eSCL</div></div></td></tr><tr class="bg-green-50 hover:bg-green-100" data-v-b2103ab5><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-2" data-v-b2103ab5><div class="font-medium text-green-900" data-v-b2103ab5>Linux</div><div class="text-sm text-green-700" data-v-b2103ab5><div data-v-b2103ab5>• Ubuntu 12.04+, Debian 8+</div><div data-v-b2103ab5>• Fedora 24+, CentOS 7+</div><div class="font-semibold text-orange-600" data-v-b2103ab5> • 统信UOS, 麒麟操作系统 </div></div><div class="text-sm font-semibold text-green-800" data-v-b2103ab5><div data-v-b2103ab5>架构支持：</div><div data-v-b2103ab5>• x86-64, ARM64</div><div class="text-orange-600" data-v-b2103ab5>• LoongArch (龙芯)</div><div class="text-orange-600" data-v-b2103ab5>• 华为鲲鹏960</div><div data-v-b2103ab5>• MIPS</div></div></div></td><td class="border border-gray-300 px-4 py-3" data-v-b2103ab5><div class="space-y-1" data-v-b2103ab5><div class="feature-tag" data-v-b2103ab5>SANE</div><div class="feature-tag" data-v-b2103ab5>eSCL</div></div></td></tr></tbody></table></div><div class="mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-orange-200 rounded-lg p-6" data-v-b2103ab5><h3 class="font-bold text-orange-800 mb-3 flex items-center" data-v-b2103ab5><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" data-v-b2103ab5><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zm8 0a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1h-6a1 1 0 01-1-1v-6z" clip-rule="evenodd" data-v-b2103ab5></path></svg> 国产化平台支持优势 </h3><div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-orange-700" data-v-b2103ab5><div data-v-b2103ab5><h4 class="font-semibold mb-2" data-v-b2103ab5>操作系统支持</h4><ul class="space-y-1 text-sm" data-v-b2103ab5><li data-v-b2103ab5>• 统信UOS桌面版和服务器版</li><li data-v-b2103ab5>• 银河麒麟桌面版和服务器版</li><li data-v-b2103ab5>• 中科方德桌面操作系统</li><li data-v-b2103ab5>• 深度Deepin操作系统</li></ul></div><div data-v-b2103ab5><h4 class="font-semibold mb-2" data-v-b2103ab5>CPU架构支持</h4><ul class="space-y-1 text-sm" data-v-b2103ab5><li data-v-b2103ab5>• 龙芯LoongArch 64位架构</li><li data-v-b2103ab5>• 华为鲲鹏960 ARM64架构</li><li data-v-b2103ab5>• 飞腾Phytium ARM64架构</li><li data-v-b2103ab5>• 海光x86-64架构</li></ul></div></div></div></div><div class="card-business p-8 mb-8" data-v-b2103ab5><h2 class="heading-secondary mb-6" data-v-b2103ab5>详细技术特性</h2><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>图像采集和文档扫描</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>TWAIN协议支持</td><td class="feature-value" data-v-b2103ab5> 支持TWAIN 1.9及以上规范；WIA 2.0 </td><td class="feature-platform" data-v-b2103ab5>Windows客户端</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>macOS扫描支持</td><td class="feature-value" data-v-b2103ab5>支持TWAIN 1.9规范；ICA兼容</td><td class="feature-platform" data-v-b2103ab5>macOS客户端</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>Linux扫描支持</td><td class="feature-value" data-v-b2103ab5>SANE兼容；支持国产操作系统</td><td class="feature-platform" data-v-b2103ab5>Linux客户端</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>网络扫描支持</td><td class="feature-value" data-v-b2103ab5>eSCL兼容；支持网络扫描仪</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>RESTful API</td><td class="feature-value" data-v-b2103ab5>支持RESTful API接口</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>高容量扫描</td><td class="feature-value" data-v-b2103ab5> 可选磁盘缓存机制，支持数千页扫描 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>自动进纸器(ADF)</td><td class="feature-value" data-v-b2103ab5>内置ADF支持和多图像采集</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>双面扫描</td><td class="feature-value" data-v-b2103ab5>支持双面扫描模式</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>空白页检测</td><td class="feature-value" data-v-b2103ab5>支持空白页检测功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>智能向导模式</td><td class="feature-value" data-v-b2103ab5>内置向导模式智能管理TWAIN状态</td><td class="feature-platform" data-v-b2103ab5>Windows</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>扫描参数设置</td><td class="feature-value" data-v-b2103ab5> 支持分辨率、像素类型、位深度、亮度、对比度、页面大小等参数设置 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像传输模式</td><td class="feature-value" data-v-b2103ab5> 提供原生和磁盘文件图像传输模式 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>缓冲内存传输</td><td class="feature-value" data-v-b2103ab5>缓冲内存传输模式</td><td class="feature-platform" data-v-b2103ab5>Windows客户端</td></tr></tbody></table></div></div><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>图像编辑功能</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像编辑器</td><td class="feature-value" data-v-b2103ab5>提供GUI和非GUI图像编辑器</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>基础编辑功能</td><td class="feature-value" data-v-b2103ab5> 内置旋转、裁剪、镜像、翻转、擦除、改变图像大小等功能 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图形标注</td><td class="feature-value" data-v-b2103ab5>支持添加彩色矩形到图像</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>文本标注</td><td class="feature-value" data-v-b2103ab5>支持文本注释功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像交换</td><td class="feature-value" data-v-b2103ab5>提供图像交换功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>区域处理</td><td class="feature-value" data-v-b2103ab5>支持清除指定区域并用颜色填充</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像缩放</td><td class="feature-value" data-v-b2103ab5>内置缩放功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>多选功能</td><td class="feature-value" data-v-b2103ab5>提供多图像选择功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像纠偏</td><td class="feature-value" data-v-b2103ab5>提供图像去倾斜功能</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>高级编辑</td><td class="feature-value" data-v-b2103ab5> 魔术棒选择、矩形选择、填白处理、马赛克处理、去黑边、去底色等 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>撤销操作</td><td class="feature-value" data-v-b2103ab5>支持UNDO撤销操作</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>鼠标交互</td><td class="feature-value" data-v-b2103ab5>鼠标滚轮缩放图像</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr></tbody></table></div></div><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>保存、上传和下载</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>HTTP/HTTPS传输</td><td class="feature-value" data-v-b2103ab5>通过HTTP/HTTPS下载/上传图像</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>FTP传输</td><td class="feature-value" data-v-b2103ab5> 通过FTP下载/上传图像（目前不支持FTPS） </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>多格式支持</td><td class="feature-value" data-v-b2103ab5> 保存和上传BMP、JPEG、PNG、TIFF和PDF文件 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>多页文档</td><td class="feature-value" data-v-b2103ab5>支持保存为多页TIFF和多页PDF</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>Base64编码</td><td class="feature-value" data-v-b2103ab5>支持将图像保存为base64字符串</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>Cookie和会话</td><td class="feature-value" data-v-b2103ab5>提供cookie和会话集成支持</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>上传绑定</td><td class="feature-value" data-v-b2103ab5>支持在上传过程中绑定cookie</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>压缩选项</td><td class="feature-value" data-v-b2103ab5> 提供RLE、G3、G4、JBIG2、LZW、PackBits、JPEG、JPEG2000和TIFF压缩选项 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>上传进度</td><td class="feature-value" data-v-b2103ab5> 上传进度对话框，可随时取消上传 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>流下载</td><td class="feature-value" data-v-b2103ab5>支持从HTTP流下载</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>内置编码器</td><td class="feature-value" data-v-b2103ab5> 内置JPEG、PNG、PDF和TIFF编码器，支持图像压缩 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>元数据上传</td><td class="feature-value" data-v-b2103ab5> 上传每个图像的元数据到服务器或数据库（SQL Server、MySQL、MS Access等） </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>分块上传</td><td class="feature-value" data-v-b2103ab5>数据量大时支持分块上传</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>多线程上传</td><td class="feature-value" data-v-b2103ab5>后台多线程上传</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr></tbody></table></div></div><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>本地文件处理</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>PDF文件支持</td><td class="feature-value" data-v-b2103ab5> 通过PDF光栅化SDK（附加模块），支持加载基于文本、图像和混合PDF文件 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>图像格式支持</td><td class="feature-value" data-v-b2103ab5> 支持加载本地JPEG、PNG、BMP、单页和多页TIFF文件 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>剪贴板支持</td><td class="feature-value" data-v-b2103ab5>从剪贴板加载DIB</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>数据格式支持</td><td class="feature-value" data-v-b2103ab5>从Base64和二进制数据加载图像</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr></tbody></table></div></div><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>安全特性</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>数据保护</td><td class="feature-value" data-v-b2103ab5> SDK意外退出时，所有缓存数据将被销毁 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>域名绑定</td><td class="feature-value" data-v-b2103ab5>许可过程中可选域名绑定</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>来源控制</td><td class="feature-value" data-v-b2103ab5>可配置为仅响应指定来源的请求</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>文件访问授权</td><td class="feature-value" data-v-b2103ab5>访问本地文件需要授权</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>设备访问授权</td><td class="feature-value" data-v-b2103ab5>访问扫描仪或摄像头需要授权</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>自定义证书</td><td class="feature-value" data-v-b2103ab5> 可自定义支持的证书以替代Dynamsoft默认证书 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>数字签名</td><td class="feature-value" data-v-b2103ab5>由VeriSign数字签名</td><td class="feature-platform" data-v-b2103ab5>Windows</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>代理支持</td><td class="feature-value" data-v-b2103ab5>支持代理连接</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>SSL支持</td><td class="feature-value" data-v-b2103ab5>支持SSL用于HTTP上传和下载</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>数据加密</td><td class="feature-value" data-v-b2103ab5> 扫描图像数据或缓存扫描数据已加密，只能由Dynamsoft扫描服务访问 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>设备访问限制</td><td class="feature-value" data-v-b2103ab5> 限制扫描仪设备仅供注册的Web扫描应用程序访问 </td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr></tbody></table></div></div><div class="feature-section" data-v-b2103ab5><h3 class="feature-section-title" data-v-b2103ab5>集成和兼容性</h3><div class="overflow-x-auto" data-v-b2103ab5><table class="feature-table" data-v-b2103ab5><tbody data-v-b2103ab5><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>Citrix环境</td><td class="feature-value" data-v-b2103ab5>支持Citrix环境部署</td><td class="feature-platform" data-v-b2103ab5>Windows</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>Electron支持</td><td class="feature-value" data-v-b2103ab5>支持Electron跨平台应用</td><td class="feature-platform" data-v-b2103ab5>全平台</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>国产化环境</td><td class="feature-value" data-v-b2103ab5> 完全支持统信UOS、麒麟操作系统等国产化环境 </td><td class="feature-platform" data-v-b2103ab5>Linux</td></tr><tr data-v-b2103ab5><td class="feature-name" data-v-b2103ab5>多架构CPU</td><td class="feature-value" data-v-b2103ab5> 支持LoongArch、华为鲲鹏960、飞腾等国产CPU架构 </td><td class="feature-platform" data-v-b2103ab5>Linux</td></tr></tbody></table></div></div></div><div class="card-business p-8 mb-8" data-v-b2103ab5><h2 class="heading-secondary mb-6" data-v-b2103ab5>生产力优势</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-8" data-v-b2103ab5><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>大幅减少开发时间</h3><p class="productivity-desc" data-v-b2103ab5> 从零开始熟悉TWAIN规范并构建TWAIN插件可能需要数月时间。使用ScanOnWeb，您可以在几天内实现TWAIN功能。对于大多数应用程序，您只需几行代码即可实现TWAIN支持。 </p></div><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>浏览器兼容性保障</h3><p class="productivity-desc" data-v-b2103ab5> 各大浏览器厂商不断更新其浏览器版本。您可以放心，我们会持续更新ScanOnWeb以保持兼容性，确保您的应用程序能够持续稳定运行。 </p></div><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>更大的时间节省</h3><p class="productivity-desc" data-v-b2103ab5> 通过我们的专业技术支持服务，使用ScanOnWeb构建扫描解决方案所需的总时间可以从数月缩短到数天。 </p></div><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>缩短产品上市时间</h3><p class="productivity-desc" data-v-b2103ab5> 产品交付速度至关重要。ScanOnWeb可以显著减少开发时间，让您的产品更快进入市场。 </p></div><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>国产化支持优势</h3><p class="productivity-desc" data-v-b2103ab5> 作为国内唯一成熟稳定的扫描控件产品，ScanOnWeb在国产化环境下具有独特优势，支持信创项目和政府采购需求。 </p></div><div class="productivity-item" data-v-b2103ab5><h3 class="productivity-title" data-v-b2103ab5>多年行业经验</h3><p class="productivity-desc" data-v-b2103ab5> 经过多年发展，ScanOnWeb已被广泛应用于税务、公安、建筑、银行等多个行业，积累了丰富的实践经验和稳定的技术基础。 </p></div></div></div><div class="card-business p-8 text-center" data-v-b2103ab5><h2 class="heading-secondary mb-4" data-v-b2103ab5>准备开始使用？</h2><p class="text-lg text-gray-600 mb-6" data-v-b2103ab5> 立即体验ScanOnWeb的强大功能，开始您的扫描应用开发之旅 </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-b2103ab5>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/download",
        class: "btn-primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 免费下载试用 `);
          } else {
            return [
              createTextVNode(" 免费下载试用 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/contact",
        class: "btn-secondary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 联系销售咨询 `);
          } else {
            return [
              createTextVNode(" 联系销售咨询 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/demo",
        class: "btn-outline"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 在线演示体验 `);
          } else {
            return [
              createTextVNode(" 在线演示体验 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/docs/scanonweb-features.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const scanonwebFeatures = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-b2103ab5"]]);
export {
  scanonwebFeatures as default
};
//# sourceMappingURL=scanonweb-features-gyoUteK8.js.map
