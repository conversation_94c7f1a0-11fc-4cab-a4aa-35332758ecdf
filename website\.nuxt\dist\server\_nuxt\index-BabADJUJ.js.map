{"version": 3, "file": "index-BabADJUJ.js", "sources": ["../../../../node_modules/@unhead/vue/dist/shared/vue.f36acd1f.mjs", "../../../../node_modules/@unhead/vue/dist/index.mjs"], "sourcesContent": ["import { ref, watchEffect, watch, getCurrentInstance, onBeforeUnmount, onDeactivated, onActivated } from 'vue';\nimport { i as injectHead, r as resolveUnrefHeadInput } from './vue.cf295fb1.mjs';\n\nfunction useHead(input, options = {}) {\n  const head = options.head || injectHead();\n  if (head) {\n    if (!head.ssr)\n      return clientUseHead(head, input, options);\n    return head.push(input, options);\n  }\n}\nfunction clientUseHead(head, input, options = {}) {\n  const deactivated = ref(false);\n  const resolvedInput = ref({});\n  watchEffect(() => {\n    resolvedInput.value = deactivated.value ? {} : resolveUnrefHeadInput(input);\n  });\n  const entry = head.push(resolvedInput.value, options);\n  watch(resolvedInput, (e) => {\n    entry.patch(e);\n  });\n  const vm = getCurrentInstance();\n  if (vm) {\n    onBeforeUnmount(() => {\n      entry.dispose();\n    });\n    onDeactivated(() => {\n      deactivated.value = true;\n    });\n    onActivated(() => {\n      deactivated.value = false;\n    });\n  }\n  return entry;\n}\n\nexport { useHead as u };\n", "import { useScript as useScript$1 } from 'unhead';\nexport { CapoPlugin, HashHydrationPlugin, createHeadCore } from 'unhead';\nimport { V as Vue3, h as headSymbol, i as injectHead } from './shared/vue.cf295fb1.mjs';\nexport { c as createHead, a as createServerHead, r as resolveUnrefHeadInput, s as setHeadInjectionHandler } from './shared/vue.cf295fb1.mjs';\nimport { getCurrentInstance, ref, onMounted } from 'vue';\nimport { u as useHead } from './shared/vue.f36acd1f.mjs';\nimport { composableNames, whitelistSafeInput, unpackMeta } from '@unhead/shared';\n\nconst VueHeadMixin = {\n  created() {\n    let source = false;\n    if (Vue3) {\n      const instance = getCurrentInstance();\n      if (!instance)\n        return;\n      const options = instance.type;\n      if (!options || !(\"head\" in options))\n        return;\n      source = typeof options.head === \"function\" ? () => options.head.call(instance.proxy) : options.head;\n    } else {\n      const head = this.$options.head;\n      if (head) {\n        source = typeof head === \"function\" ? () => head.call(this) : head;\n      }\n    }\n    source && useHead(source);\n  }\n};\n\nconst Vue2ProvideUnheadPlugin = function(_Vue, head) {\n  _Vue.mixin({\n    beforeCreate() {\n      const options = this.$options;\n      const origProvide = options.provide;\n      options.provide = function() {\n        let origProvideResult;\n        if (typeof origProvide === \"function\")\n          origProvideResult = origProvide.call(this);\n        else\n          origProvideResult = origProvide || {};\n        return {\n          ...origProvideResult,\n          [headSymbol]: head\n        };\n      };\n    }\n  });\n};\n\nconst coreComposableNames = [\n  \"injectHead\"\n];\nconst unheadVueComposablesImports = {\n  \"@unhead/vue\": [...coreComposableNames, ...composableNames]\n};\n\nfunction useHeadSafe(input, options = {}) {\n  return useHead(input, { ...options, transform: whitelistSafeInput });\n}\n\nfunction useSeoMeta(input, options) {\n  const { title, titleTemplate, ...meta } = input;\n  return useHead({\n    title,\n    titleTemplate,\n    // @ts-expect-error runtime type\n    _flatMeta: meta\n  }, {\n    ...options,\n    transform(t) {\n      const meta2 = unpackMeta({ ...t._flatMeta });\n      delete t._flatMeta;\n      return {\n        // @ts-expect-error runtime type\n        ...t,\n        meta: meta2\n      };\n    }\n  });\n}\n\nfunction useServerHead(input, options = {}) {\n  const head = options.head || injectHead();\n  delete options.head;\n  if (head)\n    return head.push(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerHeadSafe(input, options = {}) {\n  return useHeadSafe(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerSeoMeta(input, options) {\n  return useSeoMeta(input, { ...options || {}, mode: \"server\" });\n}\n\nfunction useScript(_input, _options) {\n  const input = typeof _input === \"string\" ? { src: _input } : _input;\n  const head = injectHead();\n  const options = _options || {};\n  options.head = head;\n  options.eventContext = getCurrentInstance();\n  const status = ref(\"awaitingLoad\");\n  const stubOptions = options.stub;\n  options.stub = ({ script, fn }) => {\n    script.status = status;\n    if (fn === \"$script\")\n      return script;\n    return stubOptions?.({ script, fn });\n  };\n  let instance;\n  const _ = head.hooks.hook(\"script:updated\", ({ script }) => {\n    if (instance && script.id === instance.$script.id) {\n      status.value = script.status;\n      script.status === \"removed\" && _();\n    }\n  });\n  const scope = getCurrentInstance();\n  if (scope && !options.trigger)\n    options.trigger = onMounted;\n  instance = useScript$1(input, options);\n  return instance;\n}\n\nexport { Vue2ProvideUnheadPlugin, VueHeadMixin, injectHead, unheadVueComposablesImports, useHead, useHeadSafe, useScript, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };\n"], "names": [], "mappings": ";;;AAGA,SAAS,QAAQ,OAAO,UAAU,IAAI;AACpC,QAAM,OAAO,QAAQ,QAAQ,WAAU;AACvC,MAAI,MAAM;AACR,QAAI,CAAC,KAAK;AACR,aAAO,cAAc,MAAM,OAAO,OAAO;AAC3C,WAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAChC;AACH;AACA,SAAS,cAAc,MAAM,OAAO,UAAU,CAAA,GAAI;AAChD,QAAM,cAAc,IAAI,KAAK;AAC7B,QAAM,gBAAgB,IAAI,CAAA,CAAE;AAC5B,cAAY,MAAM;AAChB,kBAAc,QAAQ,YAAY,QAAQ,CAAA,IAAK,sBAAsB,KAAK;AAAA,EAC9E,CAAG;AACD,QAAM,QAAQ,KAAK,KAAK,cAAc,OAAO,OAAO;AACpD,QAAM,eAAe,CAAC,MAAM;AAC1B,UAAM,MAAM,CAAC;AAAA,EACjB,CAAG;AACU,qBAAqB;AAYhC,SAAO;AACT;ACeA,MAAM,sBAAsB;AAAA,EAC1B;AACF;AAAA,CACoC;AAAA,EAClC,eAAe,CAAC,GAAG,qBAAqB,GAAG,eAAe;AAC5D;", "x_google_ignoreList": [0, 1]}