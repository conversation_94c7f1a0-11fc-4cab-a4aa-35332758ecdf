import{_ as e,o as a,c as t,j as s}from"./Dy7juzJL.js";const d={class:"gaopaiyi-demo"},l=s('<div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-9e9ef8b0><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-9e9ef8b0> 高拍仪演示 </h2><div class="p-8 bg-gray-100 rounded mb-6 text-center" data-v-9e9ef8b0><svg class="w-24 h-24 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg><h3 class="text-xl font-semibold mt-4 mb-2" data-v-9e9ef8b0>GaoPaiYi 高拍仪功能演示</h3><p class="text-gray-600 mb-4" data-v-9e9ef8b0> 该功能需要安装高拍仪控件并连接高拍仪设备才能使用。 </p><button class="btn bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition-colors" data-v-9e9ef8b0> 下载高拍仪控件 </button></div><div class="mb-8" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0>什么是高拍仪？</h3><p class="text-gray-700 mb-4" data-v-9e9ef8b0> 高拍仪是一种特殊的图像采集设备，它主要由高清摄像头、LED补光灯、可调支架等部件组成，可以快速将纸质文档、证件等转换为电子图像。与普通扫描仪相比，高拍仪具有拍摄速度快、操作简便的特点，广泛应用于办公、教学、银行、政务等场景。 </p><div class="bg-blue-50 p-4 rounded border border-blue-200 mt-4" data-v-9e9ef8b0><h4 class="font-semibold text-blue-700 mb-2" data-v-9e9ef8b0> 高拍仪与普通扫描仪的区别 </h4><div class="grid grid-cols-1 md:grid-cols-2 gap-4" data-v-9e9ef8b0><div data-v-9e9ef8b0><h5 class="font-medium mb-2" data-v-9e9ef8b0>高拍仪优势：</h5><ul class="list-disc list-inside text-sm text-gray-700" data-v-9e9ef8b0><li data-v-9e9ef8b0>速度快，无需预热，即开即用</li><li data-v-9e9ef8b0>可以采集立体物品图像</li><li data-v-9e9ef8b0>操作简便，安装使用方便</li><li data-v-9e9ef8b0>体积小，占用空间少</li><li data-v-9e9ef8b0>实时预览，所见即所得</li></ul></div><div data-v-9e9ef8b0><h5 class="font-medium mb-2" data-v-9e9ef8b0>适用场景：</h5><ul class="list-disc list-inside text-sm text-gray-700" data-v-9e9ef8b0><li data-v-9e9ef8b0>办公文档快速电子化</li><li data-v-9e9ef8b0>身份证、证书等证件采集</li><li data-v-9e9ef8b0>教学演示和物体展示</li><li data-v-9e9ef8b0>银行柜台业务办理</li><li data-v-9e9ef8b0>公安、政务窗口资料录入</li></ul></div></div></div></div><div class="mb-8" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0> GaoPaiYi控件功能 </h3><div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-v-9e9ef8b0><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" data-v-9e9ef8b0></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>设备控制</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> 支持设备选择、分辨率调整、对焦控制、亮度对比度调节等 </p></div><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-green-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>图像处理</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> 支持自动裁剪、旋转、翻转、锐化、去底色等图像优化处理 </p></div><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-purple-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>数据上传</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> 支持多种格式保存、上传至服务器、集成到业务系统等功能 </p></div></div></div><div class="border-t pt-6" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0>开发资源</h3><div class="flex flex-wrap gap-4" data-v-9e9ef8b0><a href="https://www.brainysoft.cn" target="_blank" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" data-v-9e9ef8b0></path></svg> 官方网站 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-9e9ef8b0></path></svg> 开发文档 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" data-v-9e9ef8b0></path></svg> SDK下载 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg> 代码示例 </a></div></div></div>',1),o=[l],i={__name:"GaoPaiYiDemo",setup(r){return(v,n)=>(a(),t("div",d,o))}},h=e(i,[["__scopeId","data-v-9e9ef8b0"]]);export{h as default};
