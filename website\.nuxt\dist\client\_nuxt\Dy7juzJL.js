const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./XfRs8NHu.js","./L-3nxwpR.js","./DYzyhJ5q.js","./contact.B5JARLiN.css","./Co61pfxM.js","./demo.CFZrKK-x.css","./D4VnDwTm.js","./Cp9XtYH-.js","./scanonweb-api.BRG4jYWy.css","./Bcgw38WA.js","./scanonweb-faq.CVAKN5_o.css","./VF4cvea4.js","./scanonweb-features.lonBaYeP.css","./DoxFx5l-.js","./scanonweb-getting-started.CY2ikeOm.css","./B04A3j4W.js","./documents.DqXxXzM1.css","./C1WhqXxa.js","./Ru65YT6T.js","./CY8I3eQZ.js","./products.CDhbi0Rm.css","./4-5J9pYw.js","./purchase.C3zoN31D.css","./q5UI1IJe.js","./error-404.CjGVuf6H.css","./DcJ3jc3j.js","./error-500.DFBAsgKS.css"])))=>i.map(i=>d[i]);
/**
* @vue/shared v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Is(e,t){const n=new Set(e.split(","));return r=>n.has(r)}const fe={},Kt=[],$e=()=>{},Qc=()=>!1,In=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ms=e=>e.startsWith("onUpdate:"),be=Object.assign,Hs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Xc=Object.prototype.hasOwnProperty,te=(e,t)=>Xc.call(e,t),J=Array.isArray,Wt=e=>on(e)==="[object Map]",br=e=>on(e)==="[object Set]",po=e=>on(e)==="[object Date]",Yc=e=>on(e)==="[object RegExp]",Q=e=>typeof e=="function",de=e=>typeof e=="string",Ze=e=>typeof e=="symbol",le=e=>e!==null&&typeof e=="object",Ui=e=>(le(e)||Q(e))&&Q(e.then)&&Q(e.catch),Di=Object.prototype.toString,on=e=>Di.call(e),Zc=e=>on(e).slice(8,-1),Vi=e=>on(e)==="[object Object]",Ls=e=>de(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,qt=Is(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),vr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ea=/-(\w)/g,We=vr(e=>e.replace(ea,(t,n)=>n?n.toUpperCase():"")),ta=/\B([A-Z])/g,Lt=vr(e=>e.replace(ta,"-$1").toLowerCase()),wr=vr(e=>e.charAt(0).toUpperCase()+e.slice(1)),$r=vr(e=>e?`on${wr(e)}`:""),vt=(e,t)=>!Object.is(e,t),Gt=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ki=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},sr=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Wi=e=>{const t=de(e)?Number(e):NaN;return isNaN(t)?e:t};let go;const qi=()=>go||(go=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Er(e){if(J(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=de(r)?oa(r):Er(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(de(e)||le(e))return e}const na=/;(?![^(]*\))/g,ra=/:([^]+)/,sa=/\/\*[^]*?\*\//g;function oa(e){const t={};return e.replace(sa,"").split(na).forEach(n=>{if(n){const r=n.split(ra);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Rr(e){let t="";if(de(e))t=e;else if(J(e))for(let n=0;n<e.length;n++){const r=Rr(e[n]);r&&(t+=r+" ")}else if(le(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function ia(e){if(!e)return null;let{class:t,style:n}=e;return t&&!de(t)&&(e.class=Rr(t)),n&&(e.style=Er(n)),e}const la="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ca=Is(la);function Gi(e){return!!e||e===""}function aa(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=Tr(e[r],t[r]);return n}function Tr(e,t){if(e===t)return!0;let n=po(e),r=po(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=Ze(e),r=Ze(t),n||r)return e===t;if(n=J(e),r=J(t),n||r)return n&&r?aa(e,t):!1;if(n=le(e),r=le(t),n||r){if(!n||!r)return!1;const s=Object.keys(e).length,o=Object.keys(t).length;if(s!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!Tr(e[i],t[i]))return!1}}return String(e)===String(t)}function ua(e,t){return e.findIndex(n=>Tr(n,t))}const Ji=e=>!!(e&&e.__v_isRef===!0),fa=e=>de(e)?e:e==null?"":J(e)||le(e)&&(e.toString===Di||!Q(e.toString))?Ji(e)?fa(e.value):JSON.stringify(e,zi,2):String(e),zi=(e,t)=>Ji(t)?zi(e,t.value):Wt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[jr(r,o)+" =>"]=s,n),{})}:br(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>jr(n))}:Ze(t)?jr(t):le(t)&&!J(t)&&!Vi(t)?String(t):t,jr=(e,t="")=>{var n;return Ze(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ue;class Qi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ue,!t&&Ue&&(this.index=(Ue.scopes||(Ue.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Ue;try{return Ue=this,t()}finally{Ue=n}}}on(){Ue=this}off(){Ue=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function da(e){return new Qi(e)}function ha(e,t=Ue){t&&t.active&&t.effects.push(e)}function Xi(){return Ue}let Ot;class Ns{constructor(t,n,r,s){this.fn=t,this.trigger=n,this.scheduler=r,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,ha(this,s)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,Et();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(pa(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),Rt()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=yt,n=Ot;try{return yt=!0,Ot=this,this._runnings++,mo(this),this.fn()}finally{yo(this),this._runnings--,Ot=n,yt=t}}stop(){this.active&&(mo(this),yo(this),this.onStop&&this.onStop(),this.active=!1)}}function pa(e){return e.value}function mo(e){e._trackId++,e._depsLength=0}function yo(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Yi(e.deps[t],e);e.deps.length=e._depsLength}}function Yi(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let yt=!0,es=0;const Zi=[];function Et(){Zi.push(yt),yt=!1}function Rt(){const e=Zi.pop();yt=e===void 0?!0:e}function $s(){es++}function js(){for(es--;!es&&ts.length;)ts.shift()()}function el(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const r=e.deps[e._depsLength];r!==t?(r&&Yi(r,e),e.deps[e._depsLength++]=t):e._depsLength++}}const ts=[];function tl(e,t,n){$s();for(const r of e.keys()){let s;r._dirtyLevel<t&&(s??(s=e.get(r)===r._trackId))&&(r._shouldSchedule||(r._shouldSchedule=r._dirtyLevel===0),r._dirtyLevel=t),r._shouldSchedule&&(s??(s=e.get(r)===r._trackId))&&(r.trigger(),(!r._runnings||r.allowRecurse)&&r._dirtyLevel!==2&&(r._shouldSchedule=!1,r.scheduler&&ts.push(r.scheduler)))}js()}const nl=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},or=new WeakMap,It=Symbol(""),ns=Symbol("");function Pe(e,t,n){if(yt&&Ot){let r=or.get(e);r||or.set(e,r=new Map);let s=r.get(n);s||r.set(n,s=nl(()=>r.delete(n))),el(Ot,s)}}function st(e,t,n,r,s,o){const i=or.get(e);if(!i)return;let l=[];if(t==="clear")l=[...i.values()];else if(n==="length"&&J(e)){const c=Number(r);i.forEach((f,u)=>{(u==="length"||!Ze(u)&&u>=c)&&l.push(f)})}else switch(n!==void 0&&l.push(i.get(n)),t){case"add":J(e)?Ls(n)&&l.push(i.get("length")):(l.push(i.get(It)),Wt(e)&&l.push(i.get(ns)));break;case"delete":J(e)||(l.push(i.get(It)),Wt(e)&&l.push(i.get(ns)));break;case"set":Wt(e)&&l.push(i.get(It));break}$s();for(const c of l)c&&tl(c,4);js()}function ga(e,t){const n=or.get(e);return n&&n.get(t)}const ma=Is("__proto__,__v_isRef,__isVue"),rl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ze)),_o=ya();function ya(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=re(this);for(let o=0,i=this.length;o<i;o++)Pe(r,"get",o+"");const s=r[t](...n);return s===-1||s===!1?r[t](...n.map(re)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Et(),$s();const r=re(this)[t].apply(this,n);return js(),Rt(),r}}),e}function _a(e){Ze(e)||(e=String(e));const t=re(this);return Pe(t,"has",e),t.hasOwnProperty(e)}class sl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?Oa:cl:o?ll:il).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=J(t);if(!s){if(i&&te(_o,n))return Reflect.get(_o,n,r);if(n==="hasOwnProperty")return _a}const l=Reflect.get(t,n,r);return(Ze(n)?rl.has(n):ma(n))||(s||Pe(t,"get",n),o)?l:Ee(l)?i&&Ls(n)?l:l.value:le(l)?s?al(l):Nt(l):l}}class ol extends sl{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const c=wt(o);if(!tn(r)&&!wt(r)&&(o=re(o),r=re(r)),!J(t)&&Ee(o)&&!Ee(r))return c?!1:(o.value=r,!0)}const i=J(t)&&Ls(n)?Number(n)<t.length:te(t,n),l=Reflect.set(t,n,r,s);return t===re(s)&&(i?vt(r,o)&&st(t,"set",n,r):st(t,"add",n,r)),l}deleteProperty(t,n){const r=te(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&st(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!Ze(n)||!rl.has(n))&&Pe(t,"has",n),r}ownKeys(t){return Pe(t,"iterate",J(t)?"length":It),Reflect.ownKeys(t)}}class ba extends sl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const va=new ol,wa=new ba,Ea=new ol(!0);const Fs=e=>e,Cr=e=>Reflect.getPrototypeOf(e);function jn(e,t,n=!1,r=!1){e=e.__v_raw;const s=re(e),o=re(t);n||(vt(t,o)&&Pe(s,"get",t),Pe(s,"get",o));const{has:i}=Cr(s),l=r?Fs:n?Ds:wn;if(i.call(s,t))return l(e.get(t));if(i.call(s,o))return l(e.get(o));e!==s&&e.get(t)}function Fn(e,t=!1){const n=this.__v_raw,r=re(n),s=re(e);return t||(vt(e,s)&&Pe(r,"has",e),Pe(r,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function Bn(e,t=!1){return e=e.__v_raw,!t&&Pe(re(e),"iterate",It),Reflect.get(e,"size",e)}function bo(e,t=!1){!t&&!tn(e)&&!wt(e)&&(e=re(e));const n=re(this);return Cr(n).has.call(n,e)||(n.add(e),st(n,"add",e,e)),this}function vo(e,t,n=!1){!n&&!tn(t)&&!wt(t)&&(t=re(t));const r=re(this),{has:s,get:o}=Cr(r);let i=s.call(r,e);i||(e=re(e),i=s.call(r,e));const l=o.call(r,e);return r.set(e,t),i?vt(t,l)&&st(r,"set",e,t):st(r,"add",e,t),this}function wo(e){const t=re(this),{has:n,get:r}=Cr(t);let s=n.call(t,e);s||(e=re(e),s=n.call(t,e)),r&&r.call(t,e);const o=t.delete(e);return s&&st(t,"delete",e,void 0),o}function Eo(){const e=re(this),t=e.size!==0,n=e.clear();return t&&st(e,"clear",void 0,void 0),n}function Un(e,t){return function(r,s){const o=this,i=o.__v_raw,l=re(i),c=t?Fs:e?Ds:wn;return!e&&Pe(l,"iterate",It),i.forEach((f,u)=>r.call(s,c(f),c(u),o))}}function Dn(e,t,n){return function(...r){const s=this.__v_raw,o=re(s),i=Wt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,f=s[e](...r),u=n?Fs:t?Ds:wn;return!t&&Pe(o,"iterate",c?ns:It),{next(){const{value:a,done:d}=f.next();return d?{value:a,done:d}:{value:l?[u(a[0]),u(a[1])]:u(a),done:d}},[Symbol.iterator](){return this}}}}function ct(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ra(){const e={get(o){return jn(this,o)},get size(){return Bn(this)},has:Fn,add:bo,set:vo,delete:wo,clear:Eo,forEach:Un(!1,!1)},t={get(o){return jn(this,o,!1,!0)},get size(){return Bn(this)},has:Fn,add(o){return bo.call(this,o,!0)},set(o,i){return vo.call(this,o,i,!0)},delete:wo,clear:Eo,forEach:Un(!1,!0)},n={get(o){return jn(this,o,!0)},get size(){return Bn(this,!0)},has(o){return Fn.call(this,o,!0)},add:ct("add"),set:ct("set"),delete:ct("delete"),clear:ct("clear"),forEach:Un(!0,!1)},r={get(o){return jn(this,o,!0,!0)},get size(){return Bn(this,!0)},has(o){return Fn.call(this,o,!0)},add:ct("add"),set:ct("set"),delete:ct("delete"),clear:ct("clear"),forEach:Un(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=Dn(o,!1,!1),n[o]=Dn(o,!0,!1),t[o]=Dn(o,!1,!0),r[o]=Dn(o,!0,!0)}),[e,n,t,r]}const[Ta,Ca,Sa,xa]=Ra();function Bs(e,t){const n=t?e?xa:Sa:e?Ca:Ta;return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(te(n,s)&&s in r?n:r,s,o)}const Pa={get:Bs(!1,!1)},Aa={get:Bs(!1,!0)},ka={get:Bs(!0,!1)};const il=new WeakMap,ll=new WeakMap,cl=new WeakMap,Oa=new WeakMap;function Ia(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ma(e){return e.__v_skip||!Object.isExtensible(e)?0:Ia(Zc(e))}function Nt(e){return wt(e)?e:Us(e,!1,va,Pa,il)}function rt(e){return Us(e,!1,Ea,Aa,ll)}function al(e){return Us(e,!0,wa,ka,cl)}function Us(e,t,n,r,s){if(!le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=s.get(e);if(o)return o;const i=Ma(e);if(i===0)return e;const l=new Proxy(e,i===2?r:n);return s.set(e,l),l}function Jt(e){return wt(e)?Jt(e.__v_raw):!!(e&&e.__v_isReactive)}function wt(e){return!!(e&&e.__v_isReadonly)}function tn(e){return!!(e&&e.__v_isShallow)}function ul(e){return e?!!e.__v_raw:!1}function re(e){const t=e&&e.__v_raw;return t?re(t):e}function Ha(e){return Object.isExtensible(e)&&Ki(e,"__v_skip",!0),e}const wn=e=>le(e)?Nt(e):e,Ds=e=>le(e)?al(e):e;class fl{constructor(t,n,r,s){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ns(()=>t(this._value),()=>Xn(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=r}get value(){const t=re(this);return(!t._cacheable||t.effect.dirty)&&vt(t._value,t._value=t.effect.run())&&Xn(t,4),dl(t),t.effect._dirtyLevel>=2&&Xn(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function La(e,t,n=!1){let r,s;const o=Q(e);return o?(r=e,s=$e):(r=e.get,s=e.set),new fl(r,s,o||!s,n)}function dl(e){var t;yt&&Ot&&(e=re(e),el(Ot,(t=e.dep)!=null?t:e.dep=nl(()=>e.dep=void 0,e instanceof fl?e:void 0)))}function Xn(e,t=4,n,r){e=re(e);const s=e.dep;s&&tl(s,t)}function Ee(e){return!!(e&&e.__v_isRef===!0)}function _t(e){return hl(e,!1)}function En(e){return hl(e,!0)}function hl(e,t){return Ee(e)?e:new Na(e,t)}class Na{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:re(t),this._value=n?t:wn(t)}get value(){return dl(this),this._value}set value(t){const n=this.__v_isShallow||tn(t)||wt(t);t=n?t:re(t),vt(t,this._rawValue)&&(this._rawValue,this._rawValue=t,this._value=n?t:wn(t),Xn(this,4))}}function ue(e){return Ee(e)?e.value:e}const $a={get:(e,t,n)=>ue(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Ee(s)&&!Ee(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function pl(e){return Jt(e)?e:new Proxy(e,$a)}class ja{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return ga(re(this._object),this._key)}}class Fa{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function Ba(e,t,n){return Ee(e)?e:Q(e)?new Fa(e):le(e)&&arguments.length>1?Ua(e,t,n):_t(e)}function Ua(e,t,n){const r=e[t];return Ee(r)?r:new ja(e,t,n)}/**
* @vue/runtime-core v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function bt(e,t,n,r){try{return r?e(...r):e()}catch(s){ln(s,t,n)}}function je(e,t,n,r){if(Q(e)){const s=bt(e,t,n,r);return s&&Ui(s)&&s.catch(o=>{ln(o,t,n)}),s}if(J(e)){const s=[];for(let o=0;o<e.length;o++)s.push(je(e[o],t,n,r));return s}}function ln(e,t,n,r=!0){const s=t?t.vnode:null;if(t){let o=t.parent;const i=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const f=o.ec;if(f){for(let u=0;u<f.length;u++)if(f[u](e,i,l)===!1)return}o=o.parent}const c=t.appContext.config.errorHandler;if(c){Et(),bt(c,null,10,[e,i,l]),Rt();return}}Da(e,n,s,r)}function Da(e,t,n,r=!0){console.error(e)}let Rn=!1,rs=!1;const we=[];let Xe=0;const zt=[];let dt=null,Pt=0;const gl=Promise.resolve();let Vs=null;function cn(e){const t=Vs||gl;return e?t.then(this?e.bind(this):e):t}function Va(e){let t=Xe+1,n=we.length;for(;t<n;){const r=t+n>>>1,s=we[r],o=Tn(s);o<e||o===e&&s.pre?t=r+1:n=r}return t}function Sr(e){(!we.length||!we.includes(e,Rn&&e.allowRecurse?Xe+1:Xe))&&(e.id==null?we.push(e):we.splice(Va(e.id),0,e),ml())}function ml(){!Rn&&!rs&&(rs=!0,Vs=gl.then(yl))}function Ka(e){const t=we.indexOf(e);t>Xe&&we.splice(t,1)}function ss(e){J(e)?zt.push(...e):(!dt||!dt.includes(e,e.allowRecurse?Pt+1:Pt))&&zt.push(e),ml()}function Ro(e,t,n=Rn?Xe+1:0){for(;n<we.length;n++){const r=we[n];if(r&&r.pre){if(e&&r.id!==e.uid)continue;we.splice(n,1),n--,r()}}}function ir(e){if(zt.length){const t=[...new Set(zt)].sort((n,r)=>Tn(n)-Tn(r));if(zt.length=0,dt){dt.push(...t);return}for(dt=t,Pt=0;Pt<dt.length;Pt++){const n=dt[Pt];n.active!==!1&&n()}dt=null,Pt=0}}const Tn=e=>e.id==null?1/0:e.id,Wa=(e,t)=>{const n=Tn(e)-Tn(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function yl(e){rs=!1,Rn=!0,we.sort(Wa);try{for(Xe=0;Xe<we.length;Xe++){const t=we[Xe];t&&t.active!==!1&&bt(t,t.i,t.i?15:14)}}finally{Xe=0,we.length=0,ir(),Rn=!1,Vs=null,(we.length||zt.length)&&yl()}}let Te=null,xr=null;function lr(e){const t=Te;return Te=e,xr=e&&e.type.__scopeId||null,t}function Ng(e){xr=e}function $g(){xr=null}function _l(e,t=Te,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Lo(-1);const o=lr(t);let i;try{i=e(...s)}finally{lr(o),r._d&&Lo(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function jg(e,t){if(Te===null)return e;const n=kr(Te),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,l,c=fe]=t[s];o&&(Q(o)&&(o={mounted:o,updated:o}),o.deep&&mt(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function ze(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let c=l.dir[r];c&&(Et(),je(c,n,8,[e.el,l,e,t]),Rt())}}const ht=Symbol("_leaveCb"),Vn=Symbol("_enterCb");function qa(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ks(()=>{e.isMounted=!0}),Ws(()=>{e.isUnmounting=!0}),e}const Ne=[Function,Array],bl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ne,onEnter:Ne,onAfterEnter:Ne,onEnterCancelled:Ne,onBeforeLeave:Ne,onLeave:Ne,onAfterLeave:Ne,onLeaveCancelled:Ne,onBeforeAppear:Ne,onAppear:Ne,onAfterAppear:Ne,onAppearCancelled:Ne},vl=e=>{const t=e.subTree;return t.component?vl(t.component):t},Ga={name:"BaseTransition",props:bl,setup(e,{slots:t}){const n=Xs(),r=qa();return()=>{const s=t.default&&El(t.default(),!0);if(!s||!s.length)return;let o=s[0];if(s.length>1){for(const d of s)if(d.type!==ve){o=d;break}}const i=re(e),{mode:l}=i;if(r.isLeaving)return Fr(o);const c=To(o);if(!c)return Fr(o);let f=os(c,i,r,n,d=>f=d);Cn(c,f);const u=n.subTree,a=u&&To(u);if(a&&a.type!==ve&&!Ve(c,a)&&vl(n).type!==ve){const d=os(a,i,r,n);if(Cn(a,d),l==="out-in"&&c.type!==ve)return r.isLeaving=!0,d.afterLeave=()=>{r.isLeaving=!1,n.update.active!==!1&&(n.effect.dirty=!0,n.update())},Fr(o);l==="in-out"&&c.type!==ve&&(d.delayLeave=(g,b,E)=>{const k=wl(r,a);k[String(a.key)]=a,g[ht]=()=>{b(),g[ht]=void 0,delete f.delayedLeave},f.delayedLeave=E})}return o}}},Ja=Ga;function wl(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function os(e,t,n,r,s){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:c,onEnter:f,onAfterEnter:u,onEnterCancelled:a,onBeforeLeave:d,onLeave:g,onAfterLeave:b,onLeaveCancelled:E,onBeforeAppear:k,onAppear:H,onAfterAppear:y,onAppearCancelled:m}=t,_=String(e.key),R=wl(n,e),w=(O,I)=>{O&&je(O,r,9,I)},A=(O,I)=>{const q=I[1];w(O,I),J(O)?O.every(M=>M.length<=1)&&q():O.length<=1&&q()},U={mode:i,persisted:l,beforeEnter(O){let I=c;if(!n.isMounted)if(o)I=k||c;else return;O[ht]&&O[ht](!0);const q=R[_];q&&Ve(e,q)&&q.el[ht]&&q.el[ht](),w(I,[O])},enter(O){let I=f,q=u,M=a;if(!n.isMounted)if(o)I=H||f,q=y||u,M=m||a;else return;let K=!1;const ee=O[Vn]=ne=>{K||(K=!0,ne?w(M,[O]):w(q,[O]),U.delayedLeave&&U.delayedLeave(),O[Vn]=void 0)};I?A(I,[O,ee]):ee()},leave(O,I){const q=String(e.key);if(O[Vn]&&O[Vn](!0),n.isUnmounting)return I();w(d,[O]);let M=!1;const K=O[ht]=ee=>{M||(M=!0,I(),ee?w(E,[O]):w(b,[O]),O[ht]=void 0,R[q]===e&&delete R[q])};R[q]=e,g?A(g,[O,K]):K()},clone(O){const I=os(O,t,n,r,s);return s&&s(I),I}};return U}function Fr(e){if(Hn(e))return e=ot(e),e.children=null,e}function To(e){if(!Hn(e))return e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Q(n.default))return n.default()}}function Cn(e,t){e.shapeFlag&6&&e.component?Cn(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function El(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Ie?(i.patchFlag&128&&s++,r=r.concat(El(i.children,t,l))):(t||i.type!==ve)&&r.push(l!=null?ot(i,{key:l}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Mn(e,t){return Q(e)?be({name:e.name},t,{setup:e}):e}const Qt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function Co(e){Q(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,timeout:o,suspensible:i=!0,onError:l}=e;let c=null,f,u=0;const a=()=>(u++,c=null,d()),d=()=>{let g;return c||(g=c=t().catch(b=>{if(b=b instanceof Error?b:new Error(String(b)),l)return new Promise((E,k)=>{l(b,()=>E(a()),()=>k(b),u+1)});throw b}).then(b=>g!==c&&c?c:(b&&(b.__esModule||b[Symbol.toStringTag]==="Module")&&(b=b.default),f=b,b)))};return Mn({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return f},setup(){const g=me;if(f)return()=>Br(f,g);const b=y=>{c=null,ln(y,g,13,!r)};if(i&&g.suspense||Nn)return d().then(y=>()=>Br(y,g)).catch(y=>(b(y),()=>r?he(r,{error:y}):null));const E=_t(!1),k=_t(),H=_t(!!s);return s&&setTimeout(()=>{H.value=!1},s),o!=null&&setTimeout(()=>{if(!E.value&&!k.value){const y=new Error(`Async component timed out after ${o}ms.`);b(y),k.value=y}},o),d().then(()=>{E.value=!0,g.parent&&Hn(g.parent.vnode)&&(g.parent.effect.dirty=!0,Sr(g.parent.update))}).catch(y=>{b(y),k.value=y}),()=>{if(E.value&&f)return Br(f,g);if(k.value&&r)return he(r,{error:k.value});if(n&&!H.value)return he(n)}}})}function Br(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=he(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const Hn=e=>e.type.__isKeepAlive,za={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Xs(),r=n.ctx;if(!r.renderer)return()=>{const y=t.default&&t.default();return y&&y.length===1?y[0]:y};const s=new Map,o=new Set;let i=null;const l=n.suspense,{renderer:{p:c,m:f,um:u,o:{createElement:a}}}=r,d=a("div");r.activate=(y,m,_,R,w)=>{const A=y.component;f(y,m,_,0,l),c(A.vnode,y,m,_,A,l,R,y.slotScopeIds,w),_e(()=>{A.isDeactivated=!1,A.a&&Gt(A.a);const U=y.props&&y.props.onVnodeMounted;U&&Se(U,A.parent,y)},l)},r.deactivate=y=>{const m=y.component;ur(m.m),ur(m.a),f(y,d,null,1,l),_e(()=>{m.da&&Gt(m.da);const _=y.props&&y.props.onVnodeUnmounted;_&&Se(_,m.parent,y),m.isDeactivated=!0},l)};function g(y){Ur(y),u(y,n,l,!0)}function b(y){s.forEach((m,_)=>{const R=ps(m.type);R&&(!y||!y(R))&&E(_)})}function E(y){const m=s.get(y);m&&(!i||!Ve(m,i))?g(m):i&&Ur(i),s.delete(y),o.delete(y)}Yt(()=>[e.include,e.exclude],([y,m])=>{y&&b(_=>dn(y,_)),m&&b(_=>!dn(m,_))},{flush:"post",deep:!0});let k=null;const H=()=>{k!=null&&(us(n.subTree.type)?_e(()=>{s.set(k,Kn(n.subTree))},n.subTree.suspense):s.set(k,Kn(n.subTree)))};return Ks(H),Tl(H),Ws(()=>{s.forEach(y=>{const{subTree:m,suspense:_}=n,R=Kn(m);if(y.type===R.type&&y.key===R.key){Ur(R);const w=R.component.da;w&&_e(w,_);return}g(y)})}),()=>{if(k=null,!t.default)return null;const y=t.default(),m=y[0];if(y.length>1)return i=null,y;if(!xn(m)||!(m.shapeFlag&4)&&!(m.shapeFlag&128))return i=null,m;let _=Kn(m);if(_.type===ve)return i=null,_;const R=_.type,w=ps(Qt(_)?_.type.__asyncResolved||{}:R),{include:A,exclude:U,max:O}=e;if(A&&(!w||!dn(A,w))||U&&w&&dn(U,w))return i=_,m;const I=_.key==null?R:_.key,q=s.get(I);return _.el&&(_=ot(_),m.shapeFlag&128&&(m.ssContent=_)),k=I,q?(_.el=q.el,_.component=q.component,_.transition&&Cn(_,_.transition),_.shapeFlag|=512,o.delete(I),o.add(I)):(o.add(I),O&&o.size>parseInt(O,10)&&E(o.values().next().value)),_.shapeFlag|=256,i=_,us(m.type)?m:_}}},Qa=za;function dn(e,t){return J(e)?e.some(n=>dn(n,t)):de(e)?e.split(",").includes(t):Yc(e)?e.test(t):!1}function Xa(e,t){Rl(e,"a",t)}function Ya(e,t){Rl(e,"da",t)}function Rl(e,t,n=me){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Pr(t,r,n),n){let s=n.parent;for(;s&&s.parent;)Hn(s.parent.vnode)&&Za(r,t,n,s),s=s.parent}}function Za(e,t,n,r){const s=Pr(t,e,r,!0);Cl(()=>{Hs(r[t],s)},n)}function Ur(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Kn(e){return e.shapeFlag&128?e.ssContent:e}function Pr(e,t,n=me,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Et();const l=Ln(n),c=je(t,n,e,i);return l(),Rt(),c});return r?s.unshift(o):s.push(o),o}}const it=e=>(t,n=me)=>{(!Nn||e==="sp")&&Pr(e,(...r)=>t(...r),n)},eu=it("bm"),Ks=it("m"),tu=it("bu"),Tl=it("u"),Ws=it("bum"),Cl=it("um"),nu=it("sp"),ru=it("rtg"),su=it("rtc");function Sl(e,t=me){Pr("ec",e,t)}const xl="components";function Fg(e,t){return Al(xl,e,!0,t)||e}const Pl=Symbol.for("v-ndc");function ou(e){return de(e)?Al(xl,e,!1)||e:e||Pl}function Al(e,t,n=!0,r=!1){const s=Te||me;if(s){const o=s.type;{const l=ps(o,!1);if(l&&(l===t||l===We(t)||l===wr(We(t))))return o}const i=So(s[e]||o[e],t)||So(s.appContext[e],t);return!i&&r?o:i}}function So(e,t){return e&&(e[t]||e[We(t)]||e[wr(We(t))])}function Bg(e,t,n,r){let s;const o=n;if(J(e)||de(e)){s=new Array(e.length);for(let i=0,l=e.length;i<l;i++)s[i]=t(e[i],i,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let i=0;i<e;i++)s[i]=t(i+1,i,void 0,o)}else if(le(e))if(e[Symbol.iterator])s=Array.from(e,(i,l)=>t(i,l,void 0,o));else{const i=Object.keys(e);s=new Array(i.length);for(let l=0,c=i.length;l<c;l++){const f=i[l];s[l]=t(e[f],f,l,o)}}else s=[];return s}const is=e=>e?sc(e)?kr(e):is(e.parent):null,pn=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>is(e.parent),$root:e=>is(e.root),$emit:e=>e.emit,$options:e=>qs(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Sr(e.update)}),$nextTick:e=>e.n||(e.n=cn.bind(e.proxy)),$watch:e=>ku.bind(e)}),Dr=(e,t)=>e!==fe&&!e.__isScriptSetup&&te(e,t),iu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:c}=e;let f;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(Dr(r,t))return i[t]=1,r[t];if(s!==fe&&te(s,t))return i[t]=2,s[t];if((f=e.propsOptions[0])&&te(f,t))return i[t]=3,o[t];if(n!==fe&&te(n,t))return i[t]=4,n[t];ls&&(i[t]=0)}}const u=pn[t];let a,d;if(u)return t==="$attrs"&&Pe(e.attrs,"get",""),u(e);if((a=l.__cssModules)&&(a=a[t]))return a;if(n!==fe&&te(n,t))return i[t]=4,n[t];if(d=c.config.globalProperties,te(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return Dr(s,t)?(s[t]=n,!0):r!==fe&&te(r,t)?(r[t]=n,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==fe&&te(e,i)||Dr(t,i)||(l=o[0])&&te(l,i)||te(r,i)||te(pn,i)||te(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:te(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function xo(e){return J(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ls=!0;function lu(e){const t=qs(e),n=e.proxy,r=e.ctx;ls=!1,t.beforeCreate&&Po(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:c,inject:f,created:u,beforeMount:a,mounted:d,beforeUpdate:g,updated:b,activated:E,deactivated:k,beforeDestroy:H,beforeUnmount:y,destroyed:m,unmounted:_,render:R,renderTracked:w,renderTriggered:A,errorCaptured:U,serverPrefetch:O,expose:I,inheritAttrs:q,components:M,directives:K,filters:ee}=t;if(f&&cu(f,r,null),i)for(const X in i){const z=i[X];Q(z)&&(r[X]=z.bind(n))}if(s){const X=s.call(n,n);le(X)&&(e.data=Nt(X))}if(ls=!0,o)for(const X in o){const z=o[X],ge=Q(z)?z.bind(n,n):Q(z.get)?z.get.bind(n,n):$e,lt=!Q(z)&&Q(z.set)?z.set.bind(n):$e,Ge=Ke({get:ge,set:lt});Object.defineProperty(r,X,{enumerable:!0,configurable:!0,get:()=>Ge.value,set:Ce=>Ge.value=Ce})}if(l)for(const X in l)kl(l[X],r,n,X);if(c){const X=Q(c)?c.call(n):c;Reflect.ownKeys(X).forEach(z=>{Xt(z,X[z])})}u&&Po(u,e,"c");function D(X,z){J(z)?z.forEach(ge=>X(ge.bind(n))):z&&X(z.bind(n))}if(D(eu,a),D(Ks,d),D(tu,g),D(Tl,b),D(Xa,E),D(Ya,k),D(Sl,U),D(su,w),D(ru,A),D(Ws,y),D(Cl,_),D(nu,O),J(I))if(I.length){const X=e.exposed||(e.exposed={});I.forEach(z=>{Object.defineProperty(X,z,{get:()=>n[z],set:ge=>n[z]=ge})})}else e.exposed||(e.exposed={});R&&e.render===$e&&(e.render=R),q!=null&&(e.inheritAttrs=q),M&&(e.components=M),K&&(e.directives=K)}function cu(e,t,n=$e){J(e)&&(e=cs(e));for(const r in e){const s=e[r];let o;le(s)?"default"in s?o=He(s.from||r,s.default,!0):o=He(s.from||r):o=He(s),Ee(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function Po(e,t,n){je(J(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function kl(e,t,n,r){const s=r.includes(".")?ql(n,r):()=>n[r];if(de(e)){const o=t[e];Q(o)&&Yt(s,o)}else if(Q(e))Yt(s,e.bind(n));else if(le(e))if(J(e))e.forEach(o=>kl(o,t,n,r));else{const o=Q(e.handler)?e.handler.bind(n):t[e.handler];Q(o)&&Yt(s,o,e)}}function qs(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!s.length&&!n&&!r?c=t:(c={},s.length&&s.forEach(f=>cr(c,f,i,!0)),cr(c,t,i)),le(t)&&o.set(t,c),c}function cr(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&cr(e,o,n,!0),s&&s.forEach(i=>cr(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=au[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const au={data:Ao,props:ko,emits:ko,methods:hn,computed:hn,beforeCreate:Re,created:Re,beforeMount:Re,mounted:Re,beforeUpdate:Re,updated:Re,beforeDestroy:Re,beforeUnmount:Re,destroyed:Re,unmounted:Re,activated:Re,deactivated:Re,errorCaptured:Re,serverPrefetch:Re,components:hn,directives:hn,watch:fu,provide:Ao,inject:uu};function Ao(e,t){return t?e?function(){return be(Q(e)?e.call(this,this):e,Q(t)?t.call(this,this):t)}:t:e}function uu(e,t){return hn(cs(e),cs(t))}function cs(e){if(J(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Re(e,t){return e?[...new Set([].concat(e,t))]:t}function hn(e,t){return e?be(Object.create(null),e,t):t}function ko(e,t){return e?J(e)&&J(t)?[...new Set([...e,...t])]:be(Object.create(null),xo(e),xo(t??{})):t}function fu(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const r in t)n[r]=Re(e[r],t[r]);return n}function Ol(){return{app:null,config:{isNativeTag:Qc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let du=0;function hu(e,t){return function(r,s=null){Q(r)||(r=be({},r)),s!=null&&!le(s)&&(s=null);const o=Ol(),i=new WeakSet;let l=!1;const c=o.app={_uid:du++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:ic,get config(){return o.config},set config(f){},use(f,...u){return i.has(f)||(f&&Q(f.install)?(i.add(f),f.install(c,...u)):Q(f)&&(i.add(f),f(c,...u))),c},mixin(f){return o.mixins.includes(f)||o.mixins.push(f),c},component(f,u){return u?(o.components[f]=u,c):o.components[f]},directive(f,u){return u?(o.directives[f]=u,c):o.directives[f]},mount(f,u,a){if(!l){const d=he(r,s);return d.appContext=o,a===!0?a="svg":a===!1&&(a=void 0),u&&t?t(d,f):e(d,f,a),l=!0,c._container=f,f.__vue_app__=c,kr(d.component)}},unmount(){l&&(e(null,c._container),delete c._container.__vue_app__)},provide(f,u){return o.provides[f]=u,c},runWithContext(f){const u=Mt;Mt=c;try{return f()}finally{Mt=u}}};return c}}let Mt=null;function Xt(e,t){if(me){let n=me.provides;const r=me.parent&&me.parent.provides;r===n&&(n=me.provides=Object.create(r)),n[e]=t}}function He(e,t,n=!1){const r=me||Te;if(r||Mt){const s=Mt?Mt._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&Q(t)?t.call(r&&r.proxy):t}}function Il(){return!!(me||Te||Mt)}const Ml={},Hl=()=>Object.create(Ml),Ll=e=>Object.getPrototypeOf(e)===Ml;function pu(e,t,n,r=!1){const s={},o=Hl();e.propsDefaults=Object.create(null),Nl(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:rt(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function gu(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=re(s),[c]=e.propsOptions;let f=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let a=0;a<u.length;a++){let d=u[a];if(Ar(e.emitsOptions,d))continue;const g=t[d];if(c)if(te(o,d))g!==o[d]&&(o[d]=g,f=!0);else{const b=We(d);s[b]=as(c,l,b,g,e,!1)}else g!==o[d]&&(o[d]=g,f=!0)}}}else{Nl(e,t,s,o)&&(f=!0);let u;for(const a in l)(!t||!te(t,a)&&((u=Lt(a))===a||!te(t,u)))&&(c?n&&(n[a]!==void 0||n[u]!==void 0)&&(s[a]=as(c,l,a,void 0,e,!0)):delete s[a]);if(o!==l)for(const a in o)(!t||!te(t,a))&&(delete o[a],f=!0)}f&&st(e.attrs,"set","")}function Nl(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(qt(c))continue;const f=t[c];let u;s&&te(s,u=We(c))?!o||!o.includes(u)?n[u]=f:(l||(l={}))[u]=f:Ar(e.emitsOptions,c)||(!(c in r)||f!==r[c])&&(r[c]=f,i=!0)}if(o){const c=re(n),f=l||fe;for(let u=0;u<o.length;u++){const a=o[u];n[a]=as(s,c,a,f[a],e,!te(f,a))}}return i}function as(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=te(i,"default");if(l&&r===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&Q(c)){const{propsDefaults:f}=s;if(n in f)r=f[n];else{const u=Ln(s);r=f[n]=c.call(null,t),u()}}else r=c}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===Lt(n))&&(r=!0))}return r}const mu=new WeakMap;function $l(e,t,n=!1){const r=n?mu:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let c=!1;if(!Q(e)){const u=a=>{c=!0;const[d,g]=$l(a,t,!0);be(i,d),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return le(e)&&r.set(e,Kt),Kt;if(J(o))for(let u=0;u<o.length;u++){const a=We(o[u]);Oo(a)&&(i[a]=fe)}else if(o)for(const u in o){const a=We(u);if(Oo(a)){const d=o[u],g=i[a]=J(d)||Q(d)?{type:d}:be({},d),b=g.type;let E=!1,k=!0;if(J(b))for(let H=0;H<b.length;++H){const y=b[H],m=Q(y)&&y.name;if(m==="Boolean"){E=!0;break}else m==="String"&&(k=!1)}else E=Q(b)&&b.name==="Boolean";g[0]=E,g[1]=k,(E||te(g,"default"))&&l.push(a)}}const f=[i,l];return le(e)&&r.set(e,f),f}function Oo(e){return e[0]!=="$"&&!qt(e)}const jl=e=>e[0]==="_"||e==="$stable",Gs=e=>J(e)?e.map(Me):[Me(e)],yu=(e,t,n)=>{if(t._n)return t;const r=_l((...s)=>Gs(t(...s)),n);return r._c=!1,r},Fl=(e,t,n)=>{const r=e._ctx;for(const s in e){if(jl(s))continue;const o=e[s];if(Q(o))t[s]=yu(s,o,r);else if(o!=null){const i=Gs(o);t[s]=()=>i}}},Bl=(e,t)=>{const n=Gs(t);e.slots.default=()=>n},Ul=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},_u=(e,t,n)=>{const r=e.slots=Hl();if(e.vnode.shapeFlag&32){const s=t._;s?(Ul(r,t,n),n&&Ki(r,"_",s,!0)):Fl(t,r)}else t&&Bl(e,t)},bu=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=fe;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Ul(s,t,n):(o=!t.$stable,Fl(t,s)),i=t}else t&&(Bl(e,t),i={default:1});if(o)for(const l in s)!jl(l)&&i[l]==null&&delete s[l]};function ar(e,t,n,r,s=!1){if(J(e)){e.forEach((d,g)=>ar(d,t&&(J(t)?t[g]:t),n,r,s));return}if(Qt(r)&&!s)return;const o=r.shapeFlag&4?kr(r.component):r.el,i=s?null:o,{i:l,r:c}=e,f=t&&t.r,u=l.refs===fe?l.refs={}:l.refs,a=l.setupState;if(f!=null&&f!==c&&(de(f)?(u[f]=null,te(a,f)&&(a[f]=null)):Ee(f)&&(f.value=null)),Q(c))bt(c,l,12,[i,u]);else{const d=de(c),g=Ee(c);if(d||g){const b=()=>{if(e.f){const E=d?te(a,c)?a[c]:u[c]:c.value;s?J(E)&&Hs(E,o):J(E)?E.includes(o)||E.push(o):d?(u[c]=[o],te(a,c)&&(a[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else d?(u[c]=i,te(a,c)&&(a[c]=i)):g&&(c.value=i,e.k&&(u[e.k]=i))};i?(b.id=-1,_e(b,n)):b()}}}const vu=Symbol("_vte"),wu=e=>e.__isTeleport;let Io=!1;const Ut=()=>{Io||(console.error("Hydration completed but contains mismatches."),Io=!0)},Eu=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Ru=e=>e.namespaceURI.includes("MathML"),Wn=e=>{if(Eu(e))return"svg";if(Ru(e))return"mathml"},qn=e=>e.nodeType===8;function Tu(e){const{mt:t,p:n,o:{patchProp:r,createText:s,nextSibling:o,parentNode:i,remove:l,insert:c,createComment:f}}=e,u=(m,_)=>{if(!_.hasChildNodes()){n(null,m,_),ir(),_._vnode=m;return}a(_.firstChild,m,null,null,null),ir(),_._vnode=m},a=(m,_,R,w,A,U=!1)=>{U=U||!!_.dynamicChildren;const O=qn(m)&&m.data==="[",I=()=>E(m,_,R,w,A,O),{type:q,ref:M,shapeFlag:K,patchFlag:ee}=_;let ne=m.nodeType;_.el=m,ee===-2&&(U=!1,_.dynamicChildren=null);let D=null;switch(q){case Ht:ne!==3?_.children===""?(c(_.el=s(""),i(m),m),D=m):D=I():(m.data!==_.children&&(Ut(),m.data=_.children),D=o(m));break;case ve:y(m)?(D=o(m),H(_.el=m.content.firstChild,m,R)):ne!==8||O?D=I():D=o(m);break;case gn:if(O&&(m=o(m),ne=m.nodeType),ne===1||ne===3){D=m;const X=!_.children.length;for(let z=0;z<_.staticCount;z++)X&&(_.children+=D.nodeType===1?D.outerHTML:D.data),z===_.staticCount-1&&(_.anchor=D),D=o(D);return O?o(D):D}else I();break;case Ie:O?D=b(m,_,R,w,A,U):D=I();break;default:if(K&1)(ne!==1||_.type.toLowerCase()!==m.tagName.toLowerCase())&&!y(m)?D=I():D=d(m,_,R,w,A,U);else if(K&6){_.slotScopeIds=A;const X=i(m);if(O?D=k(m):qn(m)&&m.data==="teleport start"?D=k(m,m.data,"teleport end"):D=o(m),t(_,X,null,R,w,Wn(X),U),Qt(_)){let z;O?(z=he(Ie),z.anchor=D?D.previousSibling:X.lastChild):z=m.nodeType===3?rc(""):he("div"),z.el=m,_.component.subTree=z}}else K&64?ne!==8?D=I():D=_.type.hydrate(m,_,R,w,A,U,e,g):K&128&&(D=_.type.hydrate(m,_,R,w,Wn(i(m)),A,U,e,a))}return M!=null&&ar(M,null,w,_),D},d=(m,_,R,w,A,U)=>{U=U||!!_.dynamicChildren;const{type:O,props:I,patchFlag:q,shapeFlag:M,dirs:K,transition:ee}=_,ne=O==="input"||O==="option";if(ne||q!==-1){K&&ze(_,null,R,"created");let D=!1;if(y(m)){D=Vl(w,ee)&&R&&R.vnode.props&&R.vnode.props.appear;const z=m.content.firstChild;D&&ee.beforeEnter(z),H(z,m,R),_.el=m=z}if(M&16&&!(I&&(I.innerHTML||I.textContent))){let z=g(m.firstChild,_,m,R,w,A,U);for(;z;){Ut();const ge=z;z=z.nextSibling,l(ge)}}else M&8&&m.textContent!==_.children&&(Ut(),m.textContent=_.children);if(I){if(ne||!U||q&48){const z=m.tagName.includes("-");for(const ge in I)(ne&&(ge.endsWith("value")||ge==="indeterminate")||In(ge)&&!qt(ge)||ge[0]==="."||z)&&r(m,ge,null,I[ge],void 0,R)}else if(I.onClick)r(m,"onClick",null,I.onClick,void 0,R);else if(q&4&&Jt(I.style))for(const z in I.style)I.style[z]}let X;(X=I&&I.onVnodeBeforeMount)&&Se(X,R,_),K&&ze(_,null,R,"beforeMount"),((X=I&&I.onVnodeMounted)||K||D)&&Ql(()=>{X&&Se(X,R,_),D&&ee.enter(m),K&&ze(_,null,R,"mounted")},w)}return m.nextSibling},g=(m,_,R,w,A,U,O)=>{O=O||!!_.dynamicChildren;const I=_.children,q=I.length;for(let M=0;M<q;M++){const K=O?I[M]:I[M]=Me(I[M]),ee=K.type===Ht;if(m){if(ee&&!O){let ne=I[M+1];ne&&(ne=Me(ne)).type===Ht&&(c(s(m.data.slice(K.children.length)),R,o(m)),m.data=K.children)}m=a(m,K,w,A,U,O)}else ee&&!K.children?c(K.el=s(""),R):(Ut(),n(null,K,R,null,w,A,Wn(R),U))}return m},b=(m,_,R,w,A,U)=>{const{slotScopeIds:O}=_;O&&(A=A?A.concat(O):O);const I=i(m),q=g(o(m),_,I,R,w,A,U);return q&&qn(q)&&q.data==="]"?o(_.anchor=q):(Ut(),c(_.anchor=f("]"),I,q),q)},E=(m,_,R,w,A,U)=>{if(Ut(),_.el=null,U){const q=k(m);for(;;){const M=o(m);if(M&&M!==q)l(M);else break}}const O=o(m),I=i(m);return l(m),n(null,_,I,O,R,w,Wn(I),A),O},k=(m,_="[",R="]")=>{let w=0;for(;m;)if(m=o(m),m&&qn(m)&&(m.data===_&&w++,m.data===R)){if(w===0)return o(m);w--}return m},H=(m,_,R)=>{const w=_.parentNode;w&&w.replaceChild(m,_);let A=R;for(;A;)A.vnode.el===_&&(A.vnode.el=A.subTree.el=m),A=A.parent},y=m=>m.nodeType===1&&m.tagName.toLowerCase()==="template";return[u,a]}const _e=Ql;function Cu(e){return Dl(e)}function Su(e){return Dl(e,Tu)}function Dl(e,t){const n=qi();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:c,setText:f,setElementText:u,parentNode:a,nextSibling:d,setScopeId:g=$e,insertStaticContent:b}=e,E=(h,p,v,S=null,T=null,P=null,$=void 0,L=null,N=!!p.dynamicChildren)=>{if(h===p)return;h&&!Ve(h,p)&&(S=C(h),Ce(h,T,P,!0),h=null),p.patchFlag===-2&&(N=!1,p.dynamicChildren=null);const{type:x,ref:B,shapeFlag:G}=p;switch(x){case Ht:k(h,p,v,S);break;case ve:H(h,p,v,S);break;case gn:h==null&&y(p,v,S,$);break;case Ie:M(h,p,v,S,T,P,$,L,N);break;default:G&1?R(h,p,v,S,T,P,$,L,N):G&6?K(h,p,v,S,T,P,$,L,N):(G&64||G&128)&&x.process(h,p,v,S,T,P,$,L,N,V)}B!=null&&T&&ar(B,h&&h.ref,P,p||h,!p)},k=(h,p,v,S)=>{if(h==null)r(p.el=l(p.children),v,S);else{const T=p.el=h.el;p.children!==h.children&&f(T,p.children)}},H=(h,p,v,S)=>{h==null?r(p.el=c(p.children||""),v,S):p.el=h.el},y=(h,p,v,S)=>{[h.el,h.anchor]=b(h.children,p,v,S,h.el,h.anchor)},m=({el:h,anchor:p},v,S)=>{let T;for(;h&&h!==p;)T=d(h),r(h,v,S),h=T;r(p,v,S)},_=({el:h,anchor:p})=>{let v;for(;h&&h!==p;)v=d(h),s(h),h=v;s(p)},R=(h,p,v,S,T,P,$,L,N)=>{p.type==="svg"?$="svg":p.type==="math"&&($="mathml"),h==null?w(p,v,S,T,P,$,L,N):O(h,p,T,P,$,L,N)},w=(h,p,v,S,T,P,$,L)=>{let N,x;const{props:B,shapeFlag:G,transition:W,dirs:Y}=h;if(N=h.el=i(h.type,P,B&&B.is,B),G&8?u(N,h.children):G&16&&U(h.children,N,null,S,T,Vr(h,P),$,L),Y&&ze(h,null,S,"created"),A(N,h,h.scopeId,$,S),B){for(const ce in B)ce!=="value"&&!qt(ce)&&o(N,ce,null,B[ce],P,S);"value"in B&&o(N,"value",null,B.value,P),(x=B.onVnodeBeforeMount)&&Se(x,S,h)}Y&&ze(h,null,S,"beforeMount");const Z=Vl(T,W);Z&&W.beforeEnter(N),r(N,p,v),((x=B&&B.onVnodeMounted)||Z||Y)&&_e(()=>{x&&Se(x,S,h),Z&&W.enter(N),Y&&ze(h,null,S,"mounted")},T)},A=(h,p,v,S,T)=>{if(v&&g(h,v),S)for(let P=0;P<S.length;P++)g(h,S[P]);if(T){let P=T.subTree;if(p===P){const $=T.vnode;A(h,$,$.scopeId,$.slotScopeIds,T.parent)}}},U=(h,p,v,S,T,P,$,L,N=0)=>{for(let x=N;x<h.length;x++){const B=h[x]=L?pt(h[x]):Me(h[x]);E(null,B,p,v,S,T,P,$,L)}},O=(h,p,v,S,T,P,$)=>{const L=p.el=h.el;let{patchFlag:N,dynamicChildren:x,dirs:B}=p;N|=h.patchFlag&16;const G=h.props||fe,W=p.props||fe;let Y;if(v&&Tt(v,!1),(Y=W.onVnodeBeforeUpdate)&&Se(Y,v,p,h),B&&ze(p,h,v,"beforeUpdate"),v&&Tt(v,!0),(G.innerHTML&&W.innerHTML==null||G.textContent&&W.textContent==null)&&u(L,""),x?I(h.dynamicChildren,x,L,v,S,Vr(p,T),P):$||z(h,p,L,null,v,S,Vr(p,T),P,!1),N>0){if(N&16)q(L,G,W,v,T);else if(N&2&&G.class!==W.class&&o(L,"class",null,W.class,T),N&4&&o(L,"style",G.style,W.style,T),N&8){const Z=p.dynamicProps;for(let ce=0;ce<Z.length;ce++){const oe=Z[ce],ye=G[oe],Be=W[oe];(Be!==ye||oe==="value")&&o(L,oe,ye,Be,T,v)}}N&1&&h.children!==p.children&&u(L,p.children)}else!$&&x==null&&q(L,G,W,v,T);((Y=W.onVnodeUpdated)||B)&&_e(()=>{Y&&Se(Y,v,p,h),B&&ze(p,h,v,"updated")},S)},I=(h,p,v,S,T,P,$)=>{for(let L=0;L<p.length;L++){const N=h[L],x=p[L],B=N.el&&(N.type===Ie||!Ve(N,x)||N.shapeFlag&70)?a(N.el):v;E(N,x,B,null,S,T,P,$,!0)}},q=(h,p,v,S,T)=>{if(p!==v){if(p!==fe)for(const P in p)!qt(P)&&!(P in v)&&o(h,P,p[P],null,T,S);for(const P in v){if(qt(P))continue;const $=v[P],L=p[P];$!==L&&P!=="value"&&o(h,P,L,$,T,S)}"value"in v&&o(h,"value",p.value,v.value,T)}},M=(h,p,v,S,T,P,$,L,N)=>{const x=p.el=h?h.el:l(""),B=p.anchor=h?h.anchor:l("");let{patchFlag:G,dynamicChildren:W,slotScopeIds:Y}=p;Y&&(L=L?L.concat(Y):Y),h==null?(r(x,v,S),r(B,v,S),U(p.children||[],v,B,T,P,$,L,N)):G>0&&G&64&&W&&h.dynamicChildren?(I(h.dynamicChildren,W,v,T,P,$,L),(p.key!=null||T&&p===T.subTree)&&Kl(h,p,!0)):z(h,p,v,B,T,P,$,L,N)},K=(h,p,v,S,T,P,$,L,N)=>{p.slotScopeIds=L,h==null?p.shapeFlag&512?T.ctx.activate(p,v,S,$,N):ee(p,v,S,T,P,$,N):ne(h,p,N)},ee=(h,p,v,S,T,P,$)=>{const L=h.component=Gu(h,S,T);if(Hn(h)&&(L.ctx.renderer=V),Ju(L,!1,$),L.asyncDep){if(T&&T.registerDep(L,D,$),!h.el){const N=L.subTree=he(ve);H(null,N,p,v)}}else D(L,h,p,v,T,P,$)},ne=(h,p,v)=>{const S=p.component=h.component;if(Nu(h,p,v))if(S.asyncDep&&!S.asyncResolved){X(S,p,v);return}else S.next=p,Ka(S.update),S.effect.dirty=!0,S.update();else p.el=h.el,S.vnode=p},D=(h,p,v,S,T,P,$)=>{const L=()=>{if(h.isMounted){let{next:B,bu:G,u:W,parent:Y,vnode:Z}=h;{const Bt=Wl(h);if(Bt){B&&(B.el=Z.el,X(h,B,$)),Bt.asyncDep.then(()=>{h.isUnmounted||L()});return}}let ce=B,oe;Tt(h,!1),B?(B.el=Z.el,X(h,B,$)):B=Z,G&&Gt(G),(oe=B.props&&B.props.onVnodeBeforeUpdate)&&Se(oe,Y,B,Z),Tt(h,!0);const ye=Kr(h),Be=h.subTree;h.subTree=ye,E(Be,ye,a(Be.el),C(Be),h,T,P),B.el=ye.el,ce===null&&zs(h,ye.el),W&&_e(W,T),(oe=B.props&&B.props.onVnodeUpdated)&&_e(()=>Se(oe,Y,B,Z),T)}else{let B;const{el:G,props:W}=p,{bm:Y,m:Z,parent:ce}=h,oe=Qt(p);if(Tt(h,!1),Y&&Gt(Y),!oe&&(B=W&&W.onVnodeBeforeMount)&&Se(B,ce,p),Tt(h,!0),G&&ae){const ye=()=>{h.subTree=Kr(h),ae(G,h.subTree,h,T,null)};oe?p.type.__asyncLoader().then(()=>!h.isUnmounted&&ye()):ye()}else{const ye=h.subTree=Kr(h);E(null,ye,v,S,h,T,P),p.el=ye.el}if(Z&&_e(Z,T),!oe&&(B=W&&W.onVnodeMounted)){const ye=p;_e(()=>Se(B,ce,ye),T)}(p.shapeFlag&256||ce&&Qt(ce.vnode)&&ce.vnode.shapeFlag&256)&&h.a&&_e(h.a,T),h.isMounted=!0,p=v=S=null}},N=h.effect=new Ns(L,$e,()=>Sr(x),h.scope),x=h.update=()=>{N.dirty&&N.run()};x.i=h,x.id=h.uid,Tt(h,!0),x()},X=(h,p,v)=>{p.component=h;const S=h.vnode.props;h.vnode=p,h.next=null,gu(h,p.props,S,v),bu(h,p.children,v),Et(),Ro(h),Rt()},z=(h,p,v,S,T,P,$,L,N=!1)=>{const x=h&&h.children,B=h?h.shapeFlag:0,G=p.children,{patchFlag:W,shapeFlag:Y}=p;if(W>0){if(W&128){lt(x,G,v,S,T,P,$,L,N);return}else if(W&256){ge(x,G,v,S,T,P,$,L,N);return}}Y&8?(B&16&&Le(x,T,P),G!==x&&u(v,G)):B&16?Y&16?lt(x,G,v,S,T,P,$,L,N):Le(x,T,P,!0):(B&8&&u(v,""),Y&16&&U(G,v,S,T,P,$,L,N))},ge=(h,p,v,S,T,P,$,L,N)=>{h=h||Kt,p=p||Kt;const x=h.length,B=p.length,G=Math.min(x,B);let W;for(W=0;W<G;W++){const Y=p[W]=N?pt(p[W]):Me(p[W]);E(h[W],Y,v,null,T,P,$,L,N)}x>B?Le(h,T,P,!0,!1,G):U(p,v,S,T,P,$,L,N,G)},lt=(h,p,v,S,T,P,$,L,N)=>{let x=0;const B=p.length;let G=h.length-1,W=B-1;for(;x<=G&&x<=W;){const Y=h[x],Z=p[x]=N?pt(p[x]):Me(p[x]);if(Ve(Y,Z))E(Y,Z,v,null,T,P,$,L,N);else break;x++}for(;x<=G&&x<=W;){const Y=h[G],Z=p[W]=N?pt(p[W]):Me(p[W]);if(Ve(Y,Z))E(Y,Z,v,null,T,P,$,L,N);else break;G--,W--}if(x>G){if(x<=W){const Y=W+1,Z=Y<B?p[Y].el:S;for(;x<=W;)E(null,p[x]=N?pt(p[x]):Me(p[x]),v,Z,T,P,$,L,N),x++}}else if(x>W)for(;x<=G;)Ce(h[x],T,P,!0),x++;else{const Y=x,Z=x,ce=new Map;for(x=Z;x<=W;x++){const Ae=p[x]=N?pt(p[x]):Me(p[x]);Ae.key!=null&&ce.set(Ae.key,x)}let oe,ye=0;const Be=W-Z+1;let Bt=!1,uo=0;const an=new Array(Be);for(x=0;x<Be;x++)an[x]=0;for(x=Y;x<=G;x++){const Ae=h[x];if(ye>=Be){Ce(Ae,T,P,!0);continue}let Je;if(Ae.key!=null)Je=ce.get(Ae.key);else for(oe=Z;oe<=W;oe++)if(an[oe-Z]===0&&Ve(Ae,p[oe])){Je=oe;break}Je===void 0?Ce(Ae,T,P,!0):(an[Je-Z]=x+1,Je>=uo?uo=Je:Bt=!0,E(Ae,p[Je],v,null,T,P,$,L,N),ye++)}const fo=Bt?xu(an):Kt;for(oe=fo.length-1,x=Be-1;x>=0;x--){const Ae=Z+x,Je=p[Ae],ho=Ae+1<B?p[Ae+1].el:S;an[x]===0?E(null,Je,v,ho,T,P,$,L,N):Bt&&(oe<0||x!==fo[oe]?Ge(Je,v,ho,2):oe--)}}},Ge=(h,p,v,S,T=null)=>{const{el:P,type:$,transition:L,children:N,shapeFlag:x}=h;if(x&6){Ge(h.component.subTree,p,v,S);return}if(x&128){h.suspense.move(p,v,S);return}if(x&64){$.move(h,p,v,V);return}if($===Ie){r(P,p,v);for(let G=0;G<N.length;G++)Ge(N[G],p,v,S);r(h.anchor,p,v);return}if($===gn){m(h,p,v);return}if(S!==2&&x&1&&L)if(S===0)L.beforeEnter(P),r(P,p,v),_e(()=>L.enter(P),T);else{const{leave:G,delayLeave:W,afterLeave:Y}=L,Z=()=>r(P,p,v),ce=()=>{G(P,()=>{Z(),Y&&Y()})};W?W(P,Z,ce):ce()}else r(P,p,v)},Ce=(h,p,v,S=!1,T=!1)=>{const{type:P,props:$,ref:L,children:N,dynamicChildren:x,shapeFlag:B,patchFlag:G,dirs:W,cacheIndex:Y}=h;if(G===-2&&(T=!1),L!=null&&ar(L,null,v,h,!0),Y!=null&&(p.renderCache[Y]=void 0),B&256){p.ctx.deactivate(h);return}const Z=B&1&&W,ce=!Qt(h);let oe;if(ce&&(oe=$&&$.onVnodeBeforeUnmount)&&Se(oe,p,h),B&6)$n(h.component,v,S);else{if(B&128){h.suspense.unmount(v,S);return}Z&&ze(h,null,p,"beforeUnmount"),B&64?h.type.remove(h,p,v,V,S):x&&!x.hasOnce&&(P!==Ie||G>0&&G&64)?Le(x,p,v,!1,!0):(P===Ie&&G&384||!T&&B&16)&&Le(N,p,v),S&&jt(h)}(ce&&(oe=$&&$.onVnodeUnmounted)||Z)&&_e(()=>{oe&&Se(oe,p,h),Z&&ze(h,null,p,"unmounted")},v)},jt=h=>{const{type:p,el:v,anchor:S,transition:T}=h;if(p===Ie){Ft(v,S);return}if(p===gn){_(h);return}const P=()=>{s(v),T&&!T.persisted&&T.afterLeave&&T.afterLeave()};if(h.shapeFlag&1&&T&&!T.persisted){const{leave:$,delayLeave:L}=T,N=()=>$(v,P);L?L(h.el,P,N):N()}else P()},Ft=(h,p)=>{let v;for(;h!==p;)v=d(h),s(h),h=v;s(p)},$n=(h,p,v)=>{const{bum:S,scope:T,update:P,subTree:$,um:L,m:N,a:x}=h;ur(N),ur(x),S&&Gt(S),T.stop(),P&&(P.active=!1,Ce($,h,p,v)),L&&_e(L,p),_e(()=>{h.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Le=(h,p,v,S=!1,T=!1,P=0)=>{for(let $=P;$<h.length;$++)Ce(h[$],p,v,S,T)},C=h=>{if(h.shapeFlag&6)return C(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const p=d(h.anchor||h.el),v=p&&p[vu];return v?d(v):p};let F=!1;const j=(h,p,v)=>{h==null?p._vnode&&Ce(p._vnode,null,null,!0):E(p._vnode||null,h,p,null,null,null,v),p._vnode=h,F||(F=!0,Ro(),ir(),F=!1)},V={p:E,um:Ce,m:Ge,r:jt,mt:ee,mc:U,pc:z,pbc:I,n:C,o:e};let se,ae;return t&&([se,ae]=t(V)),{render:j,hydrate:se,createApp:hu(j,se)}}function Vr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Tt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Vl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Kl(e,t,n=!1){const r=e.children,s=t.children;if(J(r)&&J(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=pt(s[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Kl(i,l)),l.type===Ht&&(l.el=i.el)}}function xu(e){const t=e.slice(),n=[0];let r,s,o,i,l;const c=e.length;for(r=0;r<c;r++){const f=e[r];if(f!==0){if(s=n[n.length-1],e[s]<f){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<f?o=l+1:i=l;f<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Wl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Wl(t)}function ur(e){if(e)for(let t=0;t<e.length;t++)e[t].active=!1}const Pu=Symbol.for("v-scx"),Au=()=>He(Pu);function Ug(e,t){return Js(e,null,t)}const Gn={};function Yt(e,t,n){return Js(e,t,n)}function Js(e,t,{immediate:n,deep:r,flush:s,once:o,onTrack:i,onTrigger:l}=fe){if(t&&o){const w=t;t=(...A)=>{w(...A),R()}}const c=me,f=w=>r===!0?w:mt(w,r===!1?1:void 0);let u,a=!1,d=!1;if(Ee(e)?(u=()=>e.value,a=tn(e)):Jt(e)?(u=()=>f(e),a=!0):J(e)?(d=!0,a=e.some(w=>Jt(w)||tn(w)),u=()=>e.map(w=>{if(Ee(w))return w.value;if(Jt(w))return f(w);if(Q(w))return bt(w,c,2)})):Q(e)?t?u=()=>bt(e,c,2):u=()=>(g&&g(),je(e,c,3,[b])):u=$e,t&&r){const w=u;u=()=>mt(w())}let g,b=w=>{g=m.onStop=()=>{bt(w,c,4),g=m.onStop=void 0}},E;if(Nn)if(b=$e,t?n&&je(t,c,3,[u(),d?[]:void 0,b]):u(),s==="sync"){const w=Au();E=w.__watcherHandles||(w.__watcherHandles=[])}else return $e;let k=d?new Array(e.length).fill(Gn):Gn;const H=()=>{if(!(!m.active||!m.dirty))if(t){const w=m.run();(r||a||(d?w.some((A,U)=>vt(A,k[U])):vt(w,k)))&&(g&&g(),je(t,c,3,[w,k===Gn?void 0:d&&k[0]===Gn?[]:k,b]),k=w)}else m.run()};H.allowRecurse=!!t;let y;s==="sync"?y=H:s==="post"?y=()=>_e(H,c&&c.suspense):(H.pre=!0,c&&(H.id=c.uid),y=()=>Sr(H));const m=new Ns(u,$e,y),_=Xi(),R=()=>{m.stop(),_&&Hs(_.effects,m)};return t?n?H():k=m.run():s==="post"?_e(m.run.bind(m),c&&c.suspense):m.run(),E&&E.push(R),R}function ku(e,t,n){const r=this.proxy,s=de(e)?e.includes(".")?ql(r,e):()=>r[e]:e.bind(r,r);let o;Q(t)?o=t:(o=t.handler,n=t);const i=Ln(this),l=Js(s,o.bind(r),n);return i(),l}function ql(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function mt(e,t=1/0,n){if(t<=0||!le(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ee(e))mt(e.value,t,n);else if(J(e))for(let r=0;r<e.length;r++)mt(e[r],t,n);else if(br(e)||Wt(e))e.forEach(r=>{mt(r,t,n)});else if(Vi(e)){for(const r in e)mt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&mt(e[r],t,n)}return e}const Ou=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${We(t)}Modifiers`]||e[`${Lt(t)}Modifiers`];function Iu(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||fe;let s=n;const o=t.startsWith("update:"),i=o&&Ou(r,t.slice(7));i&&(i.trim&&(s=n.map(u=>de(u)?u.trim():u)),i.number&&(s=n.map(sr)));let l,c=r[l=$r(t)]||r[l=$r(We(t))];!c&&o&&(c=r[l=$r(Lt(t))]),c&&je(c,e,6,s);const f=r[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,je(f,e,6,s)}}function Gl(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!Q(e)){const c=f=>{const u=Gl(f,t,!0);u&&(l=!0,be(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(le(e)&&r.set(e,null),null):(J(o)?o.forEach(c=>i[c]=null):be(i,o),le(e)&&r.set(e,i),i)}function Ar(e,t){return!e||!In(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,Lt(t))||te(e,t))}function Kr(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:c,render:f,renderCache:u,props:a,data:d,setupState:g,ctx:b,inheritAttrs:E}=e,k=lr(e);let H,y;try{if(n.shapeFlag&4){const _=s||r,R=_;H=Me(f.call(R,_,u,a,g,d,b)),y=l}else{const _=t;H=Me(_.length>1?_(a,{attrs:l,slots:i,emit:c}):_(a,null)),y=t.props?l:Hu(l)}}catch(_){mn.length=0,ln(_,e,1),H=he(ve)}let m=H;if(y&&E!==!1){const _=Object.keys(y),{shapeFlag:R}=m;_.length&&R&7&&(o&&_.some(Ms)&&(y=Lu(y,o)),m=ot(m,y,!1,!0))}return n.dirs&&(m=ot(m,null,!1,!0),m.dirs=m.dirs?m.dirs.concat(n.dirs):n.dirs),n.transition&&(m.transition=n.transition),H=m,lr(k),H}function Mu(e,t=!0){let n;for(let r=0;r<e.length;r++){const s=e[r];if(xn(s)){if(s.type!==ve||s.children==="v-if"){if(n)return;n=s}}else return}return n}const Hu=e=>{let t;for(const n in e)(n==="class"||n==="style"||In(n))&&((t||(t={}))[n]=e[n]);return t},Lu=(e,t)=>{const n={};for(const r in e)(!Ms(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Nu(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:c}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?Mo(r,i,f):!!i;if(c&8){const u=t.dynamicProps;for(let a=0;a<u.length;a++){const d=u[a];if(i[d]!==r[d]&&!Ar(f,d))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?Mo(r,i,f):!0:!!i;return!1}function Mo(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Ar(n,o))return!0}return!1}function zs({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const us=e=>e.__isSuspense;let fs=0;const $u={name:"Suspense",__isSuspense:!0,process(e,t,n,r,s,o,i,l,c,f){if(e==null)ju(t,n,r,s,o,i,l,c,f);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}Fu(e,t,n,r,s,i,l,c,f)}},hydrate:Bu,normalize:Uu},Jl=$u;function Sn(e,t){const n=e.props&&e.props[t];Q(n)&&n()}function ju(e,t,n,r,s,o,i,l,c){const{p:f,o:{createElement:u}}=c,a=u("div"),d=e.suspense=zl(e,s,r,t,a,n,o,i,l,c);f(null,d.pendingBranch=e.ssContent,a,null,r,d,o,i),d.deps>0?(Sn(e,"onPending"),Sn(e,"onFallback"),f(null,e.ssFallback,t,n,r,null,o,i),Zt(d,e.ssFallback)):d.resolve(!1,!0)}function Fu(e,t,n,r,s,o,i,l,{p:c,um:f,o:{createElement:u}}){const a=t.suspense=e.suspense;a.vnode=t,t.el=e.el;const d=t.ssContent,g=t.ssFallback,{activeBranch:b,pendingBranch:E,isInFallback:k,isHydrating:H}=a;if(E)a.pendingBranch=d,Ve(d,E)?(c(E,d,a.hiddenContainer,null,s,a,o,i,l),a.deps<=0?a.resolve():k&&(H||(c(b,g,n,r,s,null,o,i,l),Zt(a,g)))):(a.pendingId=fs++,H?(a.isHydrating=!1,a.activeBranch=E):f(E,s,a),a.deps=0,a.effects.length=0,a.hiddenContainer=u("div"),k?(c(null,d,a.hiddenContainer,null,s,a,o,i,l),a.deps<=0?a.resolve():(c(b,g,n,r,s,null,o,i,l),Zt(a,g))):b&&Ve(d,b)?(c(b,d,n,r,s,a,o,i,l),a.resolve(!0)):(c(null,d,a.hiddenContainer,null,s,a,o,i,l),a.deps<=0&&a.resolve()));else if(b&&Ve(d,b))c(b,d,n,r,s,a,o,i,l),Zt(a,d);else if(Sn(t,"onPending"),a.pendingBranch=d,d.shapeFlag&512?a.pendingId=d.component.suspenseId:a.pendingId=fs++,c(null,d,a.hiddenContainer,null,s,a,o,i,l),a.deps<=0)a.resolve();else{const{timeout:y,pendingId:m}=a;y>0?setTimeout(()=>{a.pendingId===m&&a.fallback(g)},y):y===0&&a.fallback(g)}}function zl(e,t,n,r,s,o,i,l,c,f,u=!1){const{p:a,m:d,um:g,n:b,o:{parentNode:E,remove:k}}=f;let H;const y=Du(e);y&&t&&t.pendingBranch&&(H=t.pendingId,t.deps++);const m=e.props?Wi(e.props.timeout):void 0,_=o,R={vnode:e,parent:t,parentComponent:n,namespace:i,container:r,hiddenContainer:s,deps:0,pendingId:fs++,timeout:typeof m=="number"?m:-1,activeBranch:null,pendingBranch:null,isInFallback:!u,isHydrating:u,isUnmounted:!1,effects:[],resolve(w=!1,A=!1){const{vnode:U,activeBranch:O,pendingBranch:I,pendingId:q,effects:M,parentComponent:K,container:ee}=R;let ne=!1;R.isHydrating?R.isHydrating=!1:w||(ne=O&&I.transition&&I.transition.mode==="out-in",ne&&(O.transition.afterLeave=()=>{q===R.pendingId&&(d(I,ee,o===_?b(O):o,0),ss(M))}),O&&(E(O.el)!==R.hiddenContainer&&(o=b(O)),g(O,K,R,!0)),ne||d(I,ee,o,0)),Zt(R,I),R.pendingBranch=null,R.isInFallback=!1;let D=R.parent,X=!1;for(;D;){if(D.pendingBranch){D.effects.push(...M),X=!0;break}D=D.parent}!X&&!ne&&ss(M),R.effects=[],y&&t&&t.pendingBranch&&H===t.pendingId&&(t.deps--,t.deps===0&&!A&&t.resolve()),Sn(U,"onResolve")},fallback(w){if(!R.pendingBranch)return;const{vnode:A,activeBranch:U,parentComponent:O,container:I,namespace:q}=R;Sn(A,"onFallback");const M=b(U),K=()=>{R.isInFallback&&(a(null,w,I,M,O,null,q,l,c),Zt(R,w))},ee=w.transition&&w.transition.mode==="out-in";ee&&(U.transition.afterLeave=K),R.isInFallback=!0,g(U,O,null,!0),ee||K()},move(w,A,U){R.activeBranch&&d(R.activeBranch,w,A,U),R.container=w},next(){return R.activeBranch&&b(R.activeBranch)},registerDep(w,A,U){const O=!!R.pendingBranch;O&&R.deps++;const I=w.vnode.el;w.asyncDep.catch(q=>{ln(q,w,0)}).then(q=>{if(w.isUnmounted||R.isUnmounted||R.pendingId!==w.suspenseId)return;w.asyncResolved=!0;const{vnode:M}=w;hs(w,q,!1),I&&(M.el=I);const K=!I&&w.subTree.el;A(w,M,E(I||w.subTree.el),I?null:b(w.subTree),R,i,U),K&&k(K),zs(w,M.el),O&&--R.deps===0&&R.resolve()})},unmount(w,A){R.isUnmounted=!0,R.activeBranch&&g(R.activeBranch,n,w,A),R.pendingBranch&&g(R.pendingBranch,n,w,A)}};return R}function Bu(e,t,n,r,s,o,i,l,c){const f=t.suspense=zl(t,r,n,e.parentNode,document.createElement("div"),null,s,o,i,l,!0),u=c(e,f.pendingBranch=t.ssContent,n,f,o,i);return f.deps===0&&f.resolve(!1,!0),u}function Uu(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=Ho(r?n.default:n),e.ssFallback=r?Ho(n.fallback):he(ve)}function Ho(e){let t;if(Q(e)){const n=nn&&e._c;n&&(e._d=!1,Qe()),e=e(),n&&(e._d=!0,t=xe,Xl())}return J(e)&&(e=Mu(e)),e=Me(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function Ql(e,t){t&&t.pendingBranch?J(e)?t.effects.push(...e):t.effects.push(e):ss(e)}function Zt(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let s=t.el;for(;!s&&t.component;)t=t.component.subTree,s=t.el;n.el=s,r&&r.subTree===n&&(r.vnode.el=s,zs(r,s))}function Du(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Ie=Symbol.for("v-fgt"),Ht=Symbol.for("v-txt"),ve=Symbol.for("v-cmt"),gn=Symbol.for("v-stc"),mn=[];let xe=null;function Qe(e=!1){mn.push(xe=e?null:[])}function Xl(){mn.pop(),xe=mn[mn.length-1]||null}let nn=1;function Lo(e){nn+=e,e<0&&xe&&(xe.hasOnce=!0)}function Yl(e){return e.dynamicChildren=nn>0?xe||Kt:null,Xl(),nn>0&&xe&&xe.push(e),e}function Zl(e,t,n,r,s,o){return Yl(tc(e,t,n,r,s,o,!0))}function At(e,t,n,r,s){return Yl(he(e,t,n,r,s,!0))}function xn(e){return e?e.__v_isVNode===!0:!1}function Ve(e,t){return e.type===t.type&&e.key===t.key}const ec=({key:e})=>e??null,Yn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?de(e)||Ee(e)||Q(e)?{i:Te,r:e,k:t,f:!!n}:e:null);function tc(e,t=null,n=null,r=0,s=null,o=e===Ie?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ec(t),ref:t&&Yn(t),scopeId:xr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:Te};return l?(Qs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=de(n)?8:16),nn>0&&!i&&xe&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&xe.push(c),c}const he=Vu;function Vu(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===Pl)&&(e=ve),xn(e)){const l=ot(e,t,!0);return n&&Qs(l,n),nn>0&&!o&&xe&&(l.shapeFlag&6?xe[xe.indexOf(e)]=l:xe.push(l)),l.patchFlag=-2,l}if(Yu(e)&&(e=e.__vccOpts),t){t=nc(t);let{class:l,style:c}=t;l&&!de(l)&&(t.class=Rr(l)),le(c)&&(ul(c)&&!J(c)&&(c=be({},c)),t.style=Er(c))}const i=de(e)?1:us(e)?128:wu(e)?64:le(e)?4:Q(e)?2:0;return tc(e,t,n,r,s,i,o,!0)}function nc(e){return e?ul(e)||Ll(e)?be({},e):e:null}function ot(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:c}=e,f=t?Ku(s||{},t):s,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&ec(f),ref:t&&t.ref?n&&o?J(o)?o.concat(Yn(t)):[o,Yn(t)]:Yn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ot(e.ssContent),ssFallback:e.ssFallback&&ot(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Cn(u,c.clone(u)),u}function rc(e=" ",t=0){return he(Ht,null,e,t)}function Dg(e,t){const n=he(gn,null,e);return n.staticCount=t,n}function Vg(e="",t=!1){return t?(Qe(),At(ve,null,e)):he(ve,null,e)}function Me(e){return e==null||typeof e=="boolean"?he(ve):J(e)?he(Ie,null,e.slice()):typeof e=="object"?pt(e):he(Ht,null,String(e))}function pt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ot(e)}function Qs(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(J(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Qs(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Ll(t)?t._ctx=Te:s===3&&Te&&(Te.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Q(t)?(t={default:t,_ctx:Te},n=32):(t=String(t),r&64?(n=16,t=[rc(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ku(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Rr([t.class,r.class]));else if(s==="style")t.style=Er([t.style,r.style]);else if(In(s)){const o=t[s],i=r[s];i&&o!==i&&!(J(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Se(e,t,n,r=null){je(e,t,7,[n,r])}const Wu=Ol();let qu=0;function Gu(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||Wu,o={uid:qu++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new Qi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:$l(r,s),emitsOptions:Gl(r,s),emit:null,emitted:null,propsDefaults:fe,inheritAttrs:r.inheritAttrs,ctx:fe,data:fe,props:fe,attrs:fe,slots:fe,refs:fe,setupState:fe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Iu.bind(null,o),e.ce&&e.ce(o),o}let me=null;const Xs=()=>me||Te;let fr,ds;{const e=qi(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};fr=t("__VUE_INSTANCE_SETTERS__",n=>me=n),ds=t("__VUE_SSR_SETTERS__",n=>Nn=n)}const Ln=e=>{const t=me;return fr(e),e.scope.on(),()=>{e.scope.off(),fr(t)}},No=()=>{me&&me.scope.off(),fr(null)};function sc(e){return e.vnode.shapeFlag&4}let Nn=!1;function Ju(e,t=!1,n=!1){t&&ds(t);const{props:r,children:s}=e.vnode,o=sc(e);pu(e,r,o,t),_u(e,s,n);const i=o?zu(e,t):void 0;return t&&ds(!1),i}function zu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,iu);const{setup:r}=n;if(r){const s=e.setupContext=r.length>1?Xu(e):null,o=Ln(e);Et();const i=bt(r,e,0,[e.props,s]);if(Rt(),o(),Ui(i)){if(i.then(No,No),t)return i.then(l=>{hs(e,l,t)}).catch(l=>{ln(l,e,0)});e.asyncDep=i}else hs(e,i,t)}else oc(e,t)}function hs(e,t,n){Q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:le(t)&&(e.setupState=pl(t)),oc(e,n)}let $o;function oc(e,t,n){const r=e.type;if(!e.render){if(!t&&$o&&!r.render){const s=r.template||qs(e).template;if(s){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:c}=r,f=be(be({isCustomElement:o,delimiters:l},i),c);r.render=$o(s,f)}}e.render=r.render||$e}{const s=Ln(e);Et();try{lu(e)}finally{Rt(),s()}}}const Qu={get(e,t){return Pe(e,"get",""),e[t]}};function Xu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Qu),slots:e.slots,emit:e.emit,expose:t}}function kr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(pl(Ha(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in pn)return pn[n](e)},has(t,n){return n in t||n in pn}})):e.proxy}function ps(e,t=!0){return Q(e)?e.displayName||e.name:e.name||t&&e.__name}function Yu(e){return Q(e)&&"__vccOpts"in e}const Ke=(e,t)=>La(e,t,Nn);function Ye(e,t,n){const r=arguments.length;return r===2?le(t)&&!J(t)?xn(t)?he(e,null,[t]):he(e,t):he(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&xn(n)&&(n=[n]),he(e,t,n))}const ic="3.4.38";/**
* @vue/runtime-dom v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Zu="http://www.w3.org/2000/svg",ef="http://www.w3.org/1998/Math/MathML",nt=typeof document<"u"?document:null,jo=nt&&nt.createElement("template"),tf={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?nt.createElementNS(Zu,e):t==="mathml"?nt.createElementNS(ef,e):n?nt.createElement(e,{is:n}):nt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>nt.createTextNode(e),createComment:e=>nt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>nt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{jo.innerHTML=r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e;const l=jo.content;if(r==="svg"||r==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},at="transition",un="animation",Pn=Symbol("_vtc"),Ys=(e,{slots:t})=>Ye(Ja,nf(e),t);Ys.displayName="Transition";const lc={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Ys.props=be({},bl,lc);const Ct=(e,t=[])=>{J(e)?e.forEach(n=>n(...t)):e&&e(...t)},Fo=e=>e?J(e)?e.some(t=>t.length>1):e.length>1:!1;function nf(e){const t={};for(const M in e)M in lc||(t[M]=e[M]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:f=i,appearToClass:u=l,leaveFromClass:a=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,b=rf(s),E=b&&b[0],k=b&&b[1],{onBeforeEnter:H,onEnter:y,onEnterCancelled:m,onLeave:_,onLeaveCancelled:R,onBeforeAppear:w=H,onAppear:A=y,onAppearCancelled:U=m}=t,O=(M,K,ee)=>{St(M,K?u:l),St(M,K?f:i),ee&&ee()},I=(M,K)=>{M._isLeaving=!1,St(M,a),St(M,g),St(M,d),K&&K()},q=M=>(K,ee)=>{const ne=M?A:y,D=()=>O(K,M,ee);Ct(ne,[K,D]),Bo(()=>{St(K,M?c:o),ut(K,M?u:l),Fo(ne)||Uo(K,r,E,D)})};return be(t,{onBeforeEnter(M){Ct(H,[M]),ut(M,o),ut(M,i)},onBeforeAppear(M){Ct(w,[M]),ut(M,c),ut(M,f)},onEnter:q(!1),onAppear:q(!0),onLeave(M,K){M._isLeaving=!0;const ee=()=>I(M,K);ut(M,a),ut(M,d),lf(),Bo(()=>{M._isLeaving&&(St(M,a),ut(M,g),Fo(_)||Uo(M,r,k,ee))}),Ct(_,[M,ee])},onEnterCancelled(M){O(M,!1),Ct(m,[M])},onAppearCancelled(M){O(M,!0),Ct(U,[M])},onLeaveCancelled(M){I(M),Ct(R,[M])}})}function rf(e){if(e==null)return null;if(le(e))return[Wr(e.enter),Wr(e.leave)];{const t=Wr(e);return[t,t]}}function Wr(e){return Wi(e)}function ut(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Pn]||(e[Pn]=new Set)).add(t)}function St(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Pn];n&&(n.delete(t),n.size||(e[Pn]=void 0))}function Bo(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let sf=0;function Uo(e,t,n,r){const s=e._endId=++sf,o=()=>{s===e._endId&&r()};if(n)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=of(e,t);if(!i)return r();const f=i+"end";let u=0;const a=()=>{e.removeEventListener(f,d),o()},d=g=>{g.target===e&&++u>=c&&a()};setTimeout(()=>{u<c&&a()},l+1),e.addEventListener(f,d)}function of(e,t){const n=window.getComputedStyle(e),r=b=>(n[b]||"").split(", "),s=r(`${at}Delay`),o=r(`${at}Duration`),i=Do(s,o),l=r(`${un}Delay`),c=r(`${un}Duration`),f=Do(l,c);let u=null,a=0,d=0;t===at?i>0&&(u=at,a=i,d=o.length):t===un?f>0&&(u=un,a=f,d=c.length):(a=Math.max(i,f),u=a>0?i>f?at:un:null,d=u?u===at?o.length:c.length:0);const g=u===at&&/\b(transform|all)(,|$)/.test(r(`${at}Property`).toString());return{type:u,timeout:a,propCount:d,hasTransform:g}}function Do(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Vo(n)+Vo(e[r])))}function Vo(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function lf(){return document.body.offsetHeight}function cf(e,t,n){const r=e[Pn];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ko=Symbol("_vod"),af=Symbol("_vsh"),uf=Symbol(""),ff=/(^|;)\s*display\s*:/;function df(e,t,n){const r=e.style,s=de(n);let o=!1;if(n&&!s){if(t)if(de(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Zn(r,l,"")}else for(const i in t)n[i]==null&&Zn(r,i,"");for(const i in n)i==="display"&&(o=!0),Zn(r,i,n[i])}else if(s){if(t!==n){const i=r[uf];i&&(n+=";"+i),r.cssText=n,o=ff.test(n)}}else t&&e.removeAttribute("style");Ko in e&&(e[Ko]=o?r.display:"",e[af]&&(r.display="none"))}const Wo=/\s*!important$/;function Zn(e,t,n){if(J(n))n.forEach(r=>Zn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=hf(e,t);Wo.test(n)?e.setProperty(Lt(r),n.replace(Wo,""),"important"):e[r]=n}}const qo=["Webkit","Moz","ms"],qr={};function hf(e,t){const n=qr[t];if(n)return n;let r=We(t);if(r!=="filter"&&r in e)return qr[t]=r;r=wr(r);for(let s=0;s<qo.length;s++){const o=qo[s]+r;if(o in e)return qr[t]=o}return t}const Go="http://www.w3.org/1999/xlink";function Jo(e,t,n,r,s,o=ca(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Go,t.slice(6,t.length)):e.setAttributeNS(Go,t,n):n==null||o&&!Gi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Ze(n)?String(n):n)}function pf(e,t,n,r){if(t==="innerHTML"||t==="textContent"){if(n==null)return;e[t]=n;return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const i=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?"":String(n);(i!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const i=typeof e[t];i==="boolean"?n=Gi(n):n==null&&i==="string"?(n="",o=!0):i==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(t)}function kt(e,t,n,r){e.addEventListener(t,n,r)}function gf(e,t,n,r){e.removeEventListener(t,n,r)}const zo=Symbol("_vei");function mf(e,t,n,r,s=null){const o=e[zo]||(e[zo]={}),i=o[t];if(r&&i)i.value=r;else{const[l,c]=yf(t);if(r){const f=o[t]=vf(r,s);kt(e,l,f,c)}else i&&(gf(e,l,i,c),o[t]=void 0)}}const Qo=/(?:Once|Passive|Capture)$/;function yf(e){let t;if(Qo.test(e)){t={};let r;for(;r=e.match(Qo);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Lt(e.slice(2)),t]}let Gr=0;const _f=Promise.resolve(),bf=()=>Gr||(_f.then(()=>Gr=0),Gr=Date.now());function vf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;je(wf(r,n.value),t,5,[r])};return n.value=e,n.attached=bf(),n}function wf(e,t){if(J(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Xo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ef=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?cf(e,r,i):t==="style"?df(e,n,r):In(t)?Ms(t)||mf(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Rf(e,t,r,i))?(pf(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Jo(e,t,r,i,o,t!=="value")):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Jo(e,t,r,i))};function Rf(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Xo(t)&&Q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Xo(t)&&de(n)?!1:t in e}const dr=e=>{const t=e.props["onUpdate:modelValue"]||!1;return J(t)?n=>Gt(t,n):t};function Tf(e){e.target.composing=!0}function Yo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const en=Symbol("_assign"),Kg={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[en]=dr(s);const o=r||s.props&&s.props.type==="number";kt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=sr(l)),e[en](l)}),n&&kt(e,"change",()=>{e.value=e.value.trim()}),t||(kt(e,"compositionstart",Tf),kt(e,"compositionend",Yo),kt(e,"change",Yo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[en]=dr(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?sr(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===c)||(e.value=c))}},Wg={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const s=br(t);kt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?sr(hr(i)):hr(i));e[en](e.multiple?s?new Set(o):o:o[0]),e._assigning=!0,cn(()=>{e._assigning=!1})}),e[en]=dr(r)},mounted(e,{value:t,modifiers:{number:n}}){Zo(e,t)},beforeUpdate(e,t,n){e[en]=dr(n)},updated(e,{value:t,modifiers:{number:n}}){e._assigning||Zo(e,t)}};function Zo(e,t,n){const r=e.multiple,s=J(t);if(!(r&&!s&&!br(t))){for(let o=0,i=e.options.length;o<i;o++){const l=e.options[o],c=hr(l);if(r)if(s){const f=typeof c;f==="string"||f==="number"?l.selected=t.some(u=>String(u)===String(c)):l.selected=ua(t,c)>-1}else l.selected=t.has(c);else if(Tr(hr(l),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!r&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function hr(e){return"_value"in e?e._value:e.value}const Cf=["ctrl","shift","alt","meta"],Sf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Cf.some(n=>e[`${n}Key`]&&!t.includes(n))},qg=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const l=Sf[t[i]];if(l&&l(s,t))return}return e(s,...o)})},cc=be({patchProp:Ef},tf);let yn,ei=!1;function xf(){return yn||(yn=Cu(cc))}function Pf(){return yn=ei?yn:Su(cc),ei=!0,yn}const Af=(...e)=>{const t=xf().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=uc(r);if(!s)return;const o=t._component;!Q(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.innerHTML="";const i=n(s,!1,ac(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t},kf=(...e)=>{const t=Pf().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=uc(r);if(s)return n(s,!0,ac(s))},t};function ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function uc(e){return de(e)?document.querySelector(e):e}const Of=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,If=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,Mf=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function Hf(e,t){if(e==="__proto__"||e==="constructor"&&t&&typeof t=="object"&&"prototype"in t){Lf(e);return}return t}function Lf(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function pr(e,t={}){if(typeof e!="string")return e;const n=e.trim();if(e[0]==='"'&&e.endsWith('"')&&!e.includes("\\"))return n.slice(1,-1);if(n.length<=9){const r=n.toLowerCase();if(r==="true")return!0;if(r==="false")return!1;if(r==="undefined")return;if(r==="null")return null;if(r==="nan")return Number.NaN;if(r==="infinity")return Number.POSITIVE_INFINITY;if(r==="-infinity")return Number.NEGATIVE_INFINITY}if(!Mf.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(Of.test(e)||If.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,Hf)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const Nf=/#/g,$f=/&/g,jf=/\//g,Ff=/=/g,Zs=/\+/g,Bf=/%5e/gi,Uf=/%60/gi,Df=/%7c/gi,Vf=/%20/gi;function Kf(e){return encodeURI(""+e).replace(Df,"|")}function gs(e){return Kf(typeof e=="string"?e:JSON.stringify(e)).replace(Zs,"%2B").replace(Vf,"+").replace(Nf,"%23").replace($f,"%26").replace(Uf,"`").replace(Bf,"^").replace(jf,"%2F")}function Jr(e){return gs(e).replace(Ff,"%3D")}function gr(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function Wf(e){return gr(e.replace(Zs," "))}function qf(e){return gr(e.replace(Zs," "))}function Gf(e=""){const t={};e[0]==="?"&&(e=e.slice(1));for(const n of e.split("&")){const r=n.match(/([^=]+)=?(.*)/)||[];if(r.length<2)continue;const s=Wf(r[1]);if(s==="__proto__"||s==="constructor")continue;const o=qf(r[2]||"");t[s]===void 0?t[s]=o:Array.isArray(t[s])?t[s].push(o):t[s]=[t[s],o]}return t}function Jf(e,t){return(typeof t=="number"||typeof t=="boolean")&&(t=String(t)),t?Array.isArray(t)?t.map(n=>`${Jr(e)}=${gs(n)}`).join("&"):`${Jr(e)}=${gs(t)}`:Jr(e)}function zf(e){return Object.keys(e).filter(t=>e[t]!==void 0).map(t=>Jf(t,e[t])).filter(Boolean).join("&")}const Qf=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,Xf=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,Yf=/^([/\\]\s*){2,}[^/\\]/,Zf=/^[\s\0]*(blob|data|javascript|vbscript):$/i,ed=/\/$|\/\?|\/#/,td=/^\.?\//;function $t(e,t={}){return typeof t=="boolean"&&(t={acceptRelative:t}),t.strict?Qf.test(e):Xf.test(e)||(t.acceptRelative?Yf.test(e):!1)}function nd(e){return!!e&&Zf.test(e)}function ms(e="",t){return t?ed.test(e):e.endsWith("/")}function eo(e="",t){if(!t)return(ms(e)?e.slice(0,-1):e)||"/";if(!ms(e,!0))return e||"/";let n=e,r="";const s=e.indexOf("#");s>=0&&(n=e.slice(0,s),r=e.slice(s));const[o,...i]=n.split("?");return((o.endsWith("/")?o.slice(0,-1):o)||"/")+(i.length>0?`?${i.join("?")}`:"")+r}function ys(e="",t){if(!t)return e.endsWith("/")?e:e+"/";if(ms(e,!0))return e||"/";let n=e,r="";const s=e.indexOf("#");if(s>=0&&(n=e.slice(0,s),r=e.slice(s),!n))return r;const[o,...i]=n.split("?");return o+"/"+(i.length>0?`?${i.join("?")}`:"")+r}function rd(e=""){return e.startsWith("/")}function ti(e=""){return rd(e)?e:"/"+e}function sd(e,t){if(dc(t)||$t(e))return e;const n=eo(t);return e.startsWith(n)?e:to(n,e)}function ni(e,t){if(dc(t))return e;const n=eo(t);if(!e.startsWith(n))return e;const r=e.slice(n.length);return r[0]==="/"?r:"/"+r}function fc(e,t){const n=ld(e),r={...Gf(n.search),...t};return n.search=zf(r),cd(n)}function dc(e){return!e||e==="/"}function od(e){return e&&e!=="/"}function to(e,...t){let n=e||"";for(const r of t.filter(s=>od(s)))if(n){const s=r.replace(td,"");n=ys(n)+s}else n=r;return n}function hc(...e){var i,l,c,f;const t=/\/(?!\/)/,n=e.filter(Boolean),r=[];let s=0;for(const u of n)if(!(!u||u==="/")){for(const[a,d]of u.split(t).entries())if(!(!d||d===".")){if(d===".."){if(r.length===1&&$t(r[0]))continue;r.pop(),s--;continue}if(a===1&&((i=r[r.length-1])!=null&&i.endsWith(":/"))){r[r.length-1]+="/"+d;continue}r.push(d),s++}}let o=r.join("/");return s>=0?(l=n[0])!=null&&l.startsWith("/")&&!o.startsWith("/")?o="/"+o:(c=n[0])!=null&&c.startsWith("./")&&!o.startsWith("./")&&(o="./"+o):o="../".repeat(-1*s)+o,(f=n[n.length-1])!=null&&f.endsWith("/")&&!o.endsWith("/")&&(o+="/"),o}function id(e,t,n={}){return n.trailingSlash||(e=ys(e),t=ys(t)),n.leadingSlash||(e=ti(e),t=ti(t)),n.encoding||(e=gr(e),t=gr(t)),e===t}const pc=Symbol.for("ufo:protocolRelative");function ld(e="",t){const n=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(n){const[,a,d=""]=n;return{protocol:a.toLowerCase(),pathname:d,href:a+d,auth:"",host:"",search:"",hash:""}}if(!$t(e,{acceptRelative:!0}))return ri(e);const[,r="",s,o=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,i="",l=""]=o.match(/([^#/?]*)(.*)?/)||[];r==="file:"&&(l=l.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:f,hash:u}=ri(l);return{protocol:r.toLowerCase(),auth:s?s.slice(0,Math.max(0,s.length-1)):"",host:i,pathname:c,search:f,hash:u,[pc]:!r}}function ri(e=""){const[t="",n="",r=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:n,hash:r}}function cd(e){const t=e.pathname||"",n=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",r=e.hash||"",s=e.auth?e.auth+"@":"",o=e.host||"";return(e.protocol||e[pc]?(e.protocol||"")+"//":"")+s+o+t+n+r}class ad extends Error{constructor(t,n){super(t,n),this.name="FetchError",n!=null&&n.cause&&!this.cause&&(this.cause=n.cause)}}function ud(e){var c,f,u,a,d;const t=((c=e.error)==null?void 0:c.message)||((f=e.error)==null?void 0:f.toString())||"",n=((u=e.request)==null?void 0:u.method)||((a=e.options)==null?void 0:a.method)||"GET",r=((d=e.request)==null?void 0:d.url)||String(e.request)||"/",s=`[${n}] ${JSON.stringify(r)}`,o=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",i=`${s}: ${o}${t?` ${t}`:""}`,l=new ad(i,e.error?{cause:e.error}:void 0);for(const g of["request","options","response"])Object.defineProperty(l,g,{get(){return e[g]}});for(const[g,b]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(l,g,{get(){return e.response&&e.response[b]}});return l}const fd=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function si(e="GET"){return fd.has(e.toUpperCase())}function dd(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}const hd=new Set(["image/svg","application/xml","application/xhtml","application/html"]),pd=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function gd(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return pd.test(t)?"json":hd.has(t)||t.startsWith("text/")?"text":"blob"}function md(e,t,n=globalThis.Headers){const r={...t,...e};if(t!=null&&t.params&&(e!=null&&e.params)&&(r.params={...t==null?void 0:t.params,...e==null?void 0:e.params}),t!=null&&t.query&&(e!=null&&e.query)&&(r.query={...t==null?void 0:t.query,...e==null?void 0:e.query}),t!=null&&t.headers&&(e!=null&&e.headers)){r.headers=new n((t==null?void 0:t.headers)||{});for(const[s,o]of new n((e==null?void 0:e.headers)||{}))r.headers.set(s,o)}return r}const yd=new Set([408,409,425,429,500,502,503,504]),_d=new Set([101,204,205,304]);function gc(e={}){const{fetch:t=globalThis.fetch,Headers:n=globalThis.Headers,AbortController:r=globalThis.AbortController}=e;async function s(l){const c=l.error&&l.error.name==="AbortError"&&!l.options.timeout||!1;if(l.options.retry!==!1&&!c){let u;typeof l.options.retry=="number"?u=l.options.retry:u=si(l.options.method)?0:1;const a=l.response&&l.response.status||500;if(u>0&&(Array.isArray(l.options.retryStatusCodes)?l.options.retryStatusCodes.includes(a):yd.has(a))){const d=l.options.retryDelay||0;return d>0&&await new Promise(g=>setTimeout(g,d)),o(l.request,{...l.options,retry:u-1})}}const f=ud(l);throw Error.captureStackTrace&&Error.captureStackTrace(f,o),f}const o=async function(c,f={}){var g;const u={request:c,options:md(f,e.defaults,n),response:void 0,error:void 0};u.options.method=(g=u.options.method)==null?void 0:g.toUpperCase(),u.options.onRequest&&await u.options.onRequest(u),typeof u.request=="string"&&(u.options.baseURL&&(u.request=sd(u.request,u.options.baseURL)),(u.options.query||u.options.params)&&(u.request=fc(u.request,{...u.options.params,...u.options.query}))),u.options.body&&si(u.options.method)&&(dd(u.options.body)?(u.options.body=typeof u.options.body=="string"?u.options.body:JSON.stringify(u.options.body),u.options.headers=new n(u.options.headers||{}),u.options.headers.has("content-type")||u.options.headers.set("content-type","application/json"),u.options.headers.has("accept")||u.options.headers.set("accept","application/json")):("pipeTo"in u.options.body&&typeof u.options.body.pipeTo=="function"||typeof u.options.body.pipe=="function")&&("duplex"in u.options||(u.options.duplex="half")));let a;if(!u.options.signal&&u.options.timeout){const b=new r;a=setTimeout(()=>b.abort(),u.options.timeout),u.options.signal=b.signal}try{u.response=await t(u.request,u.options)}catch(b){return u.error=b,u.options.onRequestError&&await u.options.onRequestError(u),await s(u)}finally{a&&clearTimeout(a)}if(u.response.body&&!_d.has(u.response.status)&&u.options.method!=="HEAD"){const b=(u.options.parseResponse?"json":u.options.responseType)||gd(u.response.headers.get("content-type")||"");switch(b){case"json":{const E=await u.response.text(),k=u.options.parseResponse||pr;u.response._data=k(E);break}case"stream":{u.response._data=u.response.body;break}default:u.response._data=await u.response[b]()}}return u.options.onResponse&&await u.options.onResponse(u),!u.options.ignoreResponseError&&u.response.status>=400&&u.response.status<600?(u.options.onResponseError&&await u.options.onResponseError(u),await s(u)):u.response},i=async function(c,f){return(await o(c,f))._data};return i.raw=o,i.native=(...l)=>t(...l),i.create=(l={})=>gc({...e,defaults:{...e.defaults,...l}}),i}const no=function(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")}(),bd=no.fetch||(()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!"))),vd=no.Headers,wd=no.AbortController,Ed=gc({fetch:bd,Headers:vd,AbortController:wd}),Rd=Ed,Td=()=>{var e;return((e=window==null?void 0:window.__NUXT__)==null?void 0:e.config)||{}},mr=Td().app,Cd=()=>mr.baseURL,Sd=()=>mr.buildAssetsDir,ro=(...e)=>hc(mc(),Sd(),...e),mc=(...e)=>{const t=mr.cdnURL||mr.baseURL;return e.length?hc(t,...e):t};globalThis.__buildAssetsURL=ro,globalThis.__publicAssetsURL=mc;globalThis.$fetch||(globalThis.$fetch=Rd.create({baseURL:Cd()}));function _s(e,t={},n){for(const r in e){const s=e[r],o=n?`${n}:${r}`:r;typeof s=="object"&&s!==null?_s(s,t,o):typeof s=="function"&&(t[o]=s)}return t}const xd={run:e=>e()},Pd=()=>xd,yc=typeof console.createTask<"u"?console.createTask:Pd;function Ad(e,t){const n=t.shift(),r=yc(n);return e.reduce((s,o)=>s.then(()=>r.run(()=>o(...t))),Promise.resolve())}function kd(e,t){const n=t.shift(),r=yc(n);return Promise.all(e.map(s=>r.run(()=>s(...t))))}function zr(e,t){for(const n of[...e])n(t)}class Od{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,r={}){if(!t||typeof n!="function")return()=>{};const s=t;let o;for(;this._deprecatedHooks[t];)o=this._deprecatedHooks[t],t=o.to;if(o&&!r.allowDeprecated){let i=o.message;i||(i=`${s} hook has been deprecated`+(o.to?`, please use ${o.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let r,s=(...o)=>(typeof r=="function"&&r(),r=void 0,s=void 0,n(...o));return r=this.hook(t,s),r}removeHook(t,n){if(this._hooks[t]){const r=this._hooks[t].indexOf(n);r!==-1&&this._hooks[t].splice(r,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const r=this._hooks[t]||[];delete this._hooks[t];for(const s of r)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=_s(t),r=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of r.splice(0,r.length))s()}}removeHooks(t){const n=_s(t);for(const r in n)this.removeHook(r,n[r])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(Ad,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(kd,t,...n)}callHookWith(t,n,...r){const s=this._before||this._after?{name:n,args:r,context:{}}:void 0;this._before&&zr(this._before,s);const o=t(n in this._hooks?[...this._hooks[n]]:[],r);return o instanceof Promise?o.finally(()=>{this._after&&s&&zr(this._after,s)}):(this._after&&s&&zr(this._after,s),o)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}}function _c(){return new Od}function Id(e={}){let t,n=!1;const r=i=>{if(t&&t!==i)throw new Error("Context conflict")};let s;if(e.asyncContext){const i=e.AsyncLocalStorage||globalThis.AsyncLocalStorage;i?s=new i:console.warn("[unctx] `AsyncLocalStorage` is not provided.")}const o=()=>{if(s&&t===void 0){const i=s.getStore();if(i!==void 0)return i}return t};return{use:()=>{const i=o();if(i===void 0)throw new Error("Context is not available");return i},tryUse:()=>o(),set:(i,l)=>{l||r(i),t=i,n=!0},unset:()=>{t=void 0,n=!1},call:(i,l)=>{r(i),t=i;try{return s?s.run(i,l):l()}finally{n||(t=void 0)}},async callAsync(i,l){t=i;const c=()=>{t=i},f=()=>t===i?c:void 0;bs.add(f);try{const u=s?s.run(i,l):l();return n||(t=void 0),await u}finally{bs.delete(f)}}}}function Md(e={}){const t={};return{get(n,r={}){return t[n]||(t[n]=Id({...e,...r})),t[n],t[n]}}}const yr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof global<"u"?global:typeof window<"u"?window:{},oi="__unctx__",Hd=yr[oi]||(yr[oi]=Md()),Ld=(e,t={})=>Hd.get(e,t),ii="__unctx_async_handlers__",bs=yr[ii]||(yr[ii]=new Set);function An(e){const t=[];for(const s of bs){const o=s();o&&t.push(o)}const n=()=>{for(const s of t)s()};let r=e();return r&&typeof r=="object"&&"catch"in r&&(r=r.catch(s=>{throw n(),s})),[r,n]}const vs=!1,Nd=!1,Gg={componentName:"NuxtLink"},$d=null,jd="#__nuxt",bc="nuxt-app",li=36e5;function vc(e=bc){return Ld(e,{asyncContext:!1})}const Fd="__nuxt_plugin";function Bd(e){let t=0;const n={_name:bc,_scope:da(),provide:void 0,globalName:"nuxt",versions:{get nuxt(){return"3.12.4"},get vue(){return n.vueApp.version}},payload:rt({data:rt({}),state:Nt({}),once:new Set,_errors:rt({})}),static:{data:{}},runWithContext(s){return n._scope.active&&!Xi()?n._scope.run(()=>ci(n,s)):ci(n,s)},isHydrating:!0,deferHydration(){if(!n.isHydrating)return()=>{};t++;let s=!1;return()=>{if(!s&&(s=!0,t--,t===0))return n.isHydrating=!1,n.callHook("app:suspense:resolve")}},_asyncDataPromises:{},_asyncData:rt({}),_payloadRevivers:{},...e};if(window.__NUXT__)for(const s in window.__NUXT__)switch(s){case"data":case"state":case"_errors":Object.assign(n.payload[s],window.__NUXT__[s]);break;default:n.payload[s]=window.__NUXT__[s]}n.hooks=_c(),n.hook=n.hooks.hook,n.callHook=n.hooks.callHook,n.provide=(s,o)=>{const i="$"+s;Jn(n,i,o),Jn(n.vueApp.config.globalProperties,i,o)},Jn(n.vueApp,"$nuxt",n),Jn(n.vueApp.config.globalProperties,"$nuxt",n);{window.addEventListener("nuxt.preloadError",o=>{n.callHook("app:chunkError",{error:o.payload})}),window.useNuxtApp=window.useNuxtApp||pe;const s=n.hook("app:error",(...o)=>{console.error("[nuxt] error caught during app initialization",...o)});n.hook("app:mounted",s)}const r=n.payload.config;return n.provide("config",r),n}function Ud(e,t){t.hooks&&e.hooks.addHooks(t.hooks)}async function Dd(e,t){if(typeof t=="function"){const{provide:n}=await e.runWithContext(()=>t(e))||{};if(n&&typeof n=="object")for(const r in n)e.provide(r,n[r])}}async function Vd(e,t){const n=[],r=[],s=[],o=[];let i=0;async function l(c){var u;const f=((u=c.dependsOn)==null?void 0:u.filter(a=>t.some(d=>d._name===a)&&!n.includes(a)))??[];if(f.length>0)r.push([new Set(f),c]);else{const a=Dd(e,c).then(async()=>{c._name&&(n.push(c._name),await Promise.all(r.map(async([d,g])=>{d.has(c._name)&&(d.delete(c._name),d.size===0&&(i++,await l(g)))})))});c.parallel?s.push(a.catch(d=>o.push(d))):await a}}for(const c of t)Ud(e,c);for(const c of t)await l(c);if(await Promise.all(s),i)for(let c=0;c<i;c++)await Promise.all(s);if(o.length)throw o[0]}function et(e){if(typeof e=="function")return e;const t=e._name||e.name;return delete e.name,Object.assign(e.setup||(()=>{}),e,{[Fd]:!0,_name:t})}function ci(e,t,n){const r=()=>t();return vc(e._name).set(e),e.vueApp.runWithContext(r)}function Kd(e){var n;let t;return Il()&&(t=(n=Xs())==null?void 0:n.appContext.app.$nuxt),t=t||vc(e).tryUse(),t||null}function pe(e){const t=Kd(e);if(!t)throw new Error("[nuxt] instance unavailable");return t}function Or(e){return pe().$config}function Jn(e,t,n){Object.defineProperty(e,t,{get:()=>n})}function Wd(e,t){return{ctx:{table:e},matchAll:n=>Ec(n,e)}}function wc(e){const t={};for(const n in e)t[n]=n==="dynamic"?new Map(Object.entries(e[n]).map(([r,s])=>[r,wc(s)])):new Map(Object.entries(e[n]));return t}function qd(e){return Wd(wc(e))}function Ec(e,t,n){e.endsWith("/")&&(e=e.slice(0,-1)||"/");const r=[];for(const[o,i]of ai(t.wildcard))(e===o||e.startsWith(o+"/"))&&r.push(i);for(const[o,i]of ai(t.dynamic))if(e.startsWith(o+"/")){const l="/"+e.slice(o.length).split("/").splice(2).join("/");r.push(...Ec(l,i))}const s=t.static.get(e);return s&&r.push(s),r.filter(Boolean)}function ai(e){return[...e.entries()].sort((t,n)=>t[0].length-n[0].length)}function Qr(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function ws(e,t,n=".",r){if(!Qr(t))return ws(e,{},n,r);const s=Object.assign({},t);for(const o in e){if(o==="__proto__"||o==="constructor")continue;const i=e[o];i!=null&&(r&&r(s,o,i,n)||(Array.isArray(i)&&Array.isArray(s[o])?s[o]=[...i,...s[o]]:Qr(i)&&Qr(s[o])?s[o]=ws(i,s[o],(n?`${n}.`:"")+o.toString(),r):s[o]=i))}return s}function Gd(e){return(...t)=>t.reduce((n,r)=>ws(n,r,"",e),{})}const Rc=Gd();function Jd(e,t){try{return t in e}catch{return!1}}var zd=Object.defineProperty,Qd=(e,t,n)=>t in e?zd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,xt=(e,t,n)=>(Qd(e,typeof t!="symbol"?t+"":t,n),n);class Es extends Error{constructor(t,n={}){super(t,n),xt(this,"statusCode",500),xt(this,"fatal",!1),xt(this,"unhandled",!1),xt(this,"statusMessage"),xt(this,"data"),xt(this,"cause"),n.cause&&!this.cause&&(this.cause=n.cause)}toJSON(){const t={message:this.message,statusCode:Ts(this.statusCode,500)};return this.statusMessage&&(t.statusMessage=Tc(this.statusMessage)),this.data!==void 0&&(t.data=this.data),t}}xt(Es,"__h3_error__",!0);function Rs(e){if(typeof e=="string")return new Es(e);if(Xd(e))return e;const t=new Es(e.message??e.statusMessage??"",{cause:e.cause||e});if(Jd(e,"stack"))try{Object.defineProperty(t,"stack",{get(){return e.stack}})}catch{try{t.stack=e.stack}catch{}}if(e.data&&(t.data=e.data),e.statusCode?t.statusCode=Ts(e.statusCode,t.statusCode):e.status&&(t.statusCode=Ts(e.status,t.statusCode)),e.statusMessage?t.statusMessage=e.statusMessage:e.statusText&&(t.statusMessage=e.statusText),t.statusMessage){const n=t.statusMessage;Tc(t.statusMessage)!==n&&console.warn("[h3] Please prefer using `message` for longer error messages instead of `statusMessage`. In the future, `statusMessage` will be sanitized by default.")}return e.fatal!==void 0&&(t.fatal=e.fatal),e.unhandled!==void 0&&(t.unhandled=e.unhandled),t}function Xd(e){var t;return((t=e==null?void 0:e.constructor)==null?void 0:t.__h3_error__)===!0}const Yd=/[^\u0009\u0020-\u007E]/g;function Tc(e=""){return e.replace(Yd,"")}function Ts(e,t=200){return!e||(typeof e=="string"&&(e=Number.parseInt(e,10)),e<100||e>999)?t:e}const Zd=Symbol("layout-meta"),Ir=Symbol("route"),Fe=()=>{var e;return(e=pe())==null?void 0:e.$router},Cc=()=>Il()?He(Ir,pe()._route):pe()._route;const eh=()=>{try{if(pe()._processingMiddleware)return!0}catch{return!1}return!1},Jg=(e,t)=>{e||(e="/");const n=typeof e=="string"?e:"path"in e?th(e):Fe().resolve(e).href;if(t!=null&&t.open){const{target:c="_blank",windowFeatures:f={}}=t.open,u=Object.entries(f).filter(([a,d])=>d!==void 0).map(([a,d])=>`${a.toLowerCase()}=${d}`).join(", ");return open(n,c,u),Promise.resolve()}const r=$t(n,{acceptRelative:!0}),s=(t==null?void 0:t.external)||r;if(s){if(!(t!=null&&t.external))throw new Error("Navigating to an external URL is not allowed by default. Use `navigateTo(url, { external: true })`.");const{protocol:c}=new URL(n,window.location.href);if(c&&nd(c))throw new Error(`Cannot navigate to a URL with '${c}' protocol.`)}const o=eh();if(!s&&o)return e;const i=Fe(),l=pe();return s?(l._scope.stop(),t!=null&&t.replace?location.replace(n):location.href=n,o?l.isHydrating?new Promise(()=>{}):!1:Promise.resolve()):t!=null&&t.replace?i.replace(e):i.push(e)};function th(e){return fc(e.path||"",e.query||{})+(e.hash||"")}const Sc="__nuxt_error",Mr=()=>Ba(pe().payload,"error"),Vt=e=>{const t=Hr(e);try{const n=pe(),r=Mr();n.hooks.callHook("app:error",t),r.value=r.value||t}catch{throw t}return t},nh=async(e={})=>{const t=pe(),n=Mr();t.callHook("app:error:cleared",e),e.redirect&&await Fe().replace(e.redirect),n.value=$d},rh=e=>!!e&&typeof e=="object"&&Sc in e,Hr=e=>{const t=Rs(e);return Object.defineProperty(t,Sc,{value:!0,configurable:!1,writable:!1}),t},sh=-1,oh=-2,ih=-3,lh=-4,ch=-5,ah=-6;function uh(e,t){return fh(JSON.parse(e),t)}function fh(e,t){if(typeof e=="number")return s(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,r=Array(n.length);function s(o,i=!1){if(o===sh)return;if(o===ih)return NaN;if(o===lh)return 1/0;if(o===ch)return-1/0;if(o===ah)return-0;if(i)throw new Error("Invalid input");if(o in r)return r[o];const l=n[o];if(!l||typeof l!="object")r[o]=l;else if(Array.isArray(l))if(typeof l[0]=="string"){const c=l[0],f=t==null?void 0:t[c];if(f)return r[o]=f(s(l[1]));switch(c){case"Date":r[o]=new Date(l[1]);break;case"Set":const u=new Set;r[o]=u;for(let g=1;g<l.length;g+=1)u.add(s(l[g]));break;case"Map":const a=new Map;r[o]=a;for(let g=1;g<l.length;g+=2)a.set(s(l[g]),s(l[g+1]));break;case"RegExp":r[o]=new RegExp(l[1],l[2]);break;case"Object":r[o]=Object(l[1]);break;case"BigInt":r[o]=BigInt(l[1]);break;case"null":const d=Object.create(null);r[o]=d;for(let g=1;g<l.length;g+=2)d[l[g]]=s(l[g+1]);break;default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(l.length);r[o]=c;for(let f=0;f<l.length;f+=1){const u=l[f];u!==oh&&(c[f]=s(u))}}else{const c={};r[o]=c;for(const f in l){const u=l[f];c[f]=s(u)}}return r[o]}return s(0)}function dh(e){return Array.isArray(e)?e:[e]}const hh=["title","titleTemplate","script","style","noscript"],er=["base","meta","link","style","script","noscript"],ph=["title","titleTemplate","templateParams","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"],gh=["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"],xc=["tagPosition","tagPriority","tagDuplicateStrategy","children","innerHTML","textContent","processTemplateParams"],mh=typeof window<"u";function so(e){let t=9;for(let n=0;n<e.length;)t=Math.imul(t^e.charCodeAt(n++),9**9);return((t^t>>>9)+65536).toString(16).substring(1,8).toLowerCase()}function ui(e){return e._h||so(e._d?e._d:`${e.tag}:${e.textContent||e.innerHTML||""}:${Object.entries(e.props).map(([t,n])=>`${t}:${String(n)}`).join(",")}`)}function Pc(e,t){const{props:n,tag:r}=e;if(gh.includes(r))return r;if(r==="link"&&n.rel==="canonical")return"canonical";if(n.charset)return"charset";const s=["id"];r==="meta"&&s.push("name","property","http-equiv");for(const o of s)if(typeof n[o]<"u"){const i=String(n[o]);return`${r}:${o}:${i}`}return!1}function fi(e,t){return e==null?t||null:typeof e=="function"?e(t):e}async function yh(e,t,n){const r={tag:e,props:await Ac(typeof t=="object"&&typeof t!="function"&&!(t instanceof Promise)?{...t}:{[["script","noscript","style"].includes(e)?"innerHTML":"textContent"]:t},["templateParams","titleTemplate"].includes(e))};return xc.forEach(s=>{const o=typeof r.props[s]<"u"?r.props[s]:n[s];typeof o<"u"&&((!["innerHTML","textContent","children"].includes(s)||hh.includes(r.tag))&&(r[s==="children"?"innerHTML":s]=o),delete r.props[s])}),r.props.body&&(r.tagPosition="bodyClose",delete r.props.body),r.tag==="script"&&typeof r.innerHTML=="object"&&(r.innerHTML=JSON.stringify(r.innerHTML),r.props.type=r.props.type||"application/json"),Array.isArray(r.props.content)?r.props.content.map(s=>({...r,props:{...r.props,content:s}})):r}function _h(e,t){var r;const n=e==="class"?" ":";";return typeof t=="object"&&!Array.isArray(t)&&(t=Object.entries(t).filter(([,s])=>s).map(([s,o])=>e==="style"?`${s}:${o}`:s)),(r=String(Array.isArray(t)?t.join(n):t))==null?void 0:r.split(n).filter(s=>s.trim()).filter(Boolean).join(n)}async function Ac(e,t){for(const n of Object.keys(e)){if(["class","style"].includes(n)){e[n]=_h(n,e[n]);continue}if(e[n]instanceof Promise&&(e[n]=await e[n]),!t&&!xc.includes(n)){const r=String(e[n]),s=n.startsWith("data-");r==="true"||r===""?e[n]=s?"true":!0:e[n]||(s&&r==="false"?e[n]="false":delete e[n])}}return e}const bh=10;async function vh(e){const t=[];return Object.entries(e.resolvedInput).filter(([n,r])=>typeof r<"u"&&ph.includes(n)).forEach(([n,r])=>{const s=dh(r);t.push(...s.map(o=>yh(n,o,e)).flat())}),(await Promise.all(t)).flat().filter(Boolean).map((n,r)=>(n._e=e._i,e.mode&&(n._m=e.mode),n._p=(e._i<<bh)+r,n))}const di={base:-10,title:10},hi={critical:-80,high:-10,low:20};function _r(e){let t=100;const n=e.tagPriority;return typeof n=="number"?n:(e.tag==="meta"?(e.props["http-equiv"]==="content-security-policy"&&(t=-30),e.props.charset&&(t=-20),e.props.name==="viewport"&&(t=-15)):e.tag==="link"&&e.props.rel==="preconnect"?t=20:e.tag in di&&(t=di[e.tag]),typeof n=="string"&&n in hi?t+hi[n]:t)}const wh=[{prefix:"before:",offset:-1},{prefix:"after:",offset:1}],pi=["onload","onerror","onabort","onprogress","onloadstart"],ft="%separator";function tr(e,t,n){if(typeof e!="string"||!e.includes("%"))return e;function r(i){let l;return["s","pageTitle"].includes(i)?l=t.pageTitle:i.includes(".")?l=i.split(".").reduce((c,f)=>c&&c[f]||void 0,t):l=t[i],typeof l<"u"?(l||"").replace(/"/g,'\\"'):!1}let s=e;try{s=decodeURI(e)}catch{}return(s.match(/%(\w+\.+\w+)|%(\w+)/g)||[]).sort().reverse().forEach(i=>{const l=r(i.slice(1));typeof l=="string"&&(e=e.replace(new RegExp(`\\${i}(\\W|$)`,"g"),(c,f)=>`${l}${f}`).trim())}),e.includes(ft)&&(e.endsWith(ft)&&(e=e.slice(0,-ft.length).trim()),e.startsWith(ft)&&(e=e.slice(ft.length).trim()),e=e.replace(new RegExp(`\\${ft}\\s*\\${ft}`,"g"),ft),e=tr(e,{separator:n},n)),e}async function kc(e,t={}){var u;const n=t.document||e.resolvedOptions.document;if(!n||!e.dirty)return;const r={shouldRender:!0,tags:[]};if(await e.hooks.callHook("dom:beforeRender",r),!r.shouldRender)return;const s=(await e.resolveTags()).map(a=>({tag:a,id:er.includes(a.tag)?ui(a):a.tag,shouldRender:!0}));let o=e._dom;if(!o){o={elMap:{htmlAttrs:n.documentElement,bodyAttrs:n.body}};for(const a of["body","head"]){const d=(u=n[a])==null?void 0:u.children,g=[];for(const b of[...d].filter(E=>er.includes(E.tagName.toLowerCase()))){const E={tag:b.tagName.toLowerCase(),props:await Ac(b.getAttributeNames().reduce((y,m)=>({...y,[m]:b.getAttribute(m)}),{})),innerHTML:b.innerHTML};let k=1,H=Pc(E);for(;H&&g.find(y=>y._d===H);)H=`${H}:${k++}`;E._d=H||void 0,g.push(E),o.elMap[b.getAttribute("data-hid")||ui(E)]=b}}}o.pendingSideEffects={...o.sideEffects||{}},o.sideEffects={};function i(a,d,g){const b=`${a}:${d}`;o.sideEffects[b]=g,delete o.pendingSideEffects[b]}function l({id:a,$el:d,tag:g}){const b=g.tag.endsWith("Attrs");o.elMap[a]=d,b||(["textContent","innerHTML"].forEach(E=>{g[E]&&g[E]!==d[E]&&(d[E]=g[E])}),i(a,"el",()=>{var E;(E=o.elMap[a])==null||E.remove(),delete o.elMap[a]}));for(const[E,k]of Object.entries(g._eventHandlers||{}))d.getAttribute(`data-${E}`)!==""&&((g.tag==="bodyAttrs"?n.defaultView:d).addEventListener(E.replace("on",""),k.bind(d)),d.setAttribute(`data-${E}`,""));Object.entries(g.props).forEach(([E,k])=>{const H=`attr:${E}`;if(E==="class")for(const y of(k||"").split(" ").filter(Boolean))b&&i(a,`${H}:${y}`,()=>d.classList.remove(y)),!d.classList.contains(y)&&d.classList.add(y);else if(E==="style")for(const y of(k||"").split(";").filter(Boolean)){const[m,..._]=y.split(":").map(R=>R.trim());i(a,`${H}:${m}`,()=>{d.style.removeProperty(m)}),d.style.setProperty(m,_.join(":"))}else d.getAttribute(E)!==k&&d.setAttribute(E,k===!0?"":String(k)),b&&i(a,H,()=>d.removeAttribute(E))})}const c=[],f={bodyClose:void 0,bodyOpen:void 0,head:void 0};for(const a of s){const{tag:d,shouldRender:g,id:b}=a;if(g){if(d.tag==="title"){n.title=d.textContent;continue}a.$el=a.$el||o.elMap[b],a.$el?l(a):er.includes(d.tag)&&c.push(a)}}for(const a of c){const d=a.tag.tagPosition||"head";a.$el=n.createElement(a.tag.tag),l(a),f[d]=f[d]||n.createDocumentFragment(),f[d].appendChild(a.$el)}for(const a of s)await e.hooks.callHook("dom:renderTag",a,n,i);f.head&&n.head.appendChild(f.head),f.bodyOpen&&n.body.insertBefore(f.bodyOpen,n.body.firstChild),f.bodyClose&&n.body.appendChild(f.bodyClose),Object.values(o.pendingSideEffects).forEach(a=>a()),e._dom=o,e.dirty=!1,await e.hooks.callHook("dom:rendered",{renders:s})}async function Eh(e,t={}){const n=t.delayFn||(r=>setTimeout(r,10));return e._domUpdatePromise=e._domUpdatePromise||new Promise(r=>n(async()=>{await kc(e,t),delete e._domUpdatePromise,r()}))}function Rh(e){return t=>{var r,s;const n=((s=(r=t.resolvedOptions.document)==null?void 0:r.head.querySelector('script[id="unhead:payload"]'))==null?void 0:s.innerHTML)||!1;return n&&t.push(JSON.parse(n)),{mode:"client",hooks:{"entries:updated":function(o){Eh(o,e)}}}}}const Th=["templateParams","htmlAttrs","bodyAttrs"],Ch={hooks:{"tag:normalise":function({tag:e}){["hid","vmid","key"].forEach(r=>{e.props[r]&&(e.key=e.props[r],delete e.props[r])});const n=Pc(e)||(e.key?`${e.tag}:${e.key}`:!1);n&&(e._d=n)},"tags:resolve":function(e){const t={};e.tags.forEach(r=>{const s=(r.key?`${r.tag}:${r.key}`:r._d)||r._p,o=t[s];if(o){let l=r==null?void 0:r.tagDuplicateStrategy;if(!l&&Th.includes(r.tag)&&(l="merge"),l==="merge"){const c=o.props;["class","style"].forEach(f=>{c[f]&&(r.props[f]?(f==="style"&&!c[f].endsWith(";")&&(c[f]+=";"),r.props[f]=`${c[f]} ${r.props[f]}`):r.props[f]=c[f])}),t[s].props={...c,...r.props};return}else if(r._e===o._e){o._duped=o._duped||[],r._d=`${o._d}:${o._duped.length+1}`,o._duped.push(r);return}else if(_r(r)>_r(o))return}const i=Object.keys(r.props).length+(r.innerHTML?1:0)+(r.textContent?1:0);if(er.includes(r.tag)&&i===0){delete t[s];return}t[s]=r});const n=[];Object.values(t).forEach(r=>{const s=r._duped;delete r._duped,n.push(r),s&&n.push(...s)}),e.tags=n,e.tags=e.tags.filter(r=>!(r.tag==="meta"&&(r.props.name||r.props.property)&&!r.props.content))}}},Sh={mode:"server",hooks:{"tags:resolve":function(e){const t={};e.tags.filter(n=>["titleTemplate","templateParams","title"].includes(n.tag)&&n._m==="server").forEach(n=>{t[n.tag]=n.tag.startsWith("title")?n.textContent:n.props}),Object.keys(t).length&&e.tags.push({tag:"script",innerHTML:JSON.stringify(t),props:{id:"unhead:payload",type:"application/json"}})}}},xh=["script","link","bodyAttrs"],Ph=e=>({hooks:{"tags:resolve":function(t){for(const n of t.tags.filter(r=>xh.includes(r.tag)))Object.entries(n.props).forEach(([r,s])=>{r.startsWith("on")&&typeof s=="function"&&(e.ssr&&pi.includes(r)?n.props[r]=`this.dataset.${r}fired = true`:delete n.props[r],n._eventHandlers=n._eventHandlers||{},n._eventHandlers[r]=s)}),e.ssr&&n._eventHandlers&&(n.props.src||n.props.href)&&(n.key=n.key||so(n.props.src||n.props.href))},"dom:renderTag":function({$el:t,tag:n}){var r,s;for(const o of Object.keys((t==null?void 0:t.dataset)||{}).filter(i=>pi.some(l=>`${l}fired`===i))){const i=o.replace("fired","");(s=(r=n._eventHandlers)==null?void 0:r[i])==null||s.call(t,new Event(i.replace("on","")))}}}}),Ah=["link","style","script","noscript"],kh={hooks:{"tag:normalise":({tag:e})=>{e.key&&Ah.includes(e.tag)&&(e.props["data-hid"]=e._h=so(e.key))}}},Oh={hooks:{"tags:resolve":e=>{const t=n=>{var r;return(r=e.tags.find(s=>s._d===n))==null?void 0:r._p};for(const{prefix:n,offset:r}of wh)for(const s of e.tags.filter(o=>typeof o.tagPriority=="string"&&o.tagPriority.startsWith(n))){const o=t(s.tagPriority.replace(n,""));typeof o<"u"&&(s._p=o+r)}e.tags.sort((n,r)=>n._p-r._p).sort((n,r)=>_r(n)-_r(r))}}},Ih={meta:"content",link:"href",htmlAttrs:"lang"},Mh=e=>({hooks:{"tags:resolve":t=>{var l;const{tags:n}=t,r=(l=n.find(c=>c.tag==="title"))==null?void 0:l.textContent,s=n.findIndex(c=>c.tag==="templateParams"),o=s!==-1?n[s].props:{},i=o.separator||"|";delete o.separator,o.pageTitle=tr(o.pageTitle||r||"",o,i);for(const c of n.filter(f=>f.processTemplateParams!==!1)){const f=Ih[c.tag];f&&typeof c.props[f]=="string"?c.props[f]=tr(c.props[f],o,i):(c.processTemplateParams===!0||["titleTemplate","title"].includes(c.tag))&&["innerHTML","textContent"].forEach(u=>{typeof c[u]=="string"&&(c[u]=tr(c[u],o,i))})}e._templateParams=o,e._separator=i,t.tags=n.filter(c=>c.tag!=="templateParams")}}}),Hh={hooks:{"tags:resolve":e=>{const{tags:t}=e;let n=t.findIndex(s=>s.tag==="titleTemplate");const r=t.findIndex(s=>s.tag==="title");if(r!==-1&&n!==-1){const s=fi(t[n].textContent,t[r].textContent);s!==null?t[r].textContent=s||t[r].textContent:delete t[r]}else if(n!==-1){const s=fi(t[n].textContent);s!==null&&(t[n].textContent=s,t[n].tag="title",n=-1)}n!==-1&&delete t[n],e.tags=t.filter(Boolean)}}},Lh={hooks:{"tags:afterResolve":function(e){for(const t of e.tags)typeof t.innerHTML=="string"&&(t.innerHTML&&["application/ld+json","application/json"].includes(t.props.type)?t.innerHTML=t.innerHTML.replace(/</g,"\\u003C"):t.innerHTML=t.innerHTML.replace(new RegExp(`</${t.tag}`,"g"),`<\\/${t.tag}`))}}};let Oc;function Nh(e={}){const t=$h(e);return t.use(Rh()),Oc=t}function gi(e,t){return!e||e==="server"&&t||e==="client"&&!t}function $h(e={}){const t=_c();t.addHooks(e.hooks||{}),e.document=e.document||(mh?document:void 0);const n=!e.document,r=()=>{l.dirty=!0,t.callHook("entries:updated",l)};let s=0,o=[];const i=[],l={plugins:i,dirty:!1,resolvedOptions:e,hooks:t,headEntries(){return o},use(c){const f=typeof c=="function"?c(l):c;(!f.key||!i.some(u=>u.key===f.key))&&(i.push(f),gi(f.mode,n)&&t.addHooks(f.hooks||{}))},push(c,f){f==null||delete f.head;const u={_i:s++,input:c,...f};return gi(u.mode,n)&&(o.push(u),r()),{dispose(){o=o.filter(a=>a._i!==u._i),t.callHook("entries:updated",l),r()},patch(a){o=o.map(d=>(d._i===u._i&&(d.input=u.input=a),d)),r()}}},async resolveTags(){const c={tags:[],entries:[...o]};await t.callHook("entries:resolve",c);for(const f of c.entries){const u=f.resolvedInput||f.input;if(f.resolvedInput=await(f.transform?f.transform(u):u),f.resolvedInput)for(const a of await vh(f)){const d={tag:a,entry:f,resolvedOptions:l.resolvedOptions};await t.callHook("tag:normalise",d),c.tags.push(d.tag)}}return await t.callHook("tags:beforeResolve",c),await t.callHook("tags:resolve",c),await t.callHook("tags:afterResolve",c),c.tags},ssr:n};return[Ch,Sh,Ph,kh,Oh,Mh,Hh,Lh,...(e==null?void 0:e.plugins)||[]].forEach(c=>l.use(c)),l.hooks.callHook("init",l),l}function jh(){return Oc}const Fh=ic.startsWith("3");function Bh(e){return typeof e=="function"?e():ue(e)}function Cs(e,t=""){if(e instanceof Promise)return e;const n=Bh(e);return!e||!n?n:Array.isArray(n)?n.map(r=>Cs(r,t)):typeof n=="object"?Object.fromEntries(Object.entries(n).map(([r,s])=>r==="titleTemplate"||r.startsWith("on")?[r,ue(s)]:[r,Cs(s,r)])):n}const Uh={hooks:{"entries:resolve":function(e){for(const t of e.entries)t.resolvedInput=Cs(t.input)}}},Ic="usehead";function Dh(e){return{install(n){Fh&&(n.config.globalProperties.$unhead=e,n.config.globalProperties.$head=e,n.provide(Ic,e))}}.install}function Vh(e={}){e.domDelayFn=e.domDelayFn||(n=>cn(()=>setTimeout(()=>n(),0)));const t=Nh(e);return t.use(Uh),t.install=Dh(t),t}const Ss=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},xs="__unhead_injection_handler__";function Kh(e){Ss[xs]=e}function zg(){if(xs in Ss)return Ss[xs]();const e=He(Ic);return e||jh()}let nr,rr;function Wh(){return nr=$fetch(ro(`builds/meta/${Or().app.buildId}.json`),{responseType:"json"}),nr.then(e=>{rr=qd(e.matcher)}).catch(e=>{console.error("[nuxt] Error fetching app manifest.",e)}),nr}function Lr(){return nr||Wh()}async function oo(e){if(await Lr(),!rr)return console.error("[nuxt] Error creating app manifest matcher.",rr),{};try{return Rc({},...rr.matchAll(e).reverse())}catch(t){return console.error("[nuxt] Error matching route rules.",t),{}}}async function mi(e,t={}){const n=await Gh(e,t),r=pe(),s=r._payloadCache=r._payloadCache||{};return n in s||(s[n]=Hc(e).then(o=>o?Mc(n).then(i=>i||(delete s[n],null)):(s[n]=null,null))),s[n]}const qh="_payload.json";async function Gh(e,t={}){const n=new URL(e,"http://localhost");if(n.host!=="localhost"||$t(n.pathname,{acceptRelative:!0}))throw new Error("Payload URL must not include hostname: "+e);const r=Or(),s=t.hash||(t.fresh?Date.now():r.app.buildId),o=r.app.cdnURL,i=o&&await Hc(e)?o:r.app.baseURL;return to(i,n.pathname,qh+(s?`?${s}`:""))}async function Mc(e){const t=fetch(e).then(n=>n.text().then(Lc));try{return await t}catch(n){console.warn("[nuxt] Cannot load payload ",e,n)}return null}async function Hc(e=Cc().path){if(e=eo(e),(await Lr()).prerendered.includes(e))return!0;const n=await oo(e);return!!n.prerender&&!n.redirect}let zn=null;async function Jh(){if(zn)return zn;const e=document.getElementById("__NUXT_DATA__");if(!e)return{};const t=await Lc(e.textContent||""),n=e.dataset.src?await Mc(e.dataset.src):void 0;return zn={...t,...n,...window.__NUXT__},zn}async function Lc(e){return await uh(e,pe()._payloadRevivers)}function zh(e,t){pe()._payloadRevivers[e]=t}const yi={NuxtError:e=>Hr(e),EmptyShallowRef:e=>En(e==="_"?void 0:e==="0n"?BigInt(0):pr(e)),EmptyRef:e=>_t(e==="_"?void 0:e==="0n"?BigInt(0):pr(e)),ShallowRef:e=>En(e),ShallowReactive:e=>rt(e),Ref:e=>_t(e),Reactive:e=>Nt(e)},Qh=et({name:"nuxt:revive-payload:client",order:-30,async setup(e){let t,n;for(const r in yi)zh(r,yi[r]);Object.assign(e.payload,([t,n]=An(()=>e.runWithContext(Jh)),t=await t,n(),t)),window.__NUXT__=e.payload}}),Xh=[],Yh=et({name:"nuxt:head",enforce:"pre",setup(e){const t=Vh({plugins:Xh});Kh(()=>pe().vueApp._context.provides.usehead),e.vueApp.use(t);{let n=!0;const r=async()=>{n=!1,await kc(t)};t.hooks.hook("dom:beforeRender",s=>{s.shouldRender=!n}),e.hooks.hook("page:start",()=>{n=!0}),e.hooks.hook("page:finish",()=>{e.isHydrating||r()}),e.hooks.hook("app:error",r),e.hooks.hook("app:suspense:resolve",r)}}});/*!
  * vue-router v4.4.3
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Dt=typeof document<"u";function Zh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ie=Object.assign;function Xr(e,t){const n={};for(const r in t){const s=t[r];n[r]=qe(s)?s.map(e):e(s)}return n}const _n=()=>{},qe=Array.isArray,Nc=/#/g,ep=/&/g,tp=/\//g,np=/=/g,rp=/\?/g,$c=/\+/g,sp=/%5B/g,op=/%5D/g,jc=/%5E/g,ip=/%60/g,Fc=/%7B/g,lp=/%7C/g,Bc=/%7D/g,cp=/%20/g;function io(e){return encodeURI(""+e).replace(lp,"|").replace(sp,"[").replace(op,"]")}function ap(e){return io(e).replace(Fc,"{").replace(Bc,"}").replace(jc,"^")}function Ps(e){return io(e).replace($c,"%2B").replace(cp,"+").replace(Nc,"%23").replace(ep,"%26").replace(ip,"`").replace(Fc,"{").replace(Bc,"}").replace(jc,"^")}function up(e){return Ps(e).replace(np,"%3D")}function fp(e){return io(e).replace(Nc,"%23").replace(rp,"%3F")}function dp(e){return e==null?"":fp(e).replace(tp,"%2F")}function kn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const hp=/\/$/,pp=e=>e.replace(hp,"");function Yr(e,t,n="/"){let r,s={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(r=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),s=e(o)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=_p(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:kn(i)}}function gp(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function _i(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function mp(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&rn(t.matched[r],n.matched[s])&&Uc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function rn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Uc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!yp(e[n],t[n]))return!1;return!0}function yp(e,t){return qe(e)?bi(e,t):qe(t)?bi(t,e):e===t}function bi(e,t){return qe(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function _p(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const De={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var On;(function(e){e.pop="pop",e.push="push"})(On||(On={}));var bn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(bn||(bn={}));function bp(e){if(!e)if(Dt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),pp(e)}const vp=/^[^#]+#/;function wp(e,t){return e.replace(vp,"#")+t}function Ep(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Nr=()=>({left:window.scrollX,top:window.scrollY});function Rp(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Ep(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function vi(e,t){return(history.state?history.state.position-t:-1)+e}const As=new Map;function Tp(e,t){As.set(e,t)}function Cp(e){const t=As.get(e);return As.delete(e),t}let Sp=()=>location.protocol+"//"+location.host;function Dc(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let l=s.includes(e.slice(o))?e.slice(o).length:1,c=s.slice(l);return c[0]!=="/"&&(c="/"+c),_i(c,"")}return _i(n,e)+r+s}function xp(e,t,n,r){let s=[],o=[],i=null;const l=({state:d})=>{const g=Dc(e,location),b=n.value,E=t.value;let k=0;if(d){if(n.value=g,t.value=d,i&&i===b){i=null;return}k=E?d.position-E.position:0}else r(g);s.forEach(H=>{H(n.value,b,{delta:k,type:On.pop,direction:k?k>0?bn.forward:bn.back:bn.unknown})})};function c(){i=n.value}function f(d){s.push(d);const g=()=>{const b=s.indexOf(d);b>-1&&s.splice(b,1)};return o.push(g),g}function u(){const{history:d}=window;d.state&&d.replaceState(ie({},d.state,{scroll:Nr()}),"")}function a(){for(const d of o)d();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:f,destroy:a}}function wi(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Nr():null}}function Pp(e){const{history:t,location:n}=window,r={value:Dc(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,f,u){const a=e.indexOf("#"),d=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+c:Sp()+e+c;try{t[u?"replaceState":"pushState"](f,"",d),s.value=f}catch(g){console.error(g),n[u?"replace":"assign"](d)}}function i(c,f){const u=ie({},t.state,wi(s.value.back,c,s.value.forward,!0),f,{position:s.value.position});o(c,u,!0),r.value=c}function l(c,f){const u=ie({},s.value,t.state,{forward:c,scroll:Nr()});o(u.current,u,!0);const a=ie({},wi(r.value,c,null),{position:u.position+1},f);o(c,a,!1),r.value=c}return{location:r,state:s,push:l,replace:i}}function Vc(e){e=bp(e);const t=Pp(e),n=xp(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=ie({location:"",base:e,go:r,createHref:wp.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Ap(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Vc(e)}function kp(e){return typeof e=="string"||e&&typeof e=="object"}function Kc(e){return typeof e=="string"||typeof e=="symbol"}const Wc=Symbol("");var Ei;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ei||(Ei={}));function sn(e,t){return ie(new Error,{type:e,[Wc]:!0},t)}function tt(e,t){return e instanceof Error&&Wc in e&&(t==null||!!(e.type&t))}const Ri="[^/]+?",Op={sensitive:!1,strict:!1,start:!0,end:!0},Ip=/[.+*?^${}()[\]/\\]/g;function Mp(e,t){const n=ie({},Op,t),r=[];let s=n.start?"^":"";const o=[];for(const f of e){const u=f.length?[]:[90];n.strict&&!f.length&&(s+="/");for(let a=0;a<f.length;a++){const d=f[a];let g=40+(n.sensitive?.25:0);if(d.type===0)a||(s+="/"),s+=d.value.replace(Ip,"\\$&"),g+=40;else if(d.type===1){const{value:b,repeatable:E,optional:k,regexp:H}=d;o.push({name:b,repeatable:E,optional:k});const y=H||Ri;if(y!==Ri){g+=10;try{new RegExp(`(${y})`)}catch(_){throw new Error(`Invalid custom RegExp for param "${b}" (${y}): `+_.message)}}let m=E?`((?:${y})(?:/(?:${y}))*)`:`(${y})`;a||(m=k&&f.length<2?`(?:/${m})`:"/"+m),k&&(m+="?"),s+=m,g+=20,k&&(g+=-8),E&&(g+=-20),y===".*"&&(g+=-50)}u.push(g)}r.push(u)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function l(f){const u=f.match(i),a={};if(!u)return null;for(let d=1;d<u.length;d++){const g=u[d]||"",b=o[d-1];a[b.name]=g&&b.repeatable?g.split("/"):g}return a}function c(f){let u="",a=!1;for(const d of e){(!a||!u.endsWith("/"))&&(u+="/"),a=!1;for(const g of d)if(g.type===0)u+=g.value;else if(g.type===1){const{value:b,repeatable:E,optional:k}=g,H=b in f?f[b]:"";if(qe(H)&&!E)throw new Error(`Provided param "${b}" is an array but it is not repeatable (* or + modifiers)`);const y=qe(H)?H.join("/"):H;if(!y)if(k)d.length<2&&(u.endsWith("/")?u=u.slice(0,-1):a=!0);else throw new Error(`Missing required param "${b}"`);u+=y}}return u||"/"}return{re:i,score:r,keys:o,parse:l,stringify:c}}function Hp(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qc(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Hp(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(Ti(r))return 1;if(Ti(s))return-1}return s.length-r.length}function Ti(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Lp={type:0,value:""},Np=/[a-zA-Z0-9_]/;function $p(e){if(!e)return[[]];if(e==="/")return[[Lp]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${f}": ${g}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let l=0,c,f="",u="";function a(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function d(){f+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(f&&a(),i()):c===":"?(a(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:Np.test(c)?d():(a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),a(),i(),s}function jp(e,t,n){const r=Mp($p(e.path),n),s=ie(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Fp(e,t){const n=[],r=new Map;t=xi({strict:!1,end:!0,sensitive:!1},t);function s(a){return r.get(a)}function o(a,d,g){const b=!g,E=Bp(a);E.aliasOf=g&&g.record;const k=xi(t,a),H=[E];if("alias"in a){const _=typeof a.alias=="string"?[a.alias]:a.alias;for(const R of _)H.push(ie({},E,{components:g?g.record.components:E.components,path:R,aliasOf:g?g.record:E}))}let y,m;for(const _ of H){const{path:R}=_;if(d&&R[0]!=="/"){const w=d.record.path,A=w[w.length-1]==="/"?"":"/";_.path=d.record.path+(R&&A+R)}if(y=jp(_,d,k),g?g.alias.push(y):(m=m||y,m!==y&&m.alias.push(y),b&&a.name&&!Si(y)&&i(a.name)),Gc(y)&&c(y),E.children){const w=E.children;for(let A=0;A<w.length;A++)o(w[A],y,g&&g.children[A])}g=g||y}return m?()=>{i(m)}:_n}function i(a){if(Kc(a)){const d=r.get(a);d&&(r.delete(a),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(a);d>-1&&(n.splice(d,1),a.record.name&&r.delete(a.record.name),a.children.forEach(i),a.alias.forEach(i))}}function l(){return n}function c(a){const d=Vp(a,n);n.splice(d,0,a),a.record.name&&!Si(a)&&r.set(a.record.name,a)}function f(a,d){let g,b={},E,k;if("name"in a&&a.name){if(g=r.get(a.name),!g)throw sn(1,{location:a});k=g.record.name,b=ie(Ci(d.params,g.keys.filter(m=>!m.optional).concat(g.parent?g.parent.keys.filter(m=>m.optional):[]).map(m=>m.name)),a.params&&Ci(a.params,g.keys.map(m=>m.name))),E=g.stringify(b)}else if(a.path!=null)E=a.path,g=n.find(m=>m.re.test(E)),g&&(b=g.parse(E),k=g.record.name);else{if(g=d.name?r.get(d.name):n.find(m=>m.re.test(d.path)),!g)throw sn(1,{location:a,currentLocation:d});k=g.record.name,b=ie({},d.params,a.params),E=g.stringify(b)}const H=[];let y=g;for(;y;)H.unshift(y.record),y=y.parent;return{name:k,path:E,params:b,matched:H,meta:Dp(H)}}e.forEach(a=>o(a));function u(){n.length=0,r.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:s}}function Ci(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Bp(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Up(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function Up(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Si(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Dp(e){return e.reduce((t,n)=>ie(t,n.meta),{})}function xi(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Vp(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;qc(e,t[o])<0?r=o:n=o+1}const s=Kp(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function Kp(e){let t=e;for(;t=t.parent;)if(Gc(t)&&qc(e,t)===0)return t}function Gc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Wp(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace($c," "),i=o.indexOf("="),l=kn(i<0?o:o.slice(0,i)),c=i<0?null:kn(o.slice(i+1));if(l in t){let f=t[l];qe(f)||(f=t[l]=[f]),f.push(c)}else t[l]=c}return t}function Pi(e){let t="";for(let n in e){const r=e[n];if(n=up(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(qe(r)?r.map(o=>o&&Ps(o)):[r&&Ps(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function qp(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=qe(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Gp=Symbol(""),Ai=Symbol(""),lo=Symbol(""),Jc=Symbol(""),ks=Symbol("");function fn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function gt(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((l,c)=>{const f=d=>{d===!1?c(sn(4,{from:n,to:t})):d instanceof Error?c(d):kp(d)?c(sn(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),l())},u=o(()=>e.call(r&&r.instances[s],t,n,f));let a=Promise.resolve(u);e.length<3&&(a=a.then(f)),a.catch(d=>c(d))})}function Zr(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Jp(c)){const u=(c.__vccOpts||c)[t];u&&o.push(gt(u,n,r,i,l,s))}else{let f=c();o.push(()=>f.then(u=>{if(!u)return Promise.reject(new Error(`Couldn't resolve component "${l}" at "${i.path}"`));const a=Zh(u)?u.default:u;i.components[l]=a;const g=(a.__vccOpts||a)[t];return g&&gt(g,n,r,i,l,s)()}))}}return o}function Jp(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ki(e){const t=He(lo),n=He(Jc),r=Ke(()=>{const c=ue(e.to);return t.resolve(c)}),s=Ke(()=>{const{matched:c}=r.value,{length:f}=c,u=c[f-1],a=n.matched;if(!u||!a.length)return-1;const d=a.findIndex(rn.bind(null,u));if(d>-1)return d;const g=Oi(c[f-2]);return f>1&&Oi(u)===g&&a[a.length-1].path!==g?a.findIndex(rn.bind(null,c[f-2])):d}),o=Ke(()=>s.value>-1&&Yp(n.params,r.value.params)),i=Ke(()=>s.value>-1&&s.value===n.matched.length-1&&Uc(n.params,r.value.params));function l(c={}){return Xp(c)?t[ue(e.replace)?"replace":"push"](ue(e.to)).catch(_n):Promise.resolve()}return{route:r,href:Ke(()=>r.value.href),isActive:o,isExactActive:i,navigate:l}}const zp=Mn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ki,setup(e,{slots:t}){const n=Nt(ki(e)),{options:r}=He(lo),s=Ke(()=>({[Ii(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Ii(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ye("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),Qp=zp;function Xp(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Yp(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!qe(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Oi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ii=(e,t,n)=>e??t??n,Zp=Mn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=He(ks),s=Ke(()=>e.route||r.value),o=He(Ai,0),i=Ke(()=>{let f=ue(o);const{matched:u}=s.value;let a;for(;(a=u[f])&&!a.components;)f++;return f}),l=Ke(()=>s.value.matched[i.value]);Xt(Ai,Ke(()=>i.value+1)),Xt(Gp,l),Xt(ks,s);const c=_t();return Yt(()=>[c.value,l.value,e.name],([f,u,a],[d,g,b])=>{u&&(u.instances[a]=f,g&&g!==u&&f&&f===d&&(u.leaveGuards.size||(u.leaveGuards=g.leaveGuards),u.updateGuards.size||(u.updateGuards=g.updateGuards))),f&&u&&(!g||!rn(u,g)||!d)&&(u.enterCallbacks[a]||[]).forEach(E=>E(f))},{flush:"post"}),()=>{const f=s.value,u=e.name,a=l.value,d=a&&a.components[u];if(!d)return Mi(n.default,{Component:d,route:f});const g=a.props[u],b=g?g===!0?f.params:typeof g=="function"?g(f):g:null,k=Ye(d,ie({},b,t,{onVnodeUnmounted:H=>{H.component.isUnmounted&&(a.instances[u]=null)},ref:c}));return Mi(n.default,{Component:k,route:f})||k}}});function Mi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const zc=Zp;function eg(e){const t=Fp(e.routes,e),n=e.parseQuery||Wp,r=e.stringifyQuery||Pi,s=e.history,o=fn(),i=fn(),l=fn(),c=En(De);let f=De;Dt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Xr.bind(null,C=>""+C),a=Xr.bind(null,dp),d=Xr.bind(null,kn);function g(C,F){let j,V;return Kc(C)?(j=t.getRecordMatcher(C),V=F):V=C,t.addRoute(V,j)}function b(C){const F=t.getRecordMatcher(C);F&&t.removeRoute(F)}function E(){return t.getRoutes().map(C=>C.record)}function k(C){return!!t.getRecordMatcher(C)}function H(C,F){if(F=ie({},F||c.value),typeof C=="string"){const p=Yr(n,C,F.path),v=t.resolve({path:p.path},F),S=s.createHref(p.fullPath);return ie(p,v,{params:d(v.params),hash:kn(p.hash),redirectedFrom:void 0,href:S})}let j;if(C.path!=null)j=ie({},C,{path:Yr(n,C.path,F.path).path});else{const p=ie({},C.params);for(const v in p)p[v]==null&&delete p[v];j=ie({},C,{params:a(p)}),F.params=a(F.params)}const V=t.resolve(j,F),se=C.hash||"";V.params=u(d(V.params));const ae=gp(r,ie({},C,{hash:ap(se),path:V.path})),h=s.createHref(ae);return ie({fullPath:ae,hash:se,query:r===Pi?qp(C.query):C.query||{}},V,{redirectedFrom:void 0,href:h})}function y(C){return typeof C=="string"?Yr(n,C,c.value.path):ie({},C)}function m(C,F){if(f!==C)return sn(8,{from:F,to:C})}function _(C){return A(C)}function R(C){return _(ie(y(C),{replace:!0}))}function w(C){const F=C.matched[C.matched.length-1];if(F&&F.redirect){const{redirect:j}=F;let V=typeof j=="function"?j(C):j;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=y(V):{path:V},V.params={}),ie({query:C.query,hash:C.hash,params:V.path!=null?{}:C.params},V)}}function A(C,F){const j=f=H(C),V=c.value,se=C.state,ae=C.force,h=C.replace===!0,p=w(j);if(p)return A(ie(y(p),{state:typeof p=="object"?ie({},se,p.state):se,force:ae,replace:h}),F||j);const v=j;v.redirectedFrom=F;let S;return!ae&&mp(r,V,j)&&(S=sn(16,{to:v,from:V}),Ge(V,V,!0,!1)),(S?Promise.resolve(S):I(v,V)).catch(T=>tt(T)?tt(T,2)?T:lt(T):z(T,v,V)).then(T=>{if(T){if(tt(T,2))return A(ie({replace:h},y(T.to),{state:typeof T.to=="object"?ie({},se,T.to.state):se,force:ae}),F||v)}else T=M(v,V,!0,h,se);return q(v,V,T),T})}function U(C,F){const j=m(C,F);return j?Promise.reject(j):Promise.resolve()}function O(C){const F=Ft.values().next().value;return F&&typeof F.runWithContext=="function"?F.runWithContext(C):C()}function I(C,F){let j;const[V,se,ae]=tg(C,F);j=Zr(V.reverse(),"beforeRouteLeave",C,F);for(const p of V)p.leaveGuards.forEach(v=>{j.push(gt(v,C,F))});const h=U.bind(null,C,F);return j.push(h),Le(j).then(()=>{j=[];for(const p of o.list())j.push(gt(p,C,F));return j.push(h),Le(j)}).then(()=>{j=Zr(se,"beforeRouteUpdate",C,F);for(const p of se)p.updateGuards.forEach(v=>{j.push(gt(v,C,F))});return j.push(h),Le(j)}).then(()=>{j=[];for(const p of ae)if(p.beforeEnter)if(qe(p.beforeEnter))for(const v of p.beforeEnter)j.push(gt(v,C,F));else j.push(gt(p.beforeEnter,C,F));return j.push(h),Le(j)}).then(()=>(C.matched.forEach(p=>p.enterCallbacks={}),j=Zr(ae,"beforeRouteEnter",C,F,O),j.push(h),Le(j))).then(()=>{j=[];for(const p of i.list())j.push(gt(p,C,F));return j.push(h),Le(j)}).catch(p=>tt(p,8)?p:Promise.reject(p))}function q(C,F,j){l.list().forEach(V=>O(()=>V(C,F,j)))}function M(C,F,j,V,se){const ae=m(C,F);if(ae)return ae;const h=F===De,p=Dt?history.state:{};j&&(V||h?s.replace(C.fullPath,ie({scroll:h&&p&&p.scroll},se)):s.push(C.fullPath,se)),c.value=C,Ge(C,F,j,h),lt()}let K;function ee(){K||(K=s.listen((C,F,j)=>{if(!$n.listening)return;const V=H(C),se=w(V);if(se){A(ie(se,{replace:!0}),V).catch(_n);return}f=V;const ae=c.value;Dt&&Tp(vi(ae.fullPath,j.delta),Nr()),I(V,ae).catch(h=>tt(h,12)?h:tt(h,2)?(A(h.to,V).then(p=>{tt(p,20)&&!j.delta&&j.type===On.pop&&s.go(-1,!1)}).catch(_n),Promise.reject()):(j.delta&&s.go(-j.delta,!1),z(h,V,ae))).then(h=>{h=h||M(V,ae,!1),h&&(j.delta&&!tt(h,8)?s.go(-j.delta,!1):j.type===On.pop&&tt(h,20)&&s.go(-1,!1)),q(V,ae,h)}).catch(_n)}))}let ne=fn(),D=fn(),X;function z(C,F,j){lt(C);const V=D.list();return V.length?V.forEach(se=>se(C,F,j)):console.error(C),Promise.reject(C)}function ge(){return X&&c.value!==De?Promise.resolve():new Promise((C,F)=>{ne.add([C,F])})}function lt(C){return X||(X=!C,ee(),ne.list().forEach(([F,j])=>C?j(C):F()),ne.reset()),C}function Ge(C,F,j,V){const{scrollBehavior:se}=e;if(!Dt||!se)return Promise.resolve();const ae=!j&&Cp(vi(C.fullPath,0))||(V||!j)&&history.state&&history.state.scroll||null;return cn().then(()=>se(C,F,ae)).then(h=>h&&Rp(h)).catch(h=>z(h,C,F))}const Ce=C=>s.go(C);let jt;const Ft=new Set,$n={currentRoute:c,listening:!0,addRoute:g,removeRoute:b,clearRoutes:t.clearRoutes,hasRoute:k,getRoutes:E,resolve:H,options:e,push:_,replace:R,go:Ce,back:()=>Ce(-1),forward:()=>Ce(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:D.add,isReady:ge,install(C){const F=this;C.component("RouterLink",Qp),C.component("RouterView",zc),C.config.globalProperties.$router=F,Object.defineProperty(C.config.globalProperties,"$route",{enumerable:!0,get:()=>ue(c)}),Dt&&!jt&&c.value===De&&(jt=!0,_(s.location).catch(se=>{}));const j={};for(const se in De)Object.defineProperty(j,se,{get:()=>c.value[se],enumerable:!0});C.provide(lo,F),C.provide(Jc,rt(j)),C.provide(ks,c);const V=C.unmount;Ft.add(C),C.unmount=function(){Ft.delete(C),Ft.size<1&&(f=De,K&&K(),K=null,c.value=De,jt=!1,X=!1),V()}}};function Le(C){return C.reduce((F,j)=>F.then(()=>O(j)),Promise.resolve())}return $n}function tg(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>rn(f,l))?r.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(f=>rn(f,c))||s.push(c))}return[n,r,s]}const ng=(e,t)=>t.path.replace(/(:\w+)\([^)]+\)/g,"$1").replace(/(:\w+)[?+*]/g,"$1").replace(/:\w+/g,n=>{var r;return((r=e.params[n.slice(1)])==null?void 0:r.toString())||""}),Os=(e,t)=>{const n=e.route.matched.find(s=>{var o;return((o=s.components)==null?void 0:o.default)===e.Component.type}),r=t??(n==null?void 0:n.meta.key)??(n&&ng(e.route,n));return typeof r=="function"?r(e.route):r},rg=(e,t)=>({default:()=>e?Ye(Qa,e===!0?{}:e,t):t});function co(e){return Array.isArray(e)?e:[e]}const sg="modulepreload",og=function(e,t){return e[0]==="."?new URL(e,t).href:e},Hi={},ig=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){const o=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));s=Promise.all(n.map(c=>{if(c=og(c,r),c in Hi)return;Hi[c]=!0;const f=c.endsWith(".css"),u=f?'[rel="stylesheet"]':"";if(!!r)for(let g=o.length-1;g>=0;g--){const b=o[g];if(b.href===c&&(!f||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${u}`))return;const d=document.createElement("link");if(d.rel=f?"stylesheet":sg,f||(d.as="script",d.crossOrigin=""),d.href=c,l&&d.setAttribute("nonce",l),document.head.appendChild(d),f)return new Promise((g,b)=>{d.addEventListener("load",g),d.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${c}`)))})}))}return s.then(()=>t()).catch(o=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=o,window.dispatchEvent(i),!i.defaultPrevented)throw o})},Oe=(...e)=>ig(...e).catch(t=>{const n=new Event("nuxt.preloadError");throw n.payload=t,window.dispatchEvent(n),t}),Li=[{name:"contact",path:"/contact",component:()=>Oe(()=>import("./XfRs8NHu.js"),__vite__mapDeps([0,1,2,3]),import.meta.url).then(e=>e.default||e)},{name:"demo",path:"/demo",component:()=>Oe(()=>import("./Co61pfxM.js"),__vite__mapDeps([4,1,2,5]),import.meta.url).then(e=>e.default||e)},{name:"docs-scanonweb-api",path:"/docs/scanonweb-api",component:()=>Oe(()=>import("./D4VnDwTm.js"),__vite__mapDeps([6,1,7,2,8]),import.meta.url).then(e=>e.default||e)},{name:"docs-scanonweb-faq",path:"/docs/scanonweb-faq",component:()=>Oe(()=>import("./Bcgw38WA.js"),__vite__mapDeps([9,1,7,2,10]),import.meta.url).then(e=>e.default||e)},{name:"docs-scanonweb-features",path:"/docs/scanonweb-features",component:()=>Oe(()=>import("./VF4cvea4.js"),__vite__mapDeps([11,1,7,2,12]),import.meta.url).then(e=>e.default||e)},{name:"docs-scanonweb-getting-started",path:"/docs/scanonweb-getting-started",component:()=>Oe(()=>import("./DoxFx5l-.js"),__vite__mapDeps([13,1,7,2,14]),import.meta.url).then(e=>e.default||e)},{name:"documents",path:"/documents",component:()=>Oe(()=>import("./B04A3j4W.js"),__vite__mapDeps([15,1,2,16]),import.meta.url).then(e=>e.default||e)},{name:"download",path:"/download",component:()=>Oe(()=>import("./C1WhqXxa.js"),__vite__mapDeps([17,2,1]),import.meta.url).then(e=>e.default||e)},{name:"index",path:"/",component:()=>Oe(()=>import("./Ru65YT6T.js"),__vite__mapDeps([18,2,1]),import.meta.url).then(e=>e.default||e)},{name:"products",path:"/products",component:()=>Oe(()=>import("./CY8I3eQZ.js"),__vite__mapDeps([19,2,1,20]),import.meta.url).then(e=>e.default||e)},{name:"purchase",path:"/purchase",component:()=>Oe(()=>import("./4-5J9pYw.js"),__vite__mapDeps([21,2,1,22]),import.meta.url).then(e=>e.default||e)}],lg=(e,t,n)=>(t=t===!0?{}:t,{default:()=>{var r;return t?Ye(e,t,n):(r=n.default)==null?void 0:r.call(n)}});function Ni(e){const t=(e==null?void 0:e.meta.key)??e.path.replace(/(:\w+)\([^)]+\)/g,"$1").replace(/(:\w+)[?+*]/g,"$1").replace(/:\w+/g,n=>{var r;return((r=e.params[n.slice(1)])==null?void 0:r.toString())||""});return typeof t=="function"?t(e):t}function cg(e,t){return e===t||t===De?!1:Ni(e)!==Ni(t)?!0:!e.matched.every((r,s)=>{var o,i;return r.components&&r.components.default===((i=(o=t.matched[s])==null?void 0:o.components)==null?void 0:i.default)})}const ag={scrollBehavior(e,t,n){var f;const r=pe(),s=((f=Fe().options)==null?void 0:f.scrollBehaviorType)??"auto";let o=n||void 0;const i=typeof e.meta.scrollToTop=="function"?e.meta.scrollToTop(e,t):e.meta.scrollToTop;if(!o&&t&&e&&i!==!1&&cg(e,t)&&(o={left:0,top:0}),e.path===t.path)return t.hash&&!e.hash?{left:0,top:0}:e.hash?{el:e.hash,top:$i(e.hash),behavior:s}:!1;const l=u=>!!(u.meta.pageTransition??vs),c=l(t)&&l(e)?"page:transition:finish":"page:finish";return new Promise(u=>{r.hooks.hookOnce(c,async()=>{await new Promise(a=>setTimeout(a,0)),e.hash&&(o={el:e.hash,top:$i(e.hash),behavior:s}),u(o)})})}};function $i(e){try{const t=document.querySelector(e);if(t)return Number.parseFloat(getComputedStyle(t).scrollMarginTop)+Number.parseFloat(getComputedStyle(document.documentElement).scrollPaddingTop)}catch{}return 0}const ug={hashMode:!1,scrollBehaviorType:"auto"},ke={...ug,...ag},fg=async e=>{var c;let t,n;if(!((c=e.meta)!=null&&c.validate))return;const r=pe(),s=Fe();if(([t,n]=An(()=>Promise.resolve(e.meta.validate(e))),t=await t,n(),t)===!0)return;const i=Hr({statusCode:404,statusMessage:`Page Not Found: ${e.fullPath}`,data:{path:e.fullPath}}),l=s.beforeResolve(f=>{if(l(),f===e){const u=s.afterEach(async()=>{u(),await r.runWithContext(()=>Vt(i)),window.history.pushState({},"",e.fullPath)});return!1}})},dg=async e=>{let t,n;const r=([t,n]=An(()=>oo(e.path)),t=await t,n(),t);if(r.redirect)return $t(r.redirect,{acceptRelative:!0})?(window.location.href=r.redirect,!1):r.redirect},hg=[fg,dg],vn={};function pg(e,t,n){const{pathname:r,search:s,hash:o}=t,i=e.indexOf("#");if(i>-1){const f=o.includes(e.slice(i))?e.slice(i).length:1;let u=o.slice(f);return u[0]!=="/"&&(u="/"+u),ni(u,"")}const l=ni(r,e),c=!n||id(l,n,{trailingSlash:!0})?l:n;return c+(c.includes("?")?"":s)+o}const gg=et({name:"nuxt:router",enforce:"pre",async setup(e){var k,H;let t,n,r=Or().app.baseURL;ke.hashMode&&!r.includes("#")&&(r+="#");const s=((k=ke.history)==null?void 0:k.call(ke,r))??(ke.hashMode?Ap(r):Vc(r)),o=((H=ke.routes)==null?void 0:H.call(ke,Li))??Li;let i;const l=eg({...ke,scrollBehavior:(y,m,_)=>{if(m===De){i=_;return}if(ke.scrollBehavior){if(l.options.scrollBehavior=ke.scrollBehavior,"scrollRestoration"in window.history){const R=l.beforeEach(()=>{R(),window.history.scrollRestoration="manual"})}return ke.scrollBehavior(y,De,i||_)}},history:s,routes:o});"scrollRestoration"in window.history&&(window.history.scrollRestoration="auto"),e.vueApp.use(l);const c=En(l.currentRoute.value);l.afterEach((y,m)=>{c.value=m}),Object.defineProperty(e.vueApp.config.globalProperties,"previousRoute",{get:()=>c.value});const f=pg(r,window.location,e.payload.path),u=En(l.currentRoute.value),a=()=>{u.value=l.currentRoute.value};e.hook("page:finish",a),l.afterEach((y,m)=>{var _,R,w,A;((R=(_=y.matched[0])==null?void 0:_.components)==null?void 0:R.default)===((A=(w=m.matched[0])==null?void 0:w.components)==null?void 0:A.default)&&a()});const d={};for(const y in u.value)Object.defineProperty(d,y,{get:()=>u.value[y]});e._route=rt(d),e._middleware=e._middleware||{global:[],named:{}};const g=Mr();l.afterEach(async(y,m,_)=>{delete e._processingMiddleware,!e.isHydrating&&g.value&&await e.runWithContext(nh),_&&await e.callHook("page:loading:end"),y.matched.length===0&&await e.runWithContext(()=>Vt(Rs({statusCode:404,fatal:!1,statusMessage:`Page not found: ${y.fullPath}`,data:{path:y.fullPath}})))});try{[t,n]=An(()=>l.isReady()),await t,n()}catch(y){[t,n]=An(()=>e.runWithContext(()=>Vt(y))),await t,n()}const b=f!==l.currentRoute.value.fullPath?l.resolve(f):l.currentRoute.value;a();const E=e.payload.state._layout;return l.beforeEach(async(y,m)=>{var _;await e.callHook("page:loading:start"),y.meta=Nt(y.meta),e.isHydrating&&E&&!wt(y.meta.layout)&&(y.meta.layout=E),e._processingMiddleware=!0;{const R=new Set([...hg,...e._middleware.global]);for(const w of y.matched){const A=w.meta.middleware;if(A)for(const U of co(A))R.add(U)}{const w=await e.runWithContext(()=>oo(y.path));if(w.appMiddleware)for(const A in w.appMiddleware)w.appMiddleware[A]?R.add(A):R.delete(A)}for(const w of R){const A=typeof w=="string"?e._middleware.named[w]||await((_=vn[w])==null?void 0:_.call(vn).then(O=>O.default||O)):w;if(!A)throw new Error(`Unknown route middleware: '${w}'.`);const U=await e.runWithContext(()=>A(y,m));if(!e.payload.serverRendered&&e.isHydrating&&(U===!1||U instanceof Error)){const O=U||Rs({statusCode:404,statusMessage:`Page Not Found: ${f}`});return await e.runWithContext(()=>Vt(O)),!1}if(U!==!0&&(U||U===!1))return U}}}),l.onError(async()=>{delete e._processingMiddleware,await e.callHook("page:loading:end")}),e.hooks.hookOnce("app:created",async()=>{try{"name"in b&&(b.name=void 0),await l.replace({...b,force:!0}),l.options.scrollBehavior=ke.scrollBehavior}catch(y){await e.runWithContext(()=>Vt(y))}}),{provide:{router:l}}}}),ji=globalThis.requestIdleCallback||(e=>{const t=Date.now(),n={didTimeout:!1,timeRemaining:()=>Math.max(0,50-(Date.now()-t))};return setTimeout(()=>{e(n)},1)}),Qg=globalThis.cancelIdleCallback||(e=>{clearTimeout(e)}),ao=e=>{const t=pe();t.isHydrating?t.hooks.hookOnce("app:suspense:resolve",()=>{ji(()=>e())}):ji(()=>e())},mg=et({name:"nuxt:payload",setup(e){Fe().beforeResolve(async(t,n)=>{if(t.path===n.path)return;const r=await mi(t.path);r&&Object.assign(e.static.data,r.data)}),ao(()=>{var t;e.hooks.hook("link:prefetch",async n=>{const{hostname:r}=new URL(n,window.location.href);r===window.location.hostname&&await mi(n)}),((t=navigator.connection)==null?void 0:t.effectiveType)!=="slow-2g"&&setTimeout(Lr,1e3)})}}),yg=et(()=>{const e=Fe();ao(()=>{e.beforeResolve(async()=>{await new Promise(t=>{setTimeout(t,100),requestAnimationFrame(()=>{setTimeout(t,0)})})})})}),_g=et(e=>{let t;async function n(){const r=await Lr();t&&clearTimeout(t),t=setTimeout(n,li);try{const s=await $fetch(ro("builds/latest.json")+`?${Date.now()}`);s.id!==r.id&&e.hooks.callHook("app:manifest:update",s)}catch{}}ao(()=>{t=setTimeout(n,li)})});function bg(e={}){const t=e.path||window.location.pathname;let n={};try{n=pr(sessionStorage.getItem("nuxt:reload")||"{}")}catch{}if(e.force||(n==null?void 0:n.path)!==t||(n==null?void 0:n.expires)<Date.now()){try{sessionStorage.setItem("nuxt:reload",JSON.stringify({path:t,expires:Date.now()+(e.ttl??1e4)}))}catch{}if(e.persistState)try{sessionStorage.setItem("nuxt:reload:state",JSON.stringify({state:pe().payload.state}))}catch{}window.location.pathname!==t?window.location.href=t:window.location.reload()}}const vg=et({name:"nuxt:chunk-reload",setup(e){const t=Fe(),n=Or(),r=new Set;t.beforeEach(()=>{r.clear()}),e.hook("app:chunkError",({error:o})=>{r.add(o)});function s(o){const l="href"in o&&o.href[0]==="#"?n.app.baseURL+o.href:to(n.app.baseURL,o.fullPath);bg({path:l,persistState:!0})}e.hook("app:manifest:update",()=>{t.beforeResolve(s)}),t.onError((o,i)=>{r.has(o)&&s(i)})}}),wg=et({name:"nuxt:global-components"}),Qn={},Eg=et({name:"nuxt:prefetch",setup(e){const t=Fe();e.hooks.hook("app:mounted",()=>{t.beforeEach(async n=>{var s;const r=(s=n==null?void 0:n.meta)==null?void 0:s.layout;r&&typeof Qn[r]=="function"&&await Qn[r]()})}),e.hooks.hook("link:prefetch",n=>{if($t(n))return;const r=t.resolve(n);if(!r)return;const s=r.meta.layout;let o=co(r.meta.middleware);o=o.filter(i=>typeof i=="string");for(const i of o)typeof vn[i]=="function"&&vn[i]();s&&typeof Qn[s]=="function"&&Qn[s]()})}}),Rg=et(()=>{{const e=()=>{window._hmt=window._hmt||[];const n=document.createElement("script");n.src="https://hm.baidu.com/hm.js?acc9c394175b8b87b1ee93f9482c2bce",n.async=!0;const r=document.getElementsByTagName("script")[0];r&&r.parentNode&&r.parentNode.insertBefore(n,r)};document.readyState==="loading"?document.addEventListener("DOMContentLoaded",e):e(),Fe().afterEach(n=>{window._hmt&&window._hmt.push(["_trackPageview",n.fullPath])})}}),Tg=[Qh,Yh,gg,mg,yg,_g,vg,wg,Eg,Rg],Cg=Mn({props:{vnode:{type:Object,required:!0},route:{type:Object,required:!0},vnodeRef:Object,renderKey:String,trackRootNodes:Boolean},setup(e){const t=e.renderKey,n=e.route,r={};for(const s in e.route)Object.defineProperty(r,s,{get:()=>t===e.renderKey?e.route[s]:n[s]});return Xt(Ir,rt(r)),()=>Ye(e.vnode,{ref:e.vnodeRef})}}),Sg=Mn({name:"NuxtPage",inheritAttrs:!1,props:{name:{type:String},transition:{type:[Boolean,Object],default:void 0},keepalive:{type:[Boolean,Object],default:void 0},route:{type:Object},pageKey:{type:[Function,String],default:null}},setup(e,{attrs:t,slots:n,expose:r}){const s=pe(),o=_t(),i=He(Ir,null);let l;r({pageRef:o});const c=He(Zd,null);let f;const u=s.deferHydration();if(s.isHydrating){const a=s.hooks.hookOnce("app:error",u);Fe().beforeEach(a)}return e.pageKey&&Yt(()=>e.pageKey,(a,d)=>{a!==d&&s.callHook("page:loading:start")}),()=>Ye(zc,{name:e.name,route:e.route,...t},{default:a=>{const d=Pg(i,a.route,a.Component),g=i&&i.matched.length===a.route.matched.length;if(!a.Component){if(f&&!g)return f;u();return}if(f&&c&&!c.isCurrent(a.route))return f;if(d&&i&&(!c||c!=null&&c.isCurrent(i)))return g?f:null;const b=Os(a,e.pageKey);!s.isHydrating&&!Ag(i,a.route,a.Component)&&l===b&&s.callHook("page:loading:end"),l=b;const E=!!(e.transition??a.route.meta.pageTransition??vs),k=E&&xg([e.transition,a.route.meta.pageTransition,vs,{onAfterLeave:()=>{s.callHook("page:transition:finish",a.Component)}}].filter(Boolean)),H=e.keepalive??a.route.meta.keepalive??Nd;return f=lg(Ys,E&&k,rg(H,Ye(Jl,{suspensible:!0,onPending:()=>s.callHook("page:start",a.Component),onResolve:()=>{cn(()=>s.callHook("page:finish",a.Component).then(()=>s.callHook("page:loading:end")).finally(u))}},{default:()=>{const y=Ye(Cg,{key:b||void 0,vnode:n.default?Ye(Ie,void 0,n.default(a)):a.Component,route:a.route,renderKey:b||void 0,trackRootNodes:E,vnodeRef:o});return H&&(y.type.name=a.Component.type.name||a.Component.type.__name||"RouteProvider"),y}}))).default(),f}})}});function xg(e){const t=e.map(n=>({...n,onAfterLeave:n.onAfterLeave?co(n.onAfterLeave):void 0}));return Rc(...t)}function Pg(e,t,n){if(!e)return!1;const r=t.matched.findIndex(s=>{var o;return((o=s.components)==null?void 0:o.default)===(n==null?void 0:n.type)});return!r||r===-1?!1:t.matched.slice(0,r).some((s,o)=>{var i,l,c;return((i=s.components)==null?void 0:i.default)!==((c=(l=e.matched[o])==null?void 0:l.components)==null?void 0:c.default)})||n&&Os({route:t,Component:n})!==Os({route:e,Component:n})}function Ag(e,t,n){return e?t.matched.findIndex(s=>{var o;return((o=s.components)==null?void 0:o.default)===(n==null?void 0:n.type)})<t.matched.length-1:!1}const kg=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Og={};function Ig(e,t){const n=Sg;return Qe(),Zl("div",null,[he(n)])}const Mg=kg(Og,[["render",Ig]]),Hg={__name:"nuxt-error-page",props:{error:Object},setup(e){const n=e.error;n.stack&&n.stack.split(`
`).splice(1).map(a=>({text:a.replace("webpack:/","").replace(".vue",".js").trim(),internal:a.includes("node_modules")&&!a.includes(".cache")||a.includes("internal")||a.includes("new Promise")})).map(a=>`<span class="stack${a.internal?" internal":""}">${a.text}</span>`).join(`
`);const r=Number(n.statusCode||500),s=r===404,o=n.statusMessage??(s?"Page Not Found":"Internal Server Error"),i=n.message||n.toString(),l=void 0,u=s?Co(()=>Oe(()=>import("./q5UI1IJe.js"),__vite__mapDeps([23,1,7,24]),import.meta.url).then(a=>a.default||a)):Co(()=>Oe(()=>import("./DcJ3jc3j.js"),__vite__mapDeps([25,7,26]),import.meta.url).then(a=>a.default||a));return(a,d)=>(Qe(),At(ue(u),ia(nc({statusCode:ue(r),statusMessage:ue(o),description:ue(i),stack:ue(l)})),null,16))}},Lg={key:0},Fi={__name:"nuxt-root",setup(e){const t=()=>null,n=pe(),r=n.deferHydration();if(n.isHydrating){const c=n.hooks.hookOnce("app:error",r);Fe().beforeEach(c)}const s=!1;Xt(Ir,Cc()),n.hooks.callHookWith(c=>c.map(f=>f()),"vue:setup");const o=Mr(),i=!1;Sl((c,f,u)=>{if(n.hooks.callHook("vue:error",c,f,u).catch(a=>console.error("[nuxt] Error in `vue:error` hook",a)),rh(c)&&(c.fatal||c.unhandled))return n.runWithContext(()=>Vt(c)),!1});const l=!1;return(c,f)=>(Qe(),At(Jl,{onResolve:ue(r)},{default:_l(()=>[ue(i)?(Qe(),Zl("div",Lg)):ue(o)?(Qe(),At(ue(Hg),{key:1,error:ue(o)},null,8,["error"])):ue(l)?(Qe(),At(ue(t),{key:2,context:ue(l)},null,8,["context"])):ue(s)?(Qe(),At(ou(ue(s)),{key:3})):(Qe(),At(ue(Mg),{key:4}))]),_:1},8,["onResolve"]))}};let Bi;{let e;Bi=async function(){var i,l;if(e)return e;const r=!!((i=window.__NUXT__)!=null&&i.serverRendered||((l=document.getElementById("__NUXT_DATA__"))==null?void 0:l.dataset.ssr)==="true")?kf(Fi):Af(Fi),s=Bd({vueApp:r});async function o(c){await s.callHook("app:error",c),s.payload.error=s.payload.error||Hr(c)}r.config.errorHandler=o;try{await Vd(s,Tg)}catch(c){o(c)}try{await s.hooks.callHook("app:created",r),await s.hooks.callHook("app:beforeMount",r),r.mount(jd),await s.hooks.callHook("app:mounted",r),await cn()}catch(c){o(c)}return r.config.errorHandler===o&&(r.config.errorHandler=void 0),r},e=Bi().catch(t=>{throw console.error("Error while mounting app:",t),t})}export{eo as $,Yt as A,Ws as B,Ya as C,Xa as D,Xs as E,Ie as F,Cs as G,Ke as H,Ye as I,Jg as J,Cl as K,Fe as L,Mn as M,pe as N,ao as O,ji as P,th as Q,Qg as R,Fg as S,Ys as T,Gf as U,Gg as V,$t as W,to as X,Or as Y,ys as Z,kg as _,tc as a,he as b,Zl as c,rc as d,$g as e,mc as f,qg as g,jg as h,Wg as i,Dg as j,Bg as k,Vg as l,Ks as m,Rr as n,Qe as o,Ng as p,At as q,_t as r,ou as s,fa as t,Co as u,Kg as v,_l as w,Oe as x,zg as y,Ug as z};
