// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  css: ['~/assets/css/main.css'],
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },

  // 允许内联脚本执行（用于百度统计等）
  ssr: true,

  // SEO 和 Meta 标签配置
  app: {
    head: {
      title: 'brainysoft.cn - 专业Web控件解决方案',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content: '专业的Web控件解决方案提供商，提供ScanOnWeb扫描仪控件、ImageCapOnWeb摄像头控件、GaoPaiYi高拍仪控件。支持Windows和Linux平台，兼容信创国产化环境。'
        },
        {
          name: 'keywords',
          content: 'Web控件,扫描仪控件,摄像头控件,高拍仪控件,ScanOnWeb,ImageCapOnWeb,GaoPaiYi,信创,国产化,JavaScript,Vue,React,Angular'
        },
        { name: 'author', content: 'brainysoft.cn' },
        { name: 'robots', content: 'index, follow' },
        { name: 'googlebot', content: 'index, follow' },
        { name: 'bingbot', content: 'index, follow' },
        { name: 'baidu-site-verification', content: '' }, // 百度站长验证码
        { name: 'google-site-verification', content: '' }, // Google Search Console验证码

        // Open Graph / Facebook
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: 'https://brainysoft.cn/' },
        { property: 'og:title', content: 'brainysoft.cn - 专业Web控件解决方案' },
        { property: 'og:description', content: '专业的Web控件解决方案提供商，提供扫描仪、摄像头、高拍仪控件，支持多平台，兼容信创国产化环境。' },
        { property: 'og:image', content: 'https://brainysoft.cn/images/og-image.jpg' },

        // Twitter
        { property: 'twitter:card', content: 'summary_large_image' },
        { property: 'twitter:url', content: 'https://brainysoft.cn/' },
        { property: 'twitter:title', content: 'brainysoft.cn - 专业Web控件解决方案' },
        { property: 'twitter:description', content: '专业的Web控件解决方案提供商，提供扫描仪、摄像头、高拍仪控件，支持多平台，兼容信创国产化环境。' },
        { property: 'twitter:image', content: 'https://brainysoft.cn/images/og-image.jpg' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://brainysoft.cn/' },
        { rel: 'sitemap', type: 'application/xml', href: '/sitemap.xml' },
      ],
      script: [
        // 其他统计脚本可以在这里添加
        // 百度统计通过插件加载 (plugins/baidu-analytics.client.ts)
        // {
        //   src: 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID',
        //   async: true
        // }
      ]
    }
  },

  // 静态生成配置
  nitro: {
    prerender: {
      routes: []
    }
  },

  // 可选：配置基础路径（如果部署在子目录）
  // app: {
  //   baseURL: '/your-subdirectory/'
  // }
})
