const interopDefault = r => r.default || r || []
export default {
  "node_modules/nuxt/dist/app/entry.js": () => import('./_nuxt/entry-styles.uA65jsqs.mjs').then(interopDefault),
  "pages/contact.vue": () => import('./_nuxt/contact-styles.-YwGc5TO.mjs').then(interopDefault),
  "pages/demo.vue": () => import('./_nuxt/demo-styles.Bb5DtSSj.mjs').then(interopDefault),
  "pages/docs/scanonweb-api.vue": () => import('./_nuxt/scanonweb-api-styles.BjiZYctW.mjs').then(interopDefault),
  "pages/docs/scanonweb-faq.vue": () => import('./_nuxt/scanonweb-faq-styles.D_3CRHjD.mjs').then(interopDefault),
  "pages/docs/scanonweb-features.vue": () => import('./_nuxt/scanonweb-features-styles.B2E9CwGR.mjs').then(interopDefault),
  "pages/docs/scanonweb-getting-started.vue": () => import('./_nuxt/scanonweb-getting-started-styles.Dtls-L8f.mjs').then(interopDefault),
  "pages/documents.vue": () => import('./_nuxt/documents-styles.DKcP6Hie.mjs').then(interopDefault),
  "pages/products.vue": () => import('./_nuxt/products-styles.KKqtDzVX.mjs').then(interopDefault),
  "pages/purchase.vue": () => import('./_nuxt/purchase-styles.HWQCmgdO.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue": () => import('./_nuxt/error-404-styles.WpyeoFtc.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue": () => import('./_nuxt/error-500-styles.BFuVBFDJ.mjs').then(interopDefault),
  "pages/products.vue?vue&type=style&index=0&scoped=9e2630bc&lang.css": () => import('./_nuxt/products-styles.i9Ed9xDF.mjs').then(interopDefault),
  "pages/purchase.vue?vue&type=style&index=0&scoped=873a08a0&lang.css": () => import('./_nuxt/purchase-styles.BS-dmHH8.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue?vue&type=style&index=0&scoped=922baad2&lang.css": () => import('./_nuxt/error-404-styles.CibK9Qn2.mjs').then(interopDefault),
  "pages/demo.vue?vue&type=style&index=0&scoped=ad3dd77b&lang.css": () => import('./_nuxt/demo-styles.b7mgx-pK.mjs').then(interopDefault),
  "pages/contact.vue?vue&type=style&index=0&scoped=37ff425d&lang.css": () => import('./_nuxt/contact-styles.BoEUfnRo.mjs').then(interopDefault),
  "pages/docs/scanonweb-features.vue?vue&type=style&index=0&scoped=b2103ab5&lang.css": () => import('./_nuxt/scanonweb-features-styles.DgtRxK0j.mjs').then(interopDefault),
  "pages/docs/scanonweb-api.vue?vue&type=style&index=0&scoped=d2fddfc7&lang.css": () => import('./_nuxt/scanonweb-api-styles.Cb1NkSE0.mjs').then(interopDefault),
  "pages/docs/scanonweb-faq.vue?vue&type=style&index=0&scoped=13cbb726&lang.css": () => import('./_nuxt/scanonweb-faq-styles.C1-arYzn.mjs').then(interopDefault),
  "pages/docs/scanonweb-getting-started.vue?vue&type=style&index=0&scoped=ec61ad0e&lang.css": () => import('./_nuxt/scanonweb-getting-started-styles.BQWHRnoY.mjs').then(interopDefault),
  "pages/documents.vue?vue&type=style&index=0&scoped=f24d6ae8&lang.css": () => import('./_nuxt/documents-styles.BVXJUusL.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue?vue&type=style&index=0&scoped=1e3620c9&lang.css": () => import('./_nuxt/error-500-styles.JhxDD0mW.mjs').then(interopDefault),
  "components/demos/ImageCapDemo.vue": () => import('./_nuxt/ImageCapDemo-styles.Can7A18f.mjs').then(interopDefault),
  "components/demos/SimpleDemo.vue": () => import('./_nuxt/SimpleDemo-styles.BqumqV5A.mjs').then(interopDefault),
  "components/demos/ScannerDemo.vue": () => import('./_nuxt/ScannerDemo-styles.Do-J0HRy.mjs').then(interopDefault),
  "components/demos/GaoPaiYiDemo.vue": () => import('./_nuxt/GaoPaiYiDemo-styles.D3QsfzNJ.mjs').then(interopDefault),
  "components/demos/ImageCapDemo.vue?vue&type=style&index=0&scoped=db70dee0&lang.css": () => import('./_nuxt/ImageCapDemo-styles.CT42aSTO.mjs').then(interopDefault),
  "components/demos/GaoPaiYiDemo.vue?vue&type=style&index=0&scoped=9e9ef8b0&lang.css": () => import('./_nuxt/GaoPaiYiDemo-styles.CGQ3oMIZ.mjs').then(interopDefault),
  "components/demos/SimpleDemo.vue?vue&type=style&index=0&scoped=b6686b1b&lang.css": () => import('./_nuxt/SimpleDemo-styles.D35KZ3C7.mjs').then(interopDefault),
  "components/demos/ScannerDemo.vue?vue&type=style&index=0&scoped=1b4af074&lang.css": () => import('./_nuxt/ScannerDemo-styles.DeH4UFui.mjs').then(interopDefault)
}