import { _ as __nuxt_component_0 } from "./nuxt-link-2X8I7ISh.js";
import { mergeProps, useSSRContext, defineAsyncComponent, ref, withCtx, createTextVNode, createVNode, resolveDynamicComponent } from "vue";
import { ssrRenderAttrs, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderComponent, ssrRenderVNode } from "vue/server-renderer";
import { _ as _sfc_main$2, a as _sfc_main$3 } from "./Footer-C3PwX65Z.js";
import { _ as _export_sfc } from "../server.mjs";
import "ufo";
import "#internal/nuxt/paths";
import "ofetch";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "@unhead/shared";
import "vue-router";
import "radix3";
import "defu";
import "devalue";
const _sfc_main$1 = {
  __name: "DemoSidebar",
  __ssrInlineRender: true,
  props: {
    demos: Array,
    selectedDemoId: Number
  },
  emits: ["select"],
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-white shadow-lg rounded-lg overflow-hidden" }, _attrs))}><div class="bg-blue-600 text-white p-4"><h2 class="text-2xl font-bold">演示列表</h2></div><ul><!--[-->`);
      ssrRenderList(__props.demos, (demo2) => {
        _push(`<li class="border-b last:border-b-0"><button class="${ssrRenderClass([{
          "bg-blue-100": demo2.id === __props.selectedDemoId,
          "hover:bg-gray-100": demo2.id !== __props.selectedDemoId
        }, "w-full text-left p-4 flex items-center transition-colors"])}"><div class="${ssrRenderClass([{
          "bg-gradient-to-r from-blue-500 to-indigo-600": demo2.icon === "document-scanner",
          "bg-gradient-to-r from-green-500 to-teal-600": demo2.icon === "document",
          "bg-gradient-to-r from-purple-500 to-pink-600": demo2.icon === "camera",
          "bg-gradient-to-r from-orange-500 to-red-600": demo2.icon === "photo"
        }, "w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold"])}">`);
        if (demo2.icon === "document-scanner") {
          _push(`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>`);
        } else if (demo2.icon === "document") {
          _push(`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>`);
        } else if (demo2.icon === "camera") {
          _push(`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>`);
        } else if (demo2.icon === "photo") {
          _push(`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div><p class="font-semibold text-gray-800">${ssrInterpolate(demo2.name)}</p><p class="text-xs text-gray-500 mt-1">${ssrInterpolate(demo2.description)}</p></div></button></li>`);
      });
      _push(`<!--]--></ul></div>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/DemoSidebar.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "demo",
  __ssrInlineRender: true,
  setup(__props) {
    const ScannerDemo = defineAsyncComponent(
      () => import("./ScannerDemo-CJ7uu1tH.js")
    );
    const SimpleDemo = defineAsyncComponent(
      () => import("./SimpleDemo-C6RzC_wr.js")
    );
    const ImageCapDemo = defineAsyncComponent(
      () => import("./ImageCapDemo-DjDT3NWX.js")
    );
    const GaoPaiYiDemo = defineAsyncComponent(
      () => import("./GaoPaiYiDemo-DoyT-eMt.js")
    );
    const demos = ref([
      {
        id: 1,
        name: "ScanOnWeb 扫描演示",
        description: "基础扫描功能演示，包括设备选择、参数设置、图像获取等",
        component: ScannerDemo,
        icon: "document-scanner"
      },
      {
        id: 2,
        name: "简易扫描工具",
        description: "简化版扫描工具，适合快速上手使用",
        component: SimpleDemo,
        icon: "document"
      },
      {
        id: 3,
        name: "ImageCapOnWeb 演示",
        description: "摄像头图像采集功能演示",
        component: ImageCapDemo,
        icon: "camera"
      },
      {
        id: 4,
        name: "GaoPaiYi 高拍仪演示",
        description: "高拍仪设备图像采集功能演示",
        component: GaoPaiYiDemo,
        icon: "photo"
      }
    ]);
    const selectedDemo = ref(null);
    const selectDemo = (demo2) => {
      selectedDemo.value = demo2;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c;
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-ad3dd77b>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`<main data-v-ad3dd77b><div class="bg-white border-b border-gray-200" data-v-ad3dd77b><div class="container mx-auto px-4 py-4" data-v-ad3dd77b><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-ad3dd77b>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`首页`);
          } else {
            return [
              createTextVNode("首页")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-ad3dd77b><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-ad3dd77b></path></svg><span class="text-gray-900 font-medium" data-v-ad3dd77b>在线演示</span></nav></div></div><div class="bg-white py-8" data-v-ad3dd77b><div class="container mx-auto px-4" data-v-ad3dd77b><div class="max-w-4xl" data-v-ad3dd77b><h1 class="heading-primary mb-4" data-v-ad3dd77b>产品演示中心</h1><p class="text-lg text-gray-600 mb-6" data-v-ad3dd77b> 体验我们专业Web控件的强大功能，通过在线演示了解产品特性和使用方法 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-ad3dd77b><div class="flex items-center" data-v-ad3dd77b><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ad3dd77b><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ad3dd77b></path></svg><span data-v-ad3dd77b>实时演示</span></div><div class="flex items-center" data-v-ad3dd77b><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ad3dd77b><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ad3dd77b></path></svg><span data-v-ad3dd77b>功能完整</span></div><div class="flex items-center" data-v-ad3dd77b><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ad3dd77b><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ad3dd77b></path></svg><span data-v-ad3dd77b>易于上手</span></div></div></div></div></div><div class="container mx-auto py-8 px-4" data-v-ad3dd77b><div class="flex flex-col lg:flex-row gap-8" data-v-ad3dd77b><div class="lg:w-1/4" data-v-ad3dd77b>`);
      _push(ssrRenderComponent(_sfc_main$1, {
        demos: demos.value,
        selectedDemoId: (_a = selectedDemo.value) == null ? void 0 : _a.id,
        onSelect: selectDemo
      }, null, _parent));
      _push(`</div><div class="lg:w-3/4" data-v-ad3dd77b>`);
      ssrRenderVNode(_push, createVNode(resolveDynamicComponent((_b = selectedDemo.value) == null ? void 0 : _b.component), {
        key: (_c = selectedDemo.value) == null ? void 0 : _c.id
      }, null), _parent);
      _push(`</div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$3, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/demo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const demo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-ad3dd77b"]]);
export {
  demo as default
};
//# sourceMappingURL=demo-Bb430eIF.js.map
