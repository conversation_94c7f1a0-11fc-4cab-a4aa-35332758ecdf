import { _ as __nuxt_component_0 } from "./nuxt-link-2X8I7ISh.js";
import { mergeProps, withCtx, createTextVNode, openBlock, createBlock, createVNode, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderAttr, ssrRenderComponent } from "vue/server-renderer";
import { publicAssetsURL } from "#internal/nuxt/paths";
const _imports_0 = publicAssetsURL("/images/logo.jpg");
const _sfc_main$1 = {
  __name: "Header",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<header${ssrRenderAttrs(mergeProps({ class: "bg-white border-b border-gray-200 shadow-sm" }, _attrs))}><div class="container mx-auto px-4 py-4"><div class="flex justify-between items-center"><div class="flex items-center"><img${ssrRenderAttr("src", _imports_0)} alt="brainysoft Logo" class="h-10 mr-3"><div><h1 class="text-2xl font-bold text-gray-900">brainysoft.cn</h1><p class="text-sm text-gray-500">专业Web控件解决方案</p></div></div><nav class="flex items-center space-x-8"><ul class="flex space-x-6"><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`首页`);
          } else {
            return [
              createTextVNode("首页")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/products",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`产品介绍`);
          } else {
            return [
              createTextVNode("产品介绍")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/demo",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`在线演示`);
          } else {
            return [
              createTextVNode("在线演示")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`文档资料`);
          } else {
            return [
              createTextVNode("文档资料")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/purchase",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`注册购买`);
          } else {
            return [
              createTextVNode("注册购买")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/contact",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`联系我们`);
          } else {
            return [
              createTextVNode("联系我们")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li></ul>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/download",
        class: "btn-primary flex items-center space-x-2"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"${_scopeId}></path></svg><span${_scopeId}>免费下载</span>`);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                })
              ])),
              createVNode("span", null, "免费下载")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</nav></div></div></header>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Header.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "Footer",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<footer${ssrRenderAttrs(mergeProps({ class: "bg-gray-800 text-white py-8" }, _attrs))}><div class="container mx-auto"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div><h3 class="text-xl font-bold mb-4">关于我们</h3><p>我们是一家致力于提供图像扫描处理软件解决方案的公司,为简化图像扫描编程应用而努力。</p></div><div><h3 class="text-xl font-bold mb-4">联系方式</h3><p>邮箱: <EMAIL></p><p>电话: (+86) 155-1196-5595</p><p>微信: 20155031</p></div><div><h3 class="text-xl font-bold mb-4">快速链接</h3><ul><li><a href="http://www.regsky.com" target="_blank" class="hover:text-gray-300">天空云科技</a></li><li><a href="#" class="hover:text-gray-300">联系我们</a></li></ul></div><div><h3 class="text-xl font-bold mb-4">关注我们</h3><div class="flex space-x-4"><a href="#" class="hover:text-gray-300">Facebook</a><a href="#" class="hover:text-gray-300">Twitter</a><a href="#" class="hover:text-gray-300">LinkedIn</a></div></div></div><div class="mt-8 text-center"><p>© 2024 brainysoft. 保留所有权利。</p></div></div></footer>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Footer.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main$1 as _,
  _sfc_main as a
};
//# sourceMappingURL=Footer-C3PwX65Z.js.map
