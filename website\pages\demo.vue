<template>
  <div class="bg-gray-100 min-h-screen">
    <Header />
    <main class="container mx-auto py-8 px-4">
      <h1 class="text-3xl font-bold text-center mb-8">产品演示中心</h1>

      <div class="flex flex-col lg:flex-row gap-8">
        <div class="lg:w-1/4">
          <DemoSidebar
            :demos="demos"
            :selectedDemoId="selectedDemo?.id"
            @select="selectDemo"
          />
        </div>
        <div class="lg:w-3/4">
          <transition name="fade" mode="out-in">
            <component :is="selectedDemo?.component" :key="selectedDemo?.id" />
          </transition>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, defineAsyncComponent } from "vue";
import Header from "~/components/Header.vue";
import Footer from "~/components/Footer.vue";
import DemoSidebar from "~/components/DemoSidebar.vue";

// 使用异步组件按需加载各个演示组件
const ScannerDemo = defineAsyncComponent(() =>
  import("~/components/demos/ScannerDemo.vue")
);
const SimpleDemo = defineAsyncComponent(() =>
  import("~/components/demos/SimpleDemo.vue")
);
const ImageCapDemo = defineAsyncComponent(() =>
  import("~/components/demos/ImageCapDemo.vue")
);
const GaoPaiYiDemo = defineAsyncComponent(() =>
  import("~/components/demos/GaoPaiYiDemo.vue")
);

const demos = ref([
  {
    id: 1,
    name: "ScanOnWeb 扫描演示",
    description: "基础扫描功能演示，包括设备选择、参数设置、图像获取等",
    component: ScannerDemo,
    icon: "document-scanner",
  },
  {
    id: 2,
    name: "简易扫描工具",
    description: "简化版扫描工具，适合快速上手使用",
    component: SimpleDemo,
    icon: "document",
  },
  {
    id: 3,
    name: "ImageCapOnWeb 演示",
    description: "摄像头图像采集功能演示",
    component: ImageCapDemo,
    icon: "camera",
  },
  {
    id: 4,
    name: "GaoPaiYi 高拍仪演示",
    description: "高拍仪设备图像采集功能演示",
    component: GaoPaiYiDemo,
    icon: "photo",
  },
]);

const selectedDemo = ref(null);

const selectDemo = (demo) => {
  selectedDemo.value = demo;
};

onMounted(() => {
  // 默认选中第一个演示
  if (demos.value.length > 0) {
    selectedDemo.value = demos.value[0];
  }
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
