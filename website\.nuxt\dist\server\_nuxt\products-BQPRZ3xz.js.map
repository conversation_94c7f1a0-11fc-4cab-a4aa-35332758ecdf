{"version": 3, "file": "products-BQPRZ3xz.js", "sources": ["../../../../components/ProductSidebar.vue", "../../../../pages/products.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bg-white shadow-lg rounded-lg overflow-hidden\">\r\n    <div class=\"bg-blue-600 text-white p-4\">\r\n      <h2 class=\"text-2xl font-bold\">产品列表</h2>\r\n    </div>\r\n    <ul>\r\n      <li\r\n        v-for=\"product in products\"\r\n        :key=\"product.id\"\r\n        class=\"border-b last:border-b-0\"\r\n      >\r\n        <button\r\n          @click=\"$emit('select', product)\"\r\n          class=\"w-full text-left p-4 flex items-center transition-colors\"\r\n          :class=\"{\r\n            'bg-blue-100': product.id === selectedProductId,\r\n            'hover:bg-gray-100': product.id !== selectedProductId,\r\n          }\"\r\n        >\r\n          <div\r\n            class=\"w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold text-xl\"\r\n            :class=\"{\r\n              'bg-gradient-to-r from-blue-500 to-indigo-600': product.id === 1,\r\n              'bg-gradient-to-r from-purple-500 to-pink-600': product.id === 2,\r\n              'bg-gradient-to-r from-green-500 to-teal-600': product.id === 3,\r\n            }\"\r\n          >\r\n            {{ product.name.charAt(0) }}\r\n          </div>\r\n          <div>\r\n            <p class=\"font-semibold text-gray-800\">{{ product.name }}</p>\r\n            <p class=\"text-xs text-gray-500 mt-1\">\r\n              {{ getShortDescription(product.description) }}\r\n            </p>\r\n          </div>\r\n        </button>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\ndefineProps({\r\n  products: Array,\r\n  selectedProductId: Number,\r\n});\r\n\r\ndefineEmits([\"select\"]);\r\n\r\nconst getShortDescription = (description) => {\r\n  return description.length > 50\r\n    ? description.substring(0, 50) + \"...\"\r\n    : description;\r\n};\r\n</script>\r\n", "<template>\r\n  <div class=\"bg-gray-100 min-h-screen\">\r\n    <Header />\r\n    <main class=\"container mx-auto py-12 px-4\">\r\n      <div class=\"flex flex-col lg:flex-row gap-8\">\r\n        <div class=\"lg:w-1/4\">\r\n          <ProductSidebar\r\n            :products=\"products\"\r\n            :selectedProductId=\"selectedProduct?.id\"\r\n            @select=\"selectProduct\"\r\n          />\r\n        </div>\r\n        <div class=\"lg:w-3/4\">\r\n          <transition name=\"fade\" mode=\"out-in\">\r\n            <ProductDetails\r\n              :key=\"selectedProduct?.id\"\r\n              :product=\"selectedProduct\"\r\n            />\r\n          </transition>\r\n        </div>\r\n      </div>\r\n    </main>\r\n    <Footer />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from \"vue\";\r\nimport Header from \"~/components/Header.vue\";\r\nimport Footer from \"~/components/Footer.vue\";\r\nimport ProductSidebar from \"~/components/ProductSidebar.vue\";\r\nimport ProductDetails from \"~/components/ProductDetails.vue\";\r\n\r\nconst products = ref([\r\n  {\r\n    id: 1,\r\n    name: \"ScanOnWeb\",\r\n    description:\r\n      \"ScanOnWeb控件(软件著作权登记号 2013SR145420，证书号0651182)用于处理图像扫描编程，适合用于web环境下的扫描仪编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容目前主流的数款扫描设备，对于个别非按标准协议支持的扫描设备亦提供了集成支持，目前控件经过多年的发展已经很成熟稳定，被广泛的应用于办公OA、电子政务、纸质文档电子化等应用场景，客户单位遍布税务、公安、建筑、银行等多个行业，是目前国内唯一成熟稳定的扫描控件产品。\",\r\n    image: \"/images/slide1.png\",\r\n    features: [\r\n      \"扫描设备选择\",\r\n      \"自动进纸器连续多页扫描\",\r\n      \"双面扫描模式\",\r\n      \"自动纠偏模式\",\r\n      \"多种图像扫描模式\",\r\n      \"多种分辨率设置\",\r\n    ],\r\n    specs: {\r\n      浏览器兼容性:\r\n        \"所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等\",\r\n      图像扫描协议:\r\n        \"支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备，支持WIA协议的所有图像采集设备\",\r\n      图像编辑特性: \"支持旋转、裁剪、填白、马赛克等多种处理\",\r\n      图像上传能力: \"支持多种格式上传到指定URL\",\r\n    },\r\n    useCases: [\r\n      \"办公OA\",\r\n      \"电子政务\",\r\n      \"纸质文档电子化\",\r\n      \"税务系统\",\r\n      \"公安系统\",\r\n      \"银行业务\",\r\n    ],\r\n    pricing: \"请下载报价单了解详情\",\r\n    videoUrl: \"https://example.com/scanonweb-demo.mp4\",\r\n    pdfUrl: \"/ScanOnWeb-quotation.pdf\",\r\n    tableData: [\r\n      {\r\n        category: \"浏览器兼容性\",\r\n        value:\r\n          \"所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等\",\r\n      },\r\n      {\r\n        category: \"图像扫描协议\",\r\n        value:\r\n          \"1.支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备2.支持WIA协议的所有图像采集设备\",\r\n      },\r\n      {\r\n        category: \"图像扫描参数控制\",\r\n        value:\r\n          \"1. 扫描设备选择 2. 是否显示扫描仪驱动程序内置设置对话框 3. 是否使用自动进纸器进行连续多页扫描 4. 是否使用自动装填纸张模式 5. 是否使用双面扫描模式 6. 是否使用自动纠偏模式 7. 是否使用自动边框检测模式 8. 图像扫描模式：黑白模式、灰度模式、彩色模式 9. dpi分辨率设置 10. 扫描结果传输模式：内存、文件、原生\",\r\n      },\r\n      {\r\n        category: \"图像编辑特性\",\r\n        value:\r\n          \"1. 支持图像向左、向右旋转 90 度 2. 支持图像自定义角度旋转 3. 支持魔术棒选择模式图像选择 4. 支持矩形图像选择 5. 支持选中区域填白处理 6. 支持选中区域反选填白处理 7. 支持选中区域马赛克处理 8. 支持裁剪选区以外所有图像处理 9. 支持选中区域归红、归绿处理 10. 支持去除图像黑边处理 11. 支持去除图像底色处理 12. UNDO 撤销操作 13. 客户端单页本地图像保存 14. 客户端单页本地图像打印及打印预览 15. 批量图像删除 16. 多页扫描结果排序（直接拖拽顺序） 17. 扫描图像删除单页处理 18. 客户端多页图像保存 19. 客户端多页图像打印机打印预览 20. 鼠标滚轮缩放图像\",\r\n      },\r\n      {\r\n        category: \"图像上传能力\",\r\n        value:\r\n          \"1. 图像转jpg base64编码，供前端显示或上传 2. 图像按照 tiff 格式上传到指定 url 3. 图像按照 pdf 格式上传到指定 url 4. 图像按照多页 jpg 方式上传到指定 url 5. 单独将某一页图像以 jpg 方式上传到指定 url 6. 上传到指定的 sftp 地址（需定制）\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"ImageCapOnWeb\",\r\n    description:\r\n      \"ImageCapOnWeb控件用于处理摄像头图像采集编程，适合用于web环境下的图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的大部分摄像头数码设备。目前已经在多所学校、银行、政府机构中进行集成应用。\",\r\n    image: \"/images/slide2.png\",\r\n    features: [\r\n      \"摄像头图像采集\",\r\n      \"Web环境下的图像采集编程\",\r\n      \"兼容Windows平台下的大部分摄像头设备\",\r\n      \"无缝集成到多种编程技术\",\r\n    ],\r\n    specs: {\r\n      兼容平台: \"Windows平台\",\r\n      支持设备: \"大部分摄像头数码设备\",\r\n      集成技术: \"jsp、php、asp.net等\",\r\n    },\r\n    useCases: [\"学校信息系统\", \"银行业务系统\", \"政府机构应用\"],\r\n    pricing: \"请下载报价单了解详情\",\r\n    videoUrl: \"https://example.com/imagecaponweb-demo.mp4\",\r\n    pdfUrl: \"/ImageCapOnWeb-quotation.pdf\",\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"GaoPaiYi\",\r\n    description:\r\n      \"GaoPaiYi控件用于处理高拍仪图像采集编程，适合用于web环境下的高拍仪图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的以摄像头为核心设备的高拍仪产品。\",\r\n    image: \"/images/slide3.png\",\r\n    features: [\r\n      \"高拍仪图像采集\",\r\n      \"Web环境下的编程应用\",\r\n      \"兼容Windows平台下的高拍仪产品\",\r\n      \"无缝集成到各种编程技术\",\r\n    ],\r\n    specs: {\r\n      兼容平台: \"Windows平台\",\r\n      支持设备: \"以摄像头为核心设备的高拍仪产品\",\r\n      集成技术: \"jsp、php、asp.net等\",\r\n    },\r\n    useCases: [\"文档扫描\", \"证件采集\", \"教育培训\"],\r\n    pricing: \"请下载报价单了解详情\",\r\n    videoUrl: \"https://example.com/gaopaiyi-demo.mp4\",\r\n    pdfUrl: \"/GaoPaiYi-quotation.pdf\",\r\n  },\r\n]);\r\n\r\n// 默认选中第一个产品\r\nconst selectedProduct = ref(products.value[0] || null);\r\n\r\nconst selectProduct = (product) => {\r\n  selectedProduct.value = product;\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "names": ["products"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,UAAM,sBAAsB,CAAC,gBAAgB;AAC3C,aAAO,YAAY,SAAS,KACxB,YAAY,UAAU,GAAG,EAAE,IAAI,QAC/B;AAAA,IACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBA,UAAMA,YAAW,IAAI;AAAA,MACnB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aACE;AAAA,QACF,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL,QACE;AAAA,UACF,QACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,UACT;AAAA,YACE,UAAU;AAAA,YACV,OACE;AAAA,UACH;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,OACE;AAAA,UACH;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,OACE;AAAA,UACH;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,OACE;AAAA,UACH;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,OACE;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aACE;AAAA,QACF,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD,UAAU,CAAC,UAAU,UAAU,QAAQ;AAAA,QACvC,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aACE;AAAA,QACF,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,QACD,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkB,IAAIA,UAAS,MAAM,CAAC,KAAK,IAAI;AAErD,UAAM,gBAAgB,CAAC,YAAY;AACjC,sBAAgB,QAAQ;AAAA,IAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}