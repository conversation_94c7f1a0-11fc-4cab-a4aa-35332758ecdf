# Apache配置文件示例 - 用于静态部署
# 将此文件重命名为 .htaccess 并放置在网站根目录

# 启用重写引擎
RewriteEngine On

# 强制HTTPS（可选，如果您的服务器支持SSL）
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 处理SPA路由 - 将所有请求重定向到index.html
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# 安全设置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header set X-Content-Type-Options nosniff
    
    # XSS保护
    Header set X-XSS-Protection "1; mode=block"
    
    # 引用策略
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 缓存静态资源
<IfModule mod_expires.c>
    ExpiresActive On
    
    # HTML文件 - 短期缓存
    ExpiresByType text/html "access plus 1 hour"
    
    # CSS和JavaScript - 长期缓存
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # 图片文件 - 长期缓存
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # 字体文件
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # 其他文件
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

# Gzip压缩
<IfModule mod_deflate.c>
    # 压缩HTML、CSS、JavaScript、Text、XML和字体
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# 禁止访问敏感文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 禁止访问备份文件
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>
