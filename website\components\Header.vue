<template>
  <header class="bg-white border-b border-gray-200 shadow-sm">
    <div class="container mx-auto px-4 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <img src="/images/logo.jpg" alt="brainysoft Logo" class="h-10 mr-3" />
          <div>
            <h1 class="text-2xl font-bold text-gray-900">brainysoft.cn</h1>
            <p class="text-sm text-gray-500">专业Web控件解决方案</p>
          </div>
        </div>

        <nav class="flex items-center space-x-8">
          <ul class="flex space-x-6">
            <li>
              <NuxtLink
                to="/"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >首页</NuxtLink
              >
            </li>
            <li>
              <NuxtLink
                to="/products"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >产品介绍</NuxtLink
              >
            </li>
            <li>
              <NuxtLink
                to="/demo"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >在线演示</NuxtLink
              >
            </li>
            <li>
              <NuxtLink
                to="/documents"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >文档资料</NuxtLink
              >
            </li>
            <li>
              <NuxtLink
                to="/purchase"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >注册购买</NuxtLink
              >
            </li>
            <li>
              <NuxtLink
                to="/contact"
                class="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200"
                active-class="text-orange-500 border-b-2 border-orange-500"
                exact-active-class="font-semibold"
                >联系我们</NuxtLink
              >
            </li>
          </ul>

          <!-- 商业化下载按钮 -->
          <NuxtLink
            to="/download"
            class="btn-primary flex items-center space-x-2"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span>免费下载</span>
          </NuxtLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
// 如果需要,可以在这里添加组件逻辑
</script>
