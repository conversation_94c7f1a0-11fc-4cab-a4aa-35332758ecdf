import { s as scanonwebFeatures_vue_vue_type_style_index_0_scoped_b2103ab5_lang } from './scanonweb-features-styles-1.mjs-BrCWjbkx.mjs';

const scanonwebFeaturesStyles_B2E9CwGR = [scanonwebFeatures_vue_vue_type_style_index_0_scoped_b2103ab5_lang, scanonwebFeatures_vue_vue_type_style_index_0_scoped_b2103ab5_lang];

export { scanonwebFeaturesStyles_B2E9CwGR as default };
//# sourceMappingURL=scanonweb-features-styles.B2E9CwGR.mjs.map
