import { ref, mergeProps, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from "vue/server-renderer";
import { _ as _export_sfc } from "../server.mjs";
import "ofetch";
import "#internal/nuxt/paths";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "@unhead/shared";
import "vue-router";
import "radix3";
import "defu";
import "ufo";
import "devalue";
const _sfc_main = {
  __name: "ScannerDemo",
  __ssrInlineRender: true,
  setup(__props) {
    const dpiX = ref("300");
    const dpiY = ref("300");
    const colorMode = ref("RGB");
    const showDialog = ref("false");
    const feedEnable = ref("false");
    const autoFeed = ref("false");
    const dupxMode = ref("false");
    const autoDeskew = ref("false");
    const autoBorderDetection = ref("false");
    const uploadUrl = ref("http://localhost:44300/api/ImageUpload/upload");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "scanner-demo" }, _attrs))} data-v-1b4af074><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-1b4af074><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-1b4af074> 图像扫描管理系统 </h2><div class="mb-8" data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>扫描设置</h3><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-1b4af074><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>扫描设备</label><select id="devices" class="w-full p-2 border rounded" data-v-1b4af074></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>分辨率</label><div class="flex items-center" data-v-1b4af074><input type="text" id="dpi_x"${ssrRenderAttr("value", dpiX.value)} class="w-16 p-2 border rounded" data-v-1b4af074><span class="mx-2" data-v-1b4af074>X</span><input type="text" id="dpi_y"${ssrRenderAttr("value", dpiY.value)} class="w-16 p-2 border rounded" data-v-1b4af074></div></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>色彩模式</label><select id="colorMode" class="w-full p-2 border rounded" data-v-1b4af074><option value="RGB" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "RGB") : ssrLooseEqual(colorMode.value, "RGB")) ? " selected" : ""}>彩色</option><option value="GRAY" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "GRAY") : ssrLooseEqual(colorMode.value, "GRAY")) ? " selected" : ""}>灰色</option><option value="BW" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "BW") : ssrLooseEqual(colorMode.value, "BW")) ? " selected" : ""}>黑白</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>显示设备对话框</label><select id="showDialog" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(showDialog.value) ? ssrLooseContain(showDialog.value, "true") : ssrLooseEqual(showDialog.value, "true")) ? " selected" : ""}>显示</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(showDialog.value) ? ssrLooseContain(showDialog.value, "false") : ssrLooseEqual(showDialog.value, "false")) ? " selected" : ""}>不显示</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>自动进纸模式</label><select id="feedEnable" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(feedEnable.value) ? ssrLooseContain(feedEnable.value, "true") : ssrLooseEqual(feedEnable.value, "true")) ? " selected" : ""}>是</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(feedEnable.value) ? ssrLooseContain(feedEnable.value, "false") : ssrLooseEqual(feedEnable.value, "false")) ? " selected" : ""}>否</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>自动装填纸张</label><select id="autoFeed" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoFeed.value) ? ssrLooseContain(autoFeed.value, "true") : ssrLooseEqual(autoFeed.value, "true")) ? " selected" : ""}>是</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoFeed.value) ? ssrLooseContain(autoFeed.value, "false") : ssrLooseEqual(autoFeed.value, "false")) ? " selected" : ""}>否</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>双面模式</label><select id="dupxMode" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(dupxMode.value) ? ssrLooseContain(dupxMode.value, "true") : ssrLooseEqual(dupxMode.value, "true")) ? " selected" : ""}>是</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(dupxMode.value) ? ssrLooseContain(dupxMode.value, "false") : ssrLooseEqual(dupxMode.value, "false")) ? " selected" : ""}>否</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>自动纠偏</label><select id="autoDeskew" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoDeskew.value) ? ssrLooseContain(autoDeskew.value, "true") : ssrLooseEqual(autoDeskew.value, "true")) ? " selected" : ""}>是</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoDeskew.value) ? ssrLooseContain(autoDeskew.value, "false") : ssrLooseEqual(autoDeskew.value, "false")) ? " selected" : ""}>否</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>自动边框检测</label><select id="autoBorderDetection" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoBorderDetection.value) ? ssrLooseContain(autoBorderDetection.value, "true") : ssrLooseEqual(autoBorderDetection.value, "true")) ? " selected" : ""}>是</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoBorderDetection.value) ? ssrLooseContain(autoBorderDetection.value, "false") : ssrLooseEqual(autoBorderDetection.value, "false")) ? " selected" : ""}>否</option></select></div></div><div class="flex flex-wrap gap-2 mt-6" data-v-1b4af074><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-1b4af074> 获取设备列表 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> 开始扫描 </button><button class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors" data-v-1b4af074> 清空扫描结果 </button><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-1b4af074> 获取所有图像 </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> 显示界面 </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> 隐藏界面 </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> 本地另存 </button></div></div><div class="mb-8" data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>上传设置</h3><div class="bg-gray-100 p-4 rounded" data-v-1b4af074><div class="mb-4" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>上传地址</label><input type="text" id="uploadUrl"${ssrRenderAttr("value", uploadUrl.value)} class="w-full p-2 border rounded" placeholder="请输入服务器上传地址" data-v-1b4af074></div><div class="flex flex-wrap gap-2" data-v-1b4af074><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> PDF格式上传 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> DOM图像上传 </button></div></div></div><div data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>扫描结果</h3><div id="imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-1b4af074></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-1b4af074><p class="font-bold" data-v-1b4af074> 注意：本演示需要下载安装托盘扫描服务才可正常工作，下载链接在下面。 </p></div><div class="mt-6 flex justify-center gap-4 border-t pt-4" data-v-1b4af074><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 扫描服务托盘程序下载 </a><a href="https://www.brainysoft.cn/video/scanh5.mp4" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 视频教程 </a><a href="https://www.brainysoft.cn" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> 官方网站 </a></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/ScannerDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ScannerDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-1b4af074"]]);
export {
  ScannerDemo as default
};
//# sourceMappingURL=ScannerDemo-CJ7uu1tH.js.map
