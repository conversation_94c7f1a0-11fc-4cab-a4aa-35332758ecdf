const error500_vue_vue_type_style_index_0_scoped_1e3620c9_lang = ".spotlight[data-v-1e3620c9]{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);filter:blur(20vh)}.fixed[data-v-1e3620c9]{position:fixed}.-bottom-1\\/2[data-v-1e3620c9]{bottom:-50%}.left-0[data-v-1e3620c9]{left:0}.right-0[data-v-1e3620c9]{right:0}.grid[data-v-1e3620c9]{display:grid}.mb-16[data-v-1e3620c9]{margin-bottom:4rem}.mb-8[data-v-1e3620c9]{margin-bottom:2rem}.h-1\\/2[data-v-1e3620c9]{height:50%}.max-w-520px[data-v-1e3620c9]{max-width:520px}.min-h-screen[data-v-1e3620c9]{min-height:100vh}.place-content-center[data-v-1e3620c9]{place-content:center}.overflow-hidden[data-v-1e3620c9]{overflow:hidden}.bg-white[data-v-1e3620c9]{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.px-8[data-v-1e3620c9]{padding-left:2rem;padding-right:2rem}.text-center[data-v-1e3620c9]{text-align:center}.text-8xl[data-v-1e3620c9]{font-size:6rem;line-height:1}.text-xl[data-v-1e3620c9]{font-size:1.25rem;line-height:1.75rem}.text-black[data-v-1e3620c9]{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light[data-v-1e3620c9]{font-weight:300}.font-medium[data-v-1e3620c9]{font-weight:500}.leading-tight[data-v-1e3620c9]{line-height:1.25}.font-sans[data-v-1e3620c9]{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased[data-v-1e3620c9]{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black[data-v-1e3620c9]{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:text-white[data-v-1e3620c9]{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:px-0[data-v-1e3620c9]{padding-left:0;padding-right:0}.sm\\:text-4xl[data-v-1e3620c9]{font-size:2.25rem;line-height:2.5rem}}";
export {
  error500_vue_vue_type_style_index_0_scoped_1e3620c9_lang as default
};
//# sourceMappingURL=error-500-styles-1.mjs-D_pjx06j.js.map
