<template>
  <div class="bg-gray-50 min-h-screen">
    <Header />
    <main>
      <!-- 面包屑导航 -->
      <div class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4 py-4">
          <nav class="flex items-center space-x-2 text-sm text-gray-500">
            <NuxtLink to="/" class="hover:text-orange-500">首页</NuxtLink>
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
            <span class="text-gray-900 font-medium">联系我们</span>
          </nav>
        </div>
      </div>

      <!-- 页面标题区域 -->
      <div class="bg-white py-12">
        <div class="container mx-auto px-4">
          <div class="max-w-3xl">
            <h1 class="heading-primary mb-4">联系我们</h1>
            <p class="text-xl text-gray-600 mb-6">
              我们随时准备为您提供专业的技术支持和服务咨询
            </p>
            <div class="flex flex-wrap gap-6 text-sm text-gray-500">
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>24小时内回复</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>专业技术团队</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>免费咨询服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速联系方式 -->
      <div class="bg-orange-50 py-8">
        <div class="container mx-auto px-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div
                class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <svg
                  class="w-8 h-8 text-orange-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  ></path>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">电话咨询</h3>
              <p class="text-orange-600 font-semibold text-lg">155-1196-5595</p>
              <p class="text-sm text-gray-500">工作日 9:00-18:00</p>
            </div>

            <div class="text-center">
              <div
                class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <svg
                  class="w-8 h-8 text-orange-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  ></path>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">邮件联系</h3>
              <p class="text-orange-600 font-semibold"><EMAIL></p>
              <p class="text-sm text-gray-500">24小时内回复</p>
            </div>

            <div class="text-center">
              <div
                class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <svg
                  class="w-8 h-8 text-orange-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  ></path>
                </svg>
              </div>
              <h3 class="font-bold text-gray-900 mb-2">微信咨询</h3>
              <p class="text-orange-600 font-semibold">20155031</p>
              <p class="text-sm text-gray-500">扫码添加微信</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 联系表单 -->
          <div class="card-business p-8">
            <div class="mb-6">
              <h2 class="heading-secondary mb-2">发送消息</h2>
              <p class="text-gray-600">请填写以下信息，我们会尽快与您联系</p>
            </div>

            <form @submit.prevent="submitForm" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-2"
                  >
                    姓名 <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    v-model="form.name"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                    placeholder="请输入您的姓名"
                  />
                </div>
                <div>
                  <label
                    for="phone"
                    class="block text-sm font-medium text-gray-700 mb-2"
                  >
                    电话 <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    v-model="form.phone"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                    placeholder="请输入您的电话"
                  />
                </div>
              </div>

              <div>
                <label
                  for="email"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >
                  邮箱
                </label>
                <input
                  type="email"
                  id="email"
                  v-model="form.email"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                  placeholder="请输入您的邮箱"
                />
              </div>

              <div>
                <label
                  for="subject"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >
                  咨询类型 <span class="text-red-500">*</span>
                </label>
                <select
                  id="subject"
                  v-model="form.subject"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                >
                  <option value="">请选择咨询类型</option>
                  <option value="产品咨询">产品功能咨询</option>
                  <option value="技术支持">技术支持</option>
                  <option value="商务合作">商务合作</option>
                  <option value="售前咨询">售前咨询</option>
                  <option value="其他问题">其他问题</option>
                </select>
              </div>

              <div>
                <label
                  for="message"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >
                  详细描述 <span class="text-red-500">*</span>
                </label>
                <textarea
                  id="message"
                  v-model="form.message"
                  required
                  rows="5"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors resize-none"
                  placeholder="请详细描述您的问题或需求，我们会根据您的描述提供更精准的服务"
                ></textarea>
              </div>

              <div class="pt-4">
                <button type="submit" class="btn-primary w-full py-4 text-lg">
                  提交咨询
                </button>
                <p class="text-sm text-gray-500 mt-3 text-center">
                  提交后我们会在24小时内与您联系
                </p>
              </div>
            </form>
          </div>

          <!-- 详细联系信息和服务时间 -->
          <div class="space-y-6">
            <!-- 联系方式详情 -->
            <div class="card-business p-6">
              <h3 class="heading-tertiary mb-4">联系方式详情</h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1"
                  >
                    <svg
                      class="w-5 h-5 text-orange-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      ></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">销售热线</h4>
                    <p class="text-orange-600 font-semibold">155-1196-5595</p>
                    <p class="text-sm text-gray-500">工作日 9:00-18:00</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div
                    class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1"
                  >
                    <svg
                      class="w-5 h-5 text-orange-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      ></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">商务邮箱</h4>
                    <p class="text-orange-600 font-semibold"><EMAIL></p>
                    <p class="text-sm text-gray-500">24小时内回复</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div
                    class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1"
                  >
                    <svg
                      class="w-5 h-5 text-orange-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      ></path>
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      ></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">公司地址</h4>
                    <p class="text-gray-700">广东省广州市天河区</p>
                    <p class="text-sm text-gray-500">可预约上门服务</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 服务时间 -->
            <div class="card-business p-6">
              <h3 class="heading-tertiary mb-4">服务时间</h3>
              <div class="space-y-3">
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-gray-700">销售咨询</span>
                  <span class="font-semibold text-gray-900"
                    >周一至周五 9:00-18:00</span
                  >
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-gray-700">技术支持</span>
                  <span class="font-semibold text-gray-900">7×24小时</span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-gray-700">邮件回复</span>
                  <span class="font-semibold text-gray-900">24小时内</span>
                </div>
              </div>
            </div>

            <!-- 微信二维码 -->
            <div class="card-business p-6 text-center">
              <h3 class="heading-tertiary mb-4">微信咨询</h3>
              <div class="inline-block bg-gray-50 p-4 rounded-lg">
                <img
                  src="/images/wechat-qrcode.jpg"
                  alt="微信二维码"
                  class="w-32 h-32 mx-auto"
                />
                <p class="text-sm text-gray-600 mt-2">扫码添加微信</p>
                <p class="text-xs text-gray-500">微信号：20155031</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 常见问题部分 -->
      <div class="container mx-auto px-4 py-12">
        <div class="card-business p-8">
          <div class="text-center mb-12">
            <h2 class="heading-secondary mb-4">常见问题解答</h2>
            <p class="text-gray-600">快速找到您关心的问题答案</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="space-y-6">
              <div class="border-l-4 border-orange-500 pl-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-900">
                  产品功能咨询
                </h3>
                <p class="text-gray-600 mb-4">
                  我们的产品支持多种扫描设备和图像处理功能，兼容Windows和Linux平台。如需了解具体功能特性，请查看产品详情页面或联系我们的技术顾问。
                </p>
                <NuxtLink
                  to="/products"
                  class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
                >
                  查看产品详情
                  <svg
                    class="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </NuxtLink>
              </div>

              <div class="border-l-4 border-orange-500 pl-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-900">
                  技术支持服务
                </h3>
                <p class="text-gray-600 mb-4">
                  我们提供7×24小时技术支持服务，包括远程协助、现场服务等。技术团队具备丰富的项目经验，能够快速解决各种技术问题。
                </p>
                <NuxtLink
                  to="/documents"
                  class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
                >
                  查看技术文档
                  <svg
                    class="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </NuxtLink>
              </div>
            </div>

            <div class="space-y-6">
              <div class="border-l-4 border-orange-500 pl-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-900">
                  购买和授权
                </h3>
                <p class="text-gray-600 mb-4">
                  我们提供灵活的授权方案和多种支付方式，支持企业采购流程。所有产品均提供正规发票和软件著作权授权文件。
                </p>
                <NuxtLink
                  to="/purchase"
                  class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
                >
                  了解购买流程
                  <svg
                    class="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </NuxtLink>
              </div>

              <div class="border-l-4 border-orange-500 pl-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-900">
                  合作与定制
                </h3>
                <p class="text-gray-600 mb-4">
                  我们欢迎各类企业合作，提供产品定制、技术咨询、解决方案设计等服务。可根据您的具体需求提供专业的定制化方案。
                </p>
                <div
                  class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center cursor-pointer"
                >
                  联系商务合作
                  <svg
                    class="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速联系提示 -->
          <div
            class="mt-12 p-6 bg-orange-50 rounded-lg border border-orange-200"
          >
            <div class="text-center">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                没有找到您要的答案？
              </h3>
              <p class="text-gray-600 mb-4">我们的专业团队随时为您提供帮助</p>
              <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:155-1196-5595"
                  class="btn-primary inline-flex items-center justify-center"
                >
                  <svg
                    class="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                  立即致电
                </a>
                <button
                  @click="scrollToForm"
                  class="btn-secondary inline-flex items-center justify-center"
                >
                  <svg
                    class="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  在线咨询
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { ref } from "vue";
import Header from "~/components/Header.vue";
import Footer from "~/components/Footer.vue";

const form = ref({
  name: "",
  email: "",
  phone: "",
  subject: "",
  message: "",
});

const submitForm = () => {
  // 验证必填字段
  if (
    !form.value.name ||
    !form.value.phone ||
    !form.value.subject ||
    !form.value.message
  ) {
    alert("请填写所有必填字段");
    return;
  }

  // 这里添加表单提交逻辑
  alert("感谢您的咨询！我们会在24小时内与您联系。");

  // 重置表单
  form.value = {
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  };
};

const scrollToForm = () => {
  const formElement = document.querySelector("form");
  if (formElement) {
    formElement.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
