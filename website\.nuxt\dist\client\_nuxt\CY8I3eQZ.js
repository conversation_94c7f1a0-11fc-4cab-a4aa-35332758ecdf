import{o,c as a,a as e,F as l,k as c,n as m,t as n,b as h,w as f,d as g,l as b,_ as v,r as x,T as y,q as w}from"./Dy7juzJL.js";import{_ as k,a as j}from"./DYzyhJ5q.js";import{_ as W}from"./L-3nxwpR.js";const C={class:"bg-white shadow-lg rounded-lg overflow-hidden"},$=e("div",{class:"bg-blue-600 text-white p-4"},[e("h2",{class:"text-2xl font-bold"},"产品列表")],-1),I=["onClick"],P={class:"font-semibold text-gray-800"},O={class:"text-xs text-gray-500 mt-1"},U={__name:"ProductSidebar",props:{products:Array,selectedProductId:Number},emits:["select"],setup(s){const d=r=>r.length>50?r.substring(0,50)+"...":r;return(r,u)=>(o(),a("div",C,[$,e("ul",null,[(o(!0),a(l,null,c(s.products,t=>(o(),a("li",{key:t.id,class:"border-b last:border-b-0"},[e("button",{onClick:i=>r.$emit("select",t),class:m(["w-full text-left p-4 flex items-center transition-colors",{"bg-blue-100":t.id===s.selectedProductId,"hover:bg-gray-100":t.id!==s.selectedProductId}])},[e("div",{class:m(["w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold text-xl",{"bg-gradient-to-r from-blue-500 to-indigo-600":t.id===1,"bg-gradient-to-r from-purple-500 to-pink-600":t.id===2,"bg-gradient-to-r from-green-500 to-teal-600":t.id===3}])},n(t.name.charAt(0)),3),e("div",null,[e("p",P,n(t.name),1),e("p",O,n(d(t.description)),1)])],10,I)]))),128))])]))}},B={key:0,class:"card-business overflow-hidden"},S={class:"bg-business-gradient p-8"},V={class:"flex items-center justify-between"},M={class:"text-2xl md:text-3xl font-bold text-white mb-2"},D=e("p",{class:"text-blue-100"},"企业级Web控件解决方案",-1),N=e("div",{class:"text-right text-white"},[e("div",{class:"text-sm text-blue-100"},"软件著作权"),e("div",{class:"font-semibold"},"2013SR145420")],-1),A={class:"p-8"},q={class:"mb-8"},z={class:"text-business text-lg leading-relaxed"},H={class:"mb-8 p-6 bg-orange-50 rounded-lg border border-orange-200"},L={class:"text-center"},E=e("h3",{class:"text-xl font-bold text-gray-900 mb-2"},"立即开始免费试用",-1),G=e("p",{class:"text-gray-600 mb-4"},"无需注册，支持多平台，专业技术支持",-1),R=e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),T={class:"mb-8"},Y=e("h3",{class:"heading-secondary mb-6"},"核心功能特性",-1),F={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J=e("div",{class:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1),K={class:"text-gray-700 font-medium"},Q={key:0,class:"mb-8"},X=e("h3",{class:"heading-secondary mb-6"},"技术规格详情",-1),Z={class:"overflow-x-auto"},ee={class:"min-w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm"},te=e("thead",null,[e("tr",{class:"bg-gray-50"},[e("th",{class:"border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"}," 特性分类 "),e("th",{class:"border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"}," 技术参数 ")])],-1),se={class:"border-b border-gray-200 px-6 py-4 font-medium text-gray-900 w-1/4"},oe={class:"border-b border-gray-200 px-6 py-4 text-gray-700 whitespace-pre-line"},ae={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},ne={class:"card-business p-6"},re=e("h3",{class:"heading-tertiary mb-4"},"基础技术规格",-1),ie={class:"space-y-3"},de={class:"font-medium text-gray-700"},le={class:"text-gray-600 text-right"},ce={class:"card-business p-6"},he=e("h3",{class:"heading-tertiary mb-4"},"典型应用场景",-1),ue={class:"grid grid-cols-2 gap-3"},ge=e("svg",{class:"w-5 h-5 text-orange-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1),me={class:"text-gray-700 font-medium"},_e={class:"feature-highlight mb-8"},pe={class:"flex items-center justify-between"},be=e("h3",{class:"heading-tertiary text-orange-700 mb-2"}," 企业级定价方案 ",-1),xe={class:"text-orange-600"},fe=e("div",{class:"text-right"},[e("div",{class:"text-sm text-orange-600"},"联系销售获取"),e("div",{class:"font-bold text-orange-700"},"专属优惠价格")],-1),ve={class:"flex flex-col sm:flex-row gap-4"},ye=["href"],we=e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),ke=["href"],je=e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),We={__name:"ProductDetails",props:{product:Object},setup(s){return(d,r)=>{const u=W;return s.product?(o(),a("div",B,[e("div",S,[e("div",V,[e("div",null,[e("h1",M,n(s.product.name),1),D]),N])]),e("div",A,[e("div",q,[e("p",z,n(s.product.description),1)]),e("div",H,[e("div",L,[E,G,h(u,{to:"/download",class:"btn-primary text-lg px-8 py-4 inline-flex items-center"},{default:f(()=>[R,g(" 免费下载试用版 ")]),_:1})])]),e("div",T,[Y,e("div",F,[(o(!0),a(l,null,c(s.product.features,t=>(o(),a("div",{key:t,class:"flex items-start p-4 bg-gray-50 rounded-lg"},[J,e("span",K,n(t),1)]))),128))])]),s.product.tableData?(o(),a("div",Q,[X,e("div",Z,[e("table",ee,[te,e("tbody",null,[(o(!0),a(l,null,c(s.product.tableData,(t,i)=>(o(),a("tr",{key:i,class:m(i%2===0?"bg-white":"bg-gray-50")},[e("td",se,n(t.category),1),e("td",oe,n(t.value),1)],2))),128))])])])])):b("",!0),e("div",ae,[e("div",ne,[re,e("div",ie,[(o(!0),a(l,null,c(s.product.specs,(t,i)=>(o(),a("div",{key:i,class:"flex justify-between py-2 border-b border-gray-100 last:border-b-0"},[e("span",de,n(i),1),e("span",le,n(t),1)]))),128))])]),e("div",ce,[he,e("div",ue,[(o(!0),a(l,null,c(s.product.useCases,t=>(o(),a("div",{key:t,class:"flex items-center p-3 bg-orange-50 rounded-lg"},[ge,e("span",me,n(t),1)]))),128))])])]),e("div",_e,[e("div",pe,[e("div",null,[be,e("p",xe,n(s.product.pricing),1)]),fe])]),e("div",ve,[e("a",{href:s.product.videoUrl,target:"_blank",class:"btn-secondary flex-1 text-center inline-flex items-center justify-center"},[we,g(" 观看产品演示 ")],8,ye),e("a",{href:s.product.pdfUrl,target:"_blank",class:"btn-secondary flex-1 text-center inline-flex items-center justify-center"},[je,g(" 下载产品资料 ")],8,ke)])])])):b("",!0)}}},Ce={class:"bg-gray-100 min-h-screen"},$e={class:"container mx-auto py-12 px-4"},Ie={class:"flex flex-col lg:flex-row gap-8"},Pe={class:"lg:w-1/4"},Oe={class:"lg:w-3/4"},Ue={__name:"products",setup(s){const d=x([{id:1,name:"ScanOnWeb",description:"ScanOnWeb控件(软件著作权登记号 2013SR145420，证书号0651182)用于处理图像扫描编程，适合用于web环境下的扫描仪编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容目前主流的数款扫描设备，对于个别非按标准协议支持的扫描设备亦提供了集成支持，目前控件经过多年的发展已经很成熟稳定，被广泛的应用于办公OA、电子政务、纸质文档电子化等应用场景，客户单位遍布税务、公安、建筑、银行等多个行业，是目前国内唯一成熟稳定的扫描控件产品。",image:"/images/slide1.png",features:["扫描设备选择","自动进纸器连续多页扫描","双面扫描模式","自动纠偏模式","多种图像扫描模式","多种分辨率设置"],specs:{浏览器兼容性:"所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等",图像扫描协议:"支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备，支持WIA协议的所有图像采集设备",图像编辑特性:"支持旋转、裁剪、填白、马赛克等多种处理",图像上传能力:"支持多种格式上传到指定URL"},useCases:["办公OA","电子政务","纸质文档电子化","税务系统","公安系统","银行业务"],pricing:"请下载报价单了解详情",videoUrl:"https://example.com/scanonweb-demo.mp4",pdfUrl:"/ScanOnWeb-quotation.pdf",tableData:[{category:"浏览器兼容性",value:"所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等"},{category:"图像扫描协议",value:"1.支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备2.支持WIA协议的所有图像采集设备"},{category:"图像扫描参数控制",value:"1. 扫描设备选择 2. 是否显示扫描仪驱动程序内置设置对话框 3. 是否使用自动进纸器进行连续多页扫描 4. 是否使用自动装填纸张模式 5. 是否使用双面扫描模式 6. 是否使用自动纠偏模式 7. 是否使用自动边框检测模式 8. 图像扫描模式：黑白模式、灰度模式、彩色模式 9. dpi分辨率设置 10. 扫描结果传输模式：内存、文件、原生"},{category:"图像编辑特性",value:"1. 支持图像向左、向右旋转 90 度 2. 支持图像自定义角度旋转 3. 支持魔术棒选择模式图像选择 4. 支持矩形图像选择 5. 支持选中区域填白处理 6. 支持选中区域反选填白处理 7. 支持选中区域马赛克处理 8. 支持裁剪选区以外所有图像处理 9. 支持选中区域归红、归绿处理 10. 支持去除图像黑边处理 11. 支持去除图像底色处理 12. UNDO 撤销操作 13. 客户端单页本地图像保存 14. 客户端单页本地图像打印及打印预览 15. 批量图像删除 16. 多页扫描结果排序（直接拖拽顺序） 17. 扫描图像删除单页处理 18. 客户端多页图像保存 19. 客户端多页图像打印机打印预览 20. 鼠标滚轮缩放图像"},{category:"图像上传能力",value:"1. 图像转jpg base64编码，供前端显示或上传 2. 图像按照 tiff 格式上传到指定 url 3. 图像按照 pdf 格式上传到指定 url 4. 图像按照多页 jpg 方式上传到指定 url 5. 单独将某一页图像以 jpg 方式上传到指定 url 6. 上传到指定的 sftp 地址（需定制）"}]},{id:2,name:"ImageCapOnWeb",description:"ImageCapOnWeb控件用于处理摄像头图像采集编程，适合用于web环境下的图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的大部分摄像头数码设备。目前已经在多所学校、银行、政府机构中进行集成应用。",image:"/images/slide2.png",features:["摄像头图像采集","Web环境下的图像采集编程","兼容Windows平台下的大部分摄像头设备","无缝集成到多种编程技术"],specs:{兼容平台:"Windows平台",支持设备:"大部分摄像头数码设备",集成技术:"jsp、php、asp.net等"},useCases:["学校信息系统","银行业务系统","政府机构应用"],pricing:"请下载报价单了解详情",videoUrl:"https://example.com/imagecaponweb-demo.mp4",pdfUrl:"/ImageCapOnWeb-quotation.pdf"},{id:3,name:"GaoPaiYi",description:"GaoPaiYi控件用于处理高拍仪图像采集编程，适合用于web环境下的高拍仪图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的以摄像头为核心设备的高拍仪产品。",image:"/images/slide3.png",features:["高拍仪图像采集","Web环境下的编程应用","兼容Windows平台下的高拍仪产品","无缝集成到各种编程技术"],specs:{兼容平台:"Windows平台",支持设备:"以摄像头为核心设备的高拍仪产品",集成技术:"jsp、php、asp.net等"},useCases:["文档扫描","证件采集","教育培训"],pricing:"请下载报价单了解详情",videoUrl:"https://example.com/gaopaiyi-demo.mp4",pdfUrl:"/GaoPaiYi-quotation.pdf"}]),r=x(d.value[0]||null),u=t=>{r.value=t};return(t,i)=>{var _;return o(),a("div",Ce,[h(k),e("main",$e,[e("div",Ie,[e("div",Pe,[h(U,{products:d.value,selectedProductId:(_=r.value)==null?void 0:_.id,onSelect:u},null,8,["products","selectedProductId"])]),e("div",Oe,[h(y,{name:"fade",mode:"out-in"},{default:f(()=>{var p;return[(o(),w(We,{key:(p=r.value)==null?void 0:p.id,product:r.value},null,8,["product"]))]}),_:1})])])]),h(j)])}}},Me=v(Ue,[["__scopeId","data-v-9e2630bc"]]);export{Me as default};
