import{_ as o}from"./L-3nxwpR.js";import{u as f}from"./Cp9XtYH-.js";import{_ as i,a as l}from"./DYzyhJ5q.js";import{_ as r,c as v,b as t,a as d,w as c,j as p,o as m,d as s,p as u,e as g}from"./Dy7juzJL.js";const e=a=>(u("data-v-d2fddfc7"),a=a(),g(),a),h={class:"bg-gray-50 min-h-screen"},b={class:"bg-white border-b border-gray-200"},q={class:"container mx-auto px-4 py-4"},x={class:"flex items-center space-x-2 text-sm text-gray-500"},_=e(()=>d("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),w=e(()=>d("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[d("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),y=e(()=>d("span",{class:"text-gray-900 font-medium"},"ScanOnWeb API文档",-1)),S=p(`<div class="bg-white py-8" data-v-d2fddfc7><div class="container mx-auto px-4" data-v-d2fddfc7><div class="max-w-4xl" data-v-d2fddfc7><h1 class="heading-primary mb-4" data-v-d2fddfc7>ScanOnWeb API文档</h1><p class="text-lg text-gray-600 mb-6" data-v-d2fddfc7> 完整的ScanOnWeb扫描控件JavaScript API参考文档，包含所有方法、属性和事件回调 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-d2fddfc7><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>完整API参考</span></div><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>详细参数说明</span></div><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>代码示例</span></div></div></div></div></div><div class="container mx-auto py-12 px-4" data-v-d2fddfc7><div class="card-business p-6 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-4" data-v-d2fddfc7>文档目录</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-d2fddfc7><a href="#constructor" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>构造函数</div><div class="text-sm text-gray-600" data-v-d2fddfc7>ScanOnWeb()</div></a><a href="#config" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>配置属性</div><div class="text-sm text-gray-600" data-v-d2fddfc7>scaner_work_config</div></a><a href="#device-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>设备管理</div><div class="text-sm text-gray-600" data-v-d2fddfc7>设备相关方法</div></a><a href="#scan-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>扫描操作</div><div class="text-sm text-gray-600" data-v-d2fddfc7>扫描相关方法</div></a><a href="#image-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>图像处理</div><div class="text-sm text-gray-600" data-v-d2fddfc7>图像相关方法</div></a><a href="#upload-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>上传保存</div><div class="text-sm text-gray-600" data-v-d2fddfc7>文件操作方法</div></a><a href="#events" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>事件回调</div><div class="text-sm text-gray-600" data-v-d2fddfc7>所有事件回调</div></a><a href="#examples" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>使用示例</div><div class="text-sm text-gray-600" data-v-d2fddfc7>完整代码示例</div></a><a href="#server-examples" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>服务器端代码</div><div class="text-sm text-gray-600" data-v-d2fddfc7>后端接收示例</div></a></div></div><div id="constructor" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>构造函数</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>new ScanOnWeb()</h3><p class="api-description" data-v-d2fddfc7> 创建ScanOnWeb扫描控件实例，自动初始化WebSocket连接并尝试连接本地托盘服务。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 创建扫描控件实例
let scanonweb = new ScanOnWeb();

// 设置事件回调
scanonweb.onScanFinishedEvent = function(msg) {
    console.log(&#39;扫描完成，图像数量：&#39;, msg.imageAfterCount);
};</pre></div></div><div class="api-notes" data-v-d2fddfc7><h4 class="api-notes-title" data-v-d2fddfc7>注意事项</h4><ul class="api-notes-list" data-v-d2fddfc7><li data-v-d2fddfc7> 构造函数会自动尝试连接本地WebSocket服务（端口1001-5001） </li><li data-v-d2fddfc7>需要确保本地已安装并运行ScanOnWeb托盘服务程序</li><li data-v-d2fddfc7>如果连接失败，相关操作方法将无法正常工作</li></ul></div></div></div><div id="config" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>配置属性</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>scaner_work_config</h3><p class="api-description" data-v-d2fddfc7> 扫描工作配置对象，包含所有扫描参数设置。在调用startScan()方法前需要正确配置这些参数。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>配置参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>默认值</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>showUI</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否显示扫描控件工作界面</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dpi_x</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>300</td><td data-v-d2fddfc7>水平分辨率（DPI）</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dpi_y</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>300</td><td data-v-d2fddfc7>垂直分辨率（DPI）</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>deviceIndex</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>0</td><td data-v-d2fddfc7>选中的扫描设备索引</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>showDialog</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否显示设备内置对话框</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoFeedEnable</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>true</td><td data-v-d2fddfc7>是否启用自动进纸器</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoFeed</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否自动装填纸张</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dupxMode</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否启用双面扫描模式</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoDeskew</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否启用自动纠偏</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoBorderDetection</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>是否启用自动边框检测</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>colorMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>&quot;RGB&quot;</td><td data-v-d2fddfc7>色彩模式：RGB(彩色)、GRAY(灰色)、BW(黑白)</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>transMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>&quot;memory&quot;</td><td data-v-d2fddfc7>数据传输模式：memory、file、native</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>配置示例</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 配置扫描参数
scanonweb.scaner_work_config = {
    showUI: false,
    dpi_x: 600,
    dpi_y: 600,
    deviceIndex: 0,
    showDialog: false,
    autoFeedEnable: true,
    autoFeed: false,
    dupxMode: true,  // 启用双面扫描
    autoDeskew: true,  // 启用自动纠偏
    autoBorderDetection: true,  // 启用边框检测
    colorMode: &quot;RGB&quot;,
    transMode: &quot;memory&quot;
};</pre></div></div></div></div><div id="device-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>设备管理方法</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>loadDevices()</h3><p class="api-description" data-v-d2fddfc7> 获取系统中所有可用的扫描设备列表。调用后会触发onGetDevicesListEvent事件回调。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置设备列表回调
scanonweb.onGetDevicesListEvent = function(msg) {
    console.log(&#39;设备列表:&#39;, msg.devices);
    console.log(&#39;当前选中设备索引:&#39;, msg.currentIndex);

    // 填充设备选择下拉框
    const deviceSelect = document.getElementById(&#39;deviceSelect&#39;);
    deviceSelect.innerHTML = &#39;&#39;;
    msg.devices.forEach((device, index) =&gt; {
        const option = document.createElement(&#39;option&#39;);
        option.value = index;
        option.textContent = device;
        deviceSelect.appendChild(option);
    });
};

// 获取设备列表
scanonweb.loadDevices();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>selectScanDevice(deviceIndex)</h3><p class="api-description" data-v-d2fddfc7>选择指定的扫描设备作为当前工作设备。</p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>deviceIndex</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>设备索引，从0开始</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 选择第一个设备
scanonweb.selectScanDevice(0);

// 选择第二个设备
scanonweb.selectScanDevice(1);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> setLicenseKey(licenseMode, key1, key2, licenseServerUrl) </h3><p class="api-description" data-v-d2fddfc7> 设置软件授权信息。在使用扫描功能前需要设置有效的授权密钥。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>licenseMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>授权模式</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>key1</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>授权密钥1</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>key2</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>授权密钥2</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>licenseServerUrl</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>否</td><td data-v-d2fddfc7>授权服务器URL</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置授权信息
scanonweb.setLicenseKey(
    &quot;online&quot;,
    &quot;your-license-key-1&quot;,
    &quot;your-license-key-2&quot;,
    &quot;https://license.brainysoft.cn&quot;
);</pre></div></div></div></div><div id="scan-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>扫描操作方法</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>startScan()</h3><p class="api-description" data-v-d2fddfc7> 开始扫描操作。使用当前的scaner_work_config配置进行扫描。扫描完成后会触发onScanFinishedEvent事件。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 配置扫描参数
scanonweb.scaner_work_config.dpi_x = 600;
scanonweb.scaner_work_config.dpi_y = 600;
scanonweb.scaner_work_config.colorMode = &quot;RGB&quot;;
scanonweb.scaner_work_config.deviceIndex = 0;

// 设置扫描完成回调
scanonweb.onScanFinishedEvent = function(msg) {
    console.log(&#39;扫描前图像数量:&#39;, msg.imageBeforeCount);
    console.log(&#39;扫描后图像数量:&#39;, msg.imageAfterCount);

    // 自动获取扫描结果
    scanonweb.getAllImage();
};

// 开始扫描
scanonweb.startScan();</pre></div></div><div class="api-notes" data-v-d2fddfc7><h4 class="api-notes-title" data-v-d2fddfc7>注意事项</h4><ul class="api-notes-list" data-v-d2fddfc7><li data-v-d2fddfc7>扫描前确保已选择正确的设备（deviceIndex）</li><li data-v-d2fddfc7>确保设备已连接并处于就绪状态</li><li data-v-d2fddfc7>扫描参数配置会影响扫描质量和速度</li></ul></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>clearAll()</h3><p class="api-description" data-v-d2fddfc7> 清除所有已扫描的图像数据，释放内存空间。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 清除所有图像
scanonweb.clearAll();

// 同时清除页面显示
document.getElementById(&#39;imageList&#39;).innerHTML = &#39;&#39;;</pre></div></div></div></div><div id="image-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>图像处理方法</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getAllImage()</h3><p class="api-description" data-v-d2fddfc7> 获取所有已扫描的图像数据。调用后会触发onGetAllImageEvent事件回调，返回Base64编码的图像数据。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置获取图像回调
scanonweb.onGetAllImageEvent = function(msg) {
    console.log(&#39;图像数量:&#39;, msg.imageCount);
    console.log(&#39;当前选中:&#39;, msg.currentSelected);

    // 显示图像
    const imageList = document.getElementById(&#39;imageList&#39;);
    imageList.innerHTML = &#39;&#39;;

    msg.images.forEach((imageBase64, index) =&gt; {
        const img = document.createElement(&#39;img&#39;);
        img.src = &#39;data:image/jpg;base64,&#39; + imageBase64;
        img.style.width = &#39;200px&#39;;
        img.style.height = &#39;200px&#39;;
        img.style.margin = &#39;10px&#39;;
        img.style.border = &#39;1px solid #ccc&#39;;
        imageList.appendChild(img);
    });
};

// 获取所有图像
scanonweb.getAllImage();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageById(index)</h3><p class="api-description" data-v-d2fddfc7> 获取指定索引的单张图像数据。调用后会触发onGetImageByIdEvent事件回调。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像索引，从0开始</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置单张图像回调
scanonweb.onGetImageByIdEvent = function(msg) {
    console.log(&#39;图像索引:&#39;, msg.imageIndex);
    console.log(&#39;图像数据:&#39;, msg.imageBase64);

    // 显示图像
    const img = document.createElement(&#39;img&#39;);
    img.src = &#39;data:image/jpg;base64,&#39; + msg.imageBase64;
    document.body.appendChild(img);
};

// 获取第一张图像
scanonweb.getImageById(0);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageCount()</h3><p class="api-description" data-v-d2fddfc7> 获取当前已扫描的图像总数。调用后会触发onGetImageCountEvent事件回调。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置图像计数回调
scanonweb.onGetImageCountEvent = function(msg) {
    console.log(&#39;图像总数:&#39;, msg.imageCount);
    console.log(&#39;当前选中图像索引:&#39;, msg.currentSelected);
};

// 获取图像数量
scanonweb.getImageCount();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>rotateImage(index, angle)</h3><p class="api-description" data-v-d2fddfc7> 旋转指定索引的图像。支持90度的倍数旋转。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像索引，从0开始</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>angle</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>旋转角度：90、180、270</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 将第一张图像顺时针旋转90度
scanonweb.rotateImage(0, 90);

// 将第二张图像旋转180度
scanonweb.rotateImage(1, 180);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageSize(index)</h3><p class="api-description" data-v-d2fddfc7> 获取指定索引图像的尺寸信息。调用后会触发onGetImageSizeEvent事件回调。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像索引，从0开始</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置图像尺寸回调
scanonweb.onGetImageSizeEvent = function(msg) {
    console.log(&#39;图像宽度:&#39;, msg.width);
    console.log(&#39;图像高度:&#39;, msg.height);
    console.log(&#39;图像索引:&#39;, msg.imageIndex);
};

// 获取第一张图像尺寸
scanonweb.getImageSize(0);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>loadImageFromUrl(url)</h3><p class="api-description" data-v-d2fddfc7> 从远程URL加载图像到扫描控件中。支持多页图像文件。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像文件的URL地址</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 从服务器加载图像
scanonweb.loadImageFromUrl(&#39;https://example.com/document.pdf&#39;);

// 加载本地服务器图像
scanonweb.loadImageFromUrl(&#39;/uploads/scan_result.tiff&#39;);</pre></div></div></div></div><div id="upload-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>上传保存方法</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> uploadAllImageAsPdfToUrl(url, id, desc) </h3><p class="api-description" data-v-d2fddfc7> 将所有图像合并为PDF格式并上传到指定URL。调用后会触发onUploadAllImageAsPdfToUrlEvent事件回调。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>上传目标URL地址</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>id</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>文档标识ID</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>desc</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>否</td><td data-v-d2fddfc7>文档描述信息</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 设置上传回调
scanonweb.onUploadAllImageAsPdfToUrlEvent = function(msg) {
    const result = JSON.parse(msg.uploadResult);
    if (result.network) {
        console.log(&#39;上传成功:&#39;, result.msg);
    } else {
        console.error(&#39;上传失败:&#39;, result.msg);
    }
};

// 上传PDF到服务器
scanonweb.uploadAllImageAsPdfToUrl(
    &#39;https://api.example.com/upload&#39;,
    &#39;DOC_001&#39;,
    &#39;扫描文档&#39;
);</pre></div></div><div id="server-examples" class="api-server-examples" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>服务器端接收代码示例</h4><p class="text-gray-600 mb-4" data-v-d2fddfc7> ScanOnWeb控件通过multipart/form-data方式提交数据，包含以下4个参数： </p><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6" data-v-d2fddfc7><h5 class="font-bold text-blue-800 mb-2" data-v-d2fddfc7>提交参数说明</h5><ul class="space-y-1 text-blue-700 text-sm" data-v-d2fddfc7><li data-v-d2fddfc7><strong data-v-d2fddfc7>image</strong> - 上传的图像文件二进制数据（PDF格式） </li><li data-v-d2fddfc7><strong data-v-d2fddfc7>imageCount</strong> - 本次上传的图像总数</li><li data-v-d2fddfc7><strong data-v-d2fddfc7>id</strong> - 调用方法时传入的业务ID参数</li><li data-v-d2fddfc7><strong data-v-d2fddfc7>desc</strong> - 调用方法时传入的描述信息参数</li></ul></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Java Spring Boot</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>@RestController
@RequestMapping(&quot;/api&quot;)
public class ScanUploadController {

    @PostMapping(&quot;/upload&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; uploadScanImages(
            @RequestParam(&quot;image&quot;) MultipartFile imageFile,
            @RequestParam(&quot;imageCount&quot;) Integer imageCount,
            @RequestParam(&quot;id&quot;) String id,
            @RequestParam(value = &quot;desc&quot;, required = false) String desc) {

        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();

        try {
            // 验证文件
            if (imageFile.isEmpty()) {
                response.put(&quot;network&quot;, false);
                response.put(&quot;msg&quot;, &quot;上传文件为空&quot;);
                return ResponseEntity.badRequest().body(response);
            }

            // 生成文件名
            String fileName = id + &quot;_&quot; + System.currentTimeMillis() + &quot;.pdf&quot;;
            String uploadDir = &quot;/uploads/scan/&quot;;
            Path uploadPath = Paths.get(uploadDir);

            // 创建目录
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 保存文件
            Path filePath = uploadPath.resolve(fileName);
            Files.copy(imageFile.getInputStream(), filePath,
                      StandardCopyOption.REPLACE_EXISTING);

            // 记录到数据库
            ScanDocument document = new ScanDocument();
            document.setBusinessId(id);
            document.setDescription(desc);
            document.setImageCount(imageCount);
            document.setFilePath(filePath.toString());
            document.setFileName(fileName);
            document.setFileSize(imageFile.getSize());
            document.setUploadTime(new Date());

            scanDocumentService.save(document);

            // 返回成功响应
            response.put(&quot;network&quot;, true);
            response.put(&quot;msg&quot;, &quot;上传成功&quot;);
            response.put(&quot;fileId&quot;, document.getId());
            response.put(&quot;fileName&quot;, fileName);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error(&quot;文件上传失败&quot;, e);
            response.put(&quot;network&quot;, false);
            response.put(&quot;msg&quot;, &quot;上传失败: &quot; + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>ASP.NET Core</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>[ApiController]
[Route(&quot;api/[controller]&quot;)]
public class ScanUploadController : ControllerBase
{
    private readonly ILogger&lt;ScanUploadController&gt; _logger;
    private readonly IScanDocumentService _scanDocumentService;

    public ScanUploadController(ILogger&lt;ScanUploadController&gt; logger,
                               IScanDocumentService scanDocumentService)
    {
        _logger = logger;
        _scanDocumentService = scanDocumentService;
    }

    [HttpPost(&quot;upload&quot;)]
    public async Task&lt;IActionResult&gt; UploadScanImages(
        [FromForm] IFormFile image,
        [FromForm] int imageCount,
        [FromForm] string id,
        [FromForm] string desc = null)
    {
        try
        {
            // 验证文件
            if (image == null || image.Length == 0)
            {
                return BadRequest(new { network = false, msg = &quot;上传文件为空&quot; });
            }

            // 生成文件名
            var fileName = $&quot;{id}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}.pdf&quot;;
            var uploadDir = Path.Combine(Directory.GetCurrentDirectory(), &quot;uploads&quot;, &quot;scan&quot;);

            // 创建目录
            if (!Directory.Exists(uploadDir))
            {
                Directory.CreateDirectory(uploadDir);
            }

            // 保存文件
            var filePath = Path.Combine(uploadDir, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await image.CopyToAsync(stream);
            }

            // 保存到数据库
            var document = new ScanDocument
            {
                BusinessId = id,
                Description = desc,
                ImageCount = imageCount,
                FilePath = filePath,
                FileName = fileName,
                FileSize = image.Length,
                UploadTime = DateTime.UtcNow
            };

            await _scanDocumentService.SaveAsync(document);

            // 返回成功响应
            return Ok(new
            {
                network = true,
                msg = &quot;上传成功&quot;,
                fileId = document.Id,
                fileName = fileName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, &quot;文件上传失败&quot;);
            return StatusCode(500, new { network = false, msg = $&quot;上传失败: {ex.Message}&quot; });
        }
    }
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Go (Gin Framework)</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>package main

import (
    &quot;fmt&quot;
    &quot;io&quot;
    &quot;net/http&quot;
    &quot;os&quot;
    &quot;path/filepath&quot;
    &quot;strconv&quot;
    &quot;time&quot;

    &quot;github.com/gin-gonic/gin&quot;
)

type ScanDocument struct {
    ID          uint      \`json:&quot;id&quot; gorm:&quot;primaryKey&quot;\`
    BusinessID  string    \`json:&quot;business_id&quot;\`
    Description string    \`json:&quot;description&quot;\`
    ImageCount  int       \`json:&quot;image_count&quot;\`
    FilePath    string    \`json:&quot;file_path&quot;\`
    FileName    string    \`json:&quot;file_name&quot;\`
    FileSize    int64     \`json:&quot;file_size&quot;\`
    UploadTime  time.Time \`json:&quot;upload_time&quot;\`
}

func uploadScanImages(c *gin.Context) {
    // 获取表单参数
    imageFile, header, err := c.Request.FormFile(&quot;image&quot;)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;获取上传文件失败: &quot; + err.Error(),
        })
        return
    }
    defer imageFile.Close()

    imageCount, _ := strconv.Atoi(c.PostForm(&quot;imageCount&quot;))
    id := c.PostForm(&quot;id&quot;)
    desc := c.PostForm(&quot;desc&quot;)

    // 验证文件
    if header.Size == 0 {
        c.JSON(http.StatusBadRequest, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;上传文件为空&quot;,
        })
        return
    }

    // 生成文件名
    fileName := fmt.Sprintf(&quot;%s_%d.pdf&quot;, id, time.Now().UnixMilli())
    uploadDir := &quot;./uploads/scan&quot;

    // 创建目录
    if err := os.MkdirAll(uploadDir, 0755); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;创建目录失败: &quot; + err.Error(),
        })
        return
    }

    // 保存文件
    filePath := filepath.Join(uploadDir, fileName)
    dst, err := os.Create(filePath)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;创建文件失败: &quot; + err.Error(),
        })
        return
    }
    defer dst.Close()

    if _, err := io.Copy(dst, imageFile); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;保存文件失败: &quot; + err.Error(),
        })
        return
    }

    // 保存到数据库
    document := ScanDocument{
        BusinessID:  id,
        Description: desc,
        ImageCount:  imageCount,
        FilePath:    filePath,
        FileName:    fileName,
        FileSize:    header.Size,
        UploadTime:  time.Now(),
    }

    // 这里应该调用数据库服务保存记录
    // db.Create(&amp;document)

    // 返回成功响应
    c.JSON(http.StatusOK, gin.H{
        &quot;network&quot;:  true,
        &quot;msg&quot;:      &quot;上传成功&quot;,
        &quot;fileId&quot;:   document.ID,
        &quot;fileName&quot;: fileName,
    })
}

func main() {
    r := gin.Default()
    r.POST(&quot;/api/upload&quot;, uploadScanImages)
    r.Run(&quot;:8080&quot;)
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Rust (Actix-web)</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>use actix_multipart::Multipart;
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use futures::{StreamExt, TryStreamExt};
use serde::{Deserialize, Serialize};
use std::io::Write;
use tokio::fs;
use uuid::Uuid;

#[derive(Serialize, Deserialize)]
struct ScanDocument {
    id: Option&lt;u32&gt;,
    business_id: String,
    description: Option&lt;String&gt;,
    image_count: i32,
    file_path: String,
    file_name: String,
    file_size: u64,
    upload_time: chrono::DateTime&lt;chrono::Utc&gt;,
}

#[derive(Serialize)]
struct UploadResponse {
    network: bool,
    msg: String,
    #[serde(skip_serializing_if = &quot;Option::is_none&quot;)]
    file_id: Option&lt;u32&gt;,
    #[serde(skip_serializing_if = &quot;Option::is_none&quot;)]
    file_name: Option&lt;String&gt;,
}

async fn upload_scan_images(mut payload: Multipart) -&gt; Result&lt;HttpResponse&gt; {
    let mut image_data: Option&lt;Vec&lt;u8&gt;&gt; = None;
    let mut image_count: Option&lt;i32&gt; = None;
    let mut business_id: Option&lt;String&gt; = None;
    let mut description: Option&lt;String&gt; = None;

    // 解析multipart数据
    while let Ok(Some(mut field)) = payload.try_next().await {
        let content_disposition = field.content_disposition();

        if let Some(name) = content_disposition.get_name() {
            match name {
                &quot;image&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    image_data = Some(data);
                }
                &quot;imageCount&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(count_str) = String::from_utf8(data) {
                        image_count = count_str.parse().ok();
                    }
                }
                &quot;id&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(id_str) = String::from_utf8(data) {
                        business_id = Some(id_str);
                    }
                }
                &quot;desc&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(desc_str) = String::from_utf8(data) {
                        description = Some(desc_str);
                    }
                }
                _ =&gt; {}
            }
        }
    }

    // 验证必要参数
    let image_data = match image_data {
        Some(data) if !data.is_empty() =&gt; data,
        _ =&gt; {
            return Ok(HttpResponse::BadRequest().json(UploadResponse {
                network: false,
                msg: &quot;上传文件为空&quot;.to_string(),
                file_id: None,
                file_name: None,
            }));
        }
    };

    let business_id = business_id.unwrap_or_else(|| Uuid::new_v4().to_string());
    let image_count = image_count.unwrap_or(1);

    // 生成文件名
    let timestamp = chrono::Utc::now().timestamp_millis();
    let file_name = format!(&quot;{}_{}.pdf&quot;, business_id, timestamp);
    let upload_dir = &quot;./uploads/scan&quot;;

    // 创建目录
    if let Err(e) = fs::create_dir_all(upload_dir).await {
        return Ok(HttpResponse::InternalServerError().json(UploadResponse {
            network: false,
            msg: format!(&quot;创建目录失败: {}&quot;, e),
            file_id: None,
            file_name: None,
        }));
    }

    // 保存文件
    let file_path = format!(&quot;{}/{}&quot;, upload_dir, file_name);
    if let Err(e) = fs::write(&amp;file_path, &amp;image_data).await {
        return Ok(HttpResponse::InternalServerError().json(UploadResponse {
            network: false,
            msg: format!(&quot;保存文件失败: {}&quot;, e),
            file_id: None,
            file_name: None,
        }));
    }

    // 保存到数据库
    let document = ScanDocument {
        id: None,
        business_id: business_id.clone(),
        description,
        image_count,
        file_path: file_path.clone(),
        file_name: file_name.clone(),
        file_size: image_data.len() as u64,
        upload_time: chrono::Utc::now(),
    };

    // 这里应该调用数据库服务保存记录
    // let saved_document = db_service.save(document).await?;

    // 返回成功响应
    Ok(HttpResponse::Ok().json(UploadResponse {
        network: true,
        msg: &quot;上传成功&quot;.to_string(),
        file_id: Some(1), // document.id
        file_name: Some(file_name),
    }))
}

#[actix_web::main]
async fn main() -&gt; std::io::Result&lt;()&gt; {
    HttpServer::new(|| {
        App::new()
            .route(&quot;/api/upload&quot;, web::post().to(upload_scan_images))
    })
    .bind(&quot;127.0.0.1:8080&quot;)?
    .run()
    .await
}</pre></div></div><div class="frontend-examples" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>前端JavaScript上传示例</h4><p class="text-gray-600 mb-4" data-v-d2fddfc7> 第二种上传方式：前端通过WebSocket获取图像数据后，使用JavaScript进行上传 </p><div class="frontend-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7> 获取图像数据并上传（Base64方式） </h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 获取扫描图像的Base64数据
scanonweb.onGetAllImageEvent = function(msg) {
    console.log(&#39;获取到图像数据:&#39;, msg.images);

    // 上传所有图像
    uploadImagesToServer(msg.images, &#39;DOC_001&#39;, &#39;扫描文档&#39;);
};

// 上传图像到服务器
async function uploadImagesToServer(images, businessId, description) {
    try {
        for (let i = 0; i &lt; images.length; i++) {
            const base64Data = images[i];

            // 将Base64转换为Blob
            const blob = base64ToBlob(base64Data, &#39;image/jpeg&#39;);

            // 创建FormData
            const formData = new FormData();
            formData.append(&#39;image&#39;, blob, \`scan_\${businessId}_\${i}.jpg\`);
            formData.append(&#39;imageCount&#39;, images.length.toString());
            formData.append(&#39;id&#39;, businessId);
            formData.append(&#39;desc&#39;, description);
            formData.append(&#39;imageIndex&#39;, i.toString());

            // 使用axios上传
            const response = await axios.post(&#39;/api/upload-image&#39;, formData, {
                headers: {
                    &#39;Content-Type&#39;: &#39;multipart/form-data&#39;
                },
                onUploadProgress: (progressEvent) =&gt; {
                    const progress = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    console.log(\`图像 \${i + 1} 上传进度: \${progress}%\`);
                }
            });

            console.log(\`图像 \${i + 1} 上传成功:\`, response.data);
        }

        alert(&#39;所有图像上传完成！&#39;);

    } catch (error) {
        console.error(&#39;上传失败:&#39;, error);
        alert(&#39;上传失败: &#39; + error.message);
    }
}

// Base64转Blob工具函数
function base64ToBlob(base64Data, contentType = &#39;&#39;) {
    const byteCharacters = atob(base64Data);
    const byteArrays = [];

    for (let offset = 0; offset &lt; byteCharacters.length; offset += 512) {
        const slice = byteCharacters.slice(offset, offset + 512);
        const byteNumbers = new Array(slice.length);

        for (let i = 0; i &lt; slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
}

// 获取图像数据
scanonweb.getAllImage();</pre></div></div><div class="frontend-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>使用Fetch API上传</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 使用fetch API上传图像
async function uploadImageWithFetch(base64Data, businessId, description, index) {
    try {
        // 将Base64转换为二进制数据
        const binaryData = atob(base64Data);
        const bytes = new Uint8Array(binaryData.length);

        for (let i = 0; i &lt; binaryData.length; i++) {
            bytes[i] = binaryData.charCodeAt(i);
        }

        // 创建FormData
        const formData = new FormData();
        const blob = new Blob([bytes], { type: &#39;image/jpeg&#39; });
        formData.append(&#39;image&#39;, blob, \`scan_\${businessId}_\${index}.jpg\`);
        formData.append(&#39;imageCount&#39;, &#39;1&#39;);
        formData.append(&#39;id&#39;, businessId);
        formData.append(&#39;desc&#39;, description);

        // 发送请求
        const response = await fetch(&#39;/api/upload-image&#39;, {
            method: &#39;POST&#39;,
            body: formData
        });

        if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
        }

        const result = await response.json();

        if (result.network) {
            console.log(&#39;上传成功:&#39;, result.msg);
            return result;
        } else {
            throw new Error(result.msg);
        }

    } catch (error) {
        console.error(&#39;上传失败:&#39;, error);
        throw error;
    }
}

// 批量上传示例
async function batchUploadImages() {
    // 先获取图像数据
    scanonweb.onGetAllImageEvent = async function(msg) {
        const images = msg.images;
        const businessId = &#39;BATCH_&#39; + Date.now();

        for (let i = 0; i &lt; images.length; i++) {
            try {
                await uploadImageWithFetch(
                    images[i],
                    businessId,
                    \`批量扫描文档 \${i + 1}\`,
                    i
                );
                console.log(\`第 \${i + 1} 张图像上传完成\`);
            } catch (error) {
                console.error(\`第 \${i + 1} 张图像上传失败:\`, error);
            }
        }
    };

    // 获取所有图像
    scanonweb.getAllImage();
}</pre></div></div></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>saveAllImageToLocal(filename)</h3><p class="api-description" data-v-d2fddfc7> 将所有图像保存到客户端本地文件。支持多种格式：PDF、TIFF、JPG等。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>filename</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>保存的文件路径和名称</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 保存为PDF文件
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.pdf&#39;);

// 保存为TIFF文件
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.tiff&#39;);

// 保存为JPG文件（仅第一张图像）
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.jpg&#39;);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> uploadJpgImageByIndex(url, id, desc, index) </h3><p class="api-description" data-v-d2fddfc7> 上传指定索引的单张图像（JPG格式）到服务器。 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>参数</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>参数名</th><th data-v-d2fddfc7>类型</th><th data-v-d2fddfc7>必填</th><th data-v-d2fddfc7>说明</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>上传目标URL地址</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>id</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像标识ID</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>desc</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>否</td><td data-v-d2fddfc7>图像描述信息</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>是</td><td data-v-d2fddfc7>图像索引，从0开始</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 上传第一张图像
scanonweb.uploadJpgImageByIndex(
    &#39;https://api.example.com/upload-image&#39;,
    &#39;IMG_001&#39;,
    &#39;身份证正面&#39;,
    0
);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>openClientLocalfile()</h3><p class="api-description" data-v-d2fddfc7> 打开客户端文件选择对话框，允许用户选择本地图像文件加载到控件中。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 打开文件选择对话框
scanonweb.openClientLocalfile();

// 用户选择文件后，会自动加载到控件中
// 可以通过getAllImage()获取加载的图像</pre></div></div></div></div><div class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>界面控制方法</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>setFocus()</h3><p class="api-description" data-v-d2fddfc7> 设置扫描控件界面获得焦点，将控件窗口置于前台。 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 将扫描控件窗口置于前台
scanonweb.setFocus();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>hidden()</h3><p class="api-description" data-v-d2fddfc7>隐藏扫描控件界面窗口。</p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>示例代码</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// 隐藏扫描控件界面
scanonweb.hidden();</pre></div></div></div></div></div>`,2),I={__name:"scanonweb-api",setup(a){return f({title:"ScanOnWeb API文档 - 完整JavaScript API参考",meta:[{name:"description",content:"ScanOnWeb扫描控件完整API文档，包含所有JavaScript方法、属性、事件回调的详细说明和代码示例。"},{name:"keywords",content:"ScanOnWeb,API文档,JavaScript,扫描控件,方法参考,事件回调,参数说明"}]}),(k,D)=>{const n=o;return m(),v("div",h,[t(i),d("main",null,[d("div",b,[d("div",q,[d("nav",x,[t(n,{to:"/",class:"hover:text-orange-500"},{default:c(()=>[s("首页")]),_:1}),_,t(n,{to:"/documents",class:"hover:text-orange-500"},{default:c(()=>[s("文档资料")]),_:1}),w,y])])]),S]),t(l)])}}},N=r(I,[["__scopeId","data-v-d2fddfc7"]]);export{N as default};
