/**
 * ScanOnWeb 控件JavaScript API
 * 这是一个占位文件，在实际使用时需要替换为真实的控件JS文件
 * 下载地址：https://www.brainysoft.cn/download/ScanOnWebH5Install.exe
 */

class ScanOnWeb {
  constructor() {
    console.log("ScanOnWeb 控件初始化");
    this.scaner_work_config = {
      dpi_x: 300,
      dpi_y: 300,
      deviceIndex: 0,
      colorMode: "RGB",
      showDialog: "false",
      autoFeedEnable: "false",
      autoFeed: "false",
      dupxMode: "false",
      autoDeskew: "false",
      autoBorderDetection: "false",
    };

    // 模拟图像数量
    this.imageCount = 0;
  }

  // 加载扫描设备列表
  loadDevices() {
    console.log("加载扫描设备列表");
    // 模拟设备列表数据
    const mockDevices = {
      devices: ["模拟扫描仪 1", "模拟扫描仪 2"],
      currentIndex: 0,
    };

    // 触发回调
    if (typeof this.onGetDevicesListEvent === "function") {
      this.onGetDevicesListEvent(mockDevices);
    }
  }

  // 开始扫描
  startScan() {
    console.log("开始扫描，使用以下配置：", this.scaner_work_config);
    // 模拟扫描过程
    setTimeout(() => {
      // 模拟扫描完成后增加图像
      const imageBeforeCount = this.imageCount;
      this.imageCount++;

      // 触发获取图像事件
      if (typeof this.onGetImageByIdEvent === "function") {
        this.onGetImageByIdEvent({
          imageIndex: this.imageCount - 1,
          imageCount: this.imageCount,
          // 使用占位图像
          imageBase64:
            "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAACKElEQVR4nO3dMU4CQRSA4X92Y2JhYW1l50W8gJfQK3gDKxsO4B1svIGlFZ3xAMY0VhRmRmfevP1/FQWb7MuXYYGEeE4IIYQQQgghhBBCCCGEEEIIIYTQNzl1Ayv1OdTdEJl7drz8zB00ddA9pQ66p7QB9xRfXoAQUQhxhYgziBBnkLGLs9+xizu/QYSIQogrRJxBhDiDjF2c/Y5d3PkNIkQUIq4QcQYR4gwyFCRnu5gSl+tDlLhcH6LE5foQJS7XhyhxuT5Eicv1IUpcrl8WW8qrCrGpEGm/72dPh0jzu5+fOkCa35dIBPsQIS5AiAsQ4gKEuAAhLkCICxDiAoS4ACEuQIgLEOIChLgAIS5AiAsQ4gKEuAAhLkCICxDiAoS4ACEuQIgLEOIChLgAIS5AiAsQ4gKEuFJ9Uqv0yauLRaT51xVS+uTVVQrS/OsKKX0iK3UQaSHLkaNCVCHLkaNCVCHLkaNCVCHLkaNCpJWcxDq5kJQ3QoUIK3kCK3YhKW+EChFW8gRW7EKaP4kVu5AtrxBCCHlHbKP1gdhG6wOxjaZCbKOpENtoKsQ2mnFjG003VuHG3o3tfC32bqzibnQVtyOouB1BxaqQVRzcVRzcVRzc1xn6j9oO/kdtB/+1QYUIEYWIKyRnO5sKydnOptZZtRtEiChEXCHiDCLEGWTs4ux37OLObxAhohBxhYgziJAGQsZDVhq0/AJAe/+hDuKXsQAAAABJRU5ErkJggg==",
        });
      }

      // 触发扫描完成事件
      if (typeof this.onScanFinishedEvent === "function") {
        this.onScanFinishedEvent({
          imageBeforeCount: imageBeforeCount,
          imageAfterCount: this.imageCount,
        });
      }
    }, 1000);
  }

  // 清空所有图像
  clearAll() {
    console.log("清空所有图像");
    this.imageCount = 0;
  }

  // 获取所有图像
  getAllImage() {
    console.log("获取所有图像");

    // 模拟图像数据
    const mockImages = [];
    for (let i = 0; i < this.imageCount; i++) {
      mockImages.push(
        "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAACKElEQVR4nO3dMU4CQRSA4X92Y2JhYW1l50W8gJfQK3gDKxsO4B1svIGlFZ3xAMY0VhRmRmfevP1/FQWb7MuXYYGEeE4IIYQQQgghhBBCCCGEEEIIIYTQNzl1Ayv1OdTdEJl7drz8zB00ddA9pQ66p7QB9xRfXoAQUQhxhYgziBBnkLGLs9+xizu/QYSIQogrRJxBhDiDjF2c/Y5d3PkNIkQUIq4QcQYR4gwyFCRnu5gSl+tDlLhcH6LE5foQJS7XhyhxuT5Eicv1IUpcrl8WW8qrCrGpEGm/72dPh0jzu5+fOkCa35dIBPsQIS5AiAsQ4gKEuAAhLkCICxDiAoS4ACEuQIgLEOIChLgAIS5AiAsQ4gKEuAAhLkCICxDiAoS4ACEuQIgLEOIChLgAIS5AiAsQ4gKEuFJ9Uqv0yauLRaT51xVS+uTVVQrS/OsKKX0iK3UQaSHLkaNCVCHLkaNCVCHLkaNCVCHLkaNCpJWcxDq5kJQ3QoUIK3kCK3YhKW+EChFW8gRW7EKaP4kVu5AtrxBCCHlHbKP1gdhG6wOxjaZCbKOpENtoKsQ2mnFjG003VuHG3o3tfC32bqzibnQVtyOouB1BxaqQVRzcVRzcVRzc1xn6j9oO/kdtB/+1QYUIEYWIKyRnO5sKydnOptZZtRtEiChEXCHiDCLEGWTs4ux37OLObxAhohBxhYgziJAGQsZDVhq0/AJAe/+hDuKXsQAAAABJRU5ErkJggg=="
      );
    }

    // 触发回调
    if (typeof this.onGetAllImageEvent === "function") {
      this.onGetAllImageEvent({
        images: mockImages,
        imageCount: this.imageCount,
        currentSelected: 0,
      });
    }
  }

  // 上传所有图像为PDF
  uploadAllImageAsPdfToUrl(url, id, desc) {
    console.log("上传PDF，URL:", url, "ID:", id, "描述:", desc);

    // 模拟上传结果
    setTimeout(() => {
      if (typeof this.onUploadAllImageAsPdfToUrlEvent === "function") {
        this.onUploadAllImageAsPdfToUrlEvent({
          uploadResult: JSON.stringify({
            network: true,
            msg: "上传成功",
          }),
        });
      }
    }, 1500);
  }

  // 本地保存
  saveAllImageToLocal(fileName) {
    console.log("保存文件到本地:", fileName);
    // 模拟文件保存
    return true;
  }

  // 设置焦点
  setFocus() {
    console.log("设置扫描控件界面焦点");
  }

  // 隐藏界面
  hidden() {
    console.log("隐藏扫描控件界面");
  }

  // 设置授权信息
  setLicenseKey(licenseMode, licenseKey1, licenseKey2, checkLicenseUrl) {
    console.log(
      "设置授权信息:",
      licenseMode,
      licenseKey1,
      licenseKey2,
      checkLicenseUrl
    );
  }
}
