<template>
  <div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-2xl font-bold">文档资料</h2>
    </div>
    <div>
      <div v-for="(category, index) in categories" :key="index">
        <div class="bg-gray-100 p-3 font-semibold border-b">
          {{ category.name }}
        </div>
        <ul>
          <li
            v-for="doc in category.documents"
            :key="doc.id"
            class="border-b last:border-b-0"
          >
            <button
              @click="$emit('select', doc)"
              class="w-full text-left p-4 flex items-center transition-colors"
              :class="{
                'bg-blue-100': doc.id === selectedDocId,
                'hover:bg-gray-100': doc.id !== selectedDocId,
              }"
            >
              <!-- 图标区域 -->
              <div
                class="w-10 h-10 rounded-md mr-4 flex items-center justify-center text-white"
                :class="getIconClass(doc.type)"
              >
                <svg
                  v-if="doc.type === 'pdf'"
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  ></path>
                </svg>
                <svg
                  v-else-if="doc.type === 'video'"
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  ></path>
                </svg>
                <svg
                  v-else-if="doc.type === 'code'"
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                  ></path>
                </svg>
                <svg
                  v-else
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
              </div>

              <!-- 文本内容区域 -->
              <div>
                <p class="font-semibold text-gray-800">{{ doc.title }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ doc.description }}</p>
              </div>
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

defineProps({
  categories: Array,
  selectedDocId: Number,
});

defineEmits(["select"]);

const getIconClass = (type) => {
  switch (type) {
    case "pdf":
      return "bg-gradient-to-r from-red-500 to-pink-600";
    case "video":
      return "bg-gradient-to-r from-blue-500 to-indigo-600";
    case "code":
      return "bg-gradient-to-r from-green-500 to-teal-600";
    default:
      return "bg-gradient-to-r from-gray-500 to-gray-600";
  }
};
</script>
