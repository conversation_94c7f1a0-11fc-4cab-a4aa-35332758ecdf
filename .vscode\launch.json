{"version": "0.2.0", "configurations": [{"name": "Nuxt: Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/website/node_modules/nuxt/bin/nuxt.mjs", "args": ["dev"], "cwd": "${workspaceFolder}/website", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "Nuxt: Debug Client", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/website", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*", "webpack:///src/*": "${webRoot}/*", "webpack:///*": "*", "webpack:///./~/*": "${webRoot}/node_modules/*"}}, {"name": "Nuxt: Debug Full Stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/website/node_modules/nuxt/bin/nuxt.mjs", "args": ["dev"], "cwd": "${workspaceFolder}/website", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "serverReadyAction": {"pattern": "Local:.*?(https?://\\S+)", "uriFormat": "%s", "action": "debugWithChrome"}}]}