import { ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { _ as _export_sfc } from './server.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';

const _sfc_main = {
  __name: "ScannerDemo",
  __ssrInlineRender: true,
  setup(__props) {
    const dpiX = ref("300");
    const dpiY = ref("300");
    const colorMode = ref("RGB");
    const showDialog = ref("false");
    const feedEnable = ref("false");
    const autoFeed = ref("false");
    const dupxMode = ref("false");
    const autoDeskew = ref("false");
    const autoBorderDetection = ref("false");
    const uploadUrl = ref("http://localhost:44300/api/ImageUpload/upload");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "scanner-demo" }, _attrs))} data-v-1b4af074><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-1b4af074><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-1b4af074> \u56FE\u50CF\u626B\u63CF\u7BA1\u7406\u7CFB\u7EDF </h2><div class="mb-8" data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>\u626B\u63CF\u8BBE\u7F6E</h3><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-1b4af074><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u626B\u63CF\u8BBE\u5907</label><select id="devices" class="w-full p-2 border rounded" data-v-1b4af074></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u5206\u8FA8\u7387</label><div class="flex items-center" data-v-1b4af074><input type="text" id="dpi_x"${ssrRenderAttr("value", dpiX.value)} class="w-16 p-2 border rounded" data-v-1b4af074><span class="mx-2" data-v-1b4af074>X</span><input type="text" id="dpi_y"${ssrRenderAttr("value", dpiY.value)} class="w-16 p-2 border rounded" data-v-1b4af074></div></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u8272\u5F69\u6A21\u5F0F</label><select id="colorMode" class="w-full p-2 border rounded" data-v-1b4af074><option value="RGB" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "RGB") : ssrLooseEqual(colorMode.value, "RGB")) ? " selected" : ""}>\u5F69\u8272</option><option value="GRAY" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "GRAY") : ssrLooseEqual(colorMode.value, "GRAY")) ? " selected" : ""}>\u7070\u8272</option><option value="BW" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "BW") : ssrLooseEqual(colorMode.value, "BW")) ? " selected" : ""}>\u9ED1\u767D</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u663E\u793A\u8BBE\u5907\u5BF9\u8BDD\u6846</label><select id="showDialog" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(showDialog.value) ? ssrLooseContain(showDialog.value, "true") : ssrLooseEqual(showDialog.value, "true")) ? " selected" : ""}>\u663E\u793A</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(showDialog.value) ? ssrLooseContain(showDialog.value, "false") : ssrLooseEqual(showDialog.value, "false")) ? " selected" : ""}>\u4E0D\u663E\u793A</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u81EA\u52A8\u8FDB\u7EB8\u6A21\u5F0F</label><select id="feedEnable" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(feedEnable.value) ? ssrLooseContain(feedEnable.value, "true") : ssrLooseEqual(feedEnable.value, "true")) ? " selected" : ""}>\u662F</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(feedEnable.value) ? ssrLooseContain(feedEnable.value, "false") : ssrLooseEqual(feedEnable.value, "false")) ? " selected" : ""}>\u5426</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u81EA\u52A8\u88C5\u586B\u7EB8\u5F20</label><select id="autoFeed" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoFeed.value) ? ssrLooseContain(autoFeed.value, "true") : ssrLooseEqual(autoFeed.value, "true")) ? " selected" : ""}>\u662F</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoFeed.value) ? ssrLooseContain(autoFeed.value, "false") : ssrLooseEqual(autoFeed.value, "false")) ? " selected" : ""}>\u5426</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u53CC\u9762\u6A21\u5F0F</label><select id="dupxMode" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(dupxMode.value) ? ssrLooseContain(dupxMode.value, "true") : ssrLooseEqual(dupxMode.value, "true")) ? " selected" : ""}>\u662F</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(dupxMode.value) ? ssrLooseContain(dupxMode.value, "false") : ssrLooseEqual(dupxMode.value, "false")) ? " selected" : ""}>\u5426</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u81EA\u52A8\u7EA0\u504F</label><select id="autoDeskew" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoDeskew.value) ? ssrLooseContain(autoDeskew.value, "true") : ssrLooseEqual(autoDeskew.value, "true")) ? " selected" : ""}>\u662F</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoDeskew.value) ? ssrLooseContain(autoDeskew.value, "false") : ssrLooseEqual(autoDeskew.value, "false")) ? " selected" : ""}>\u5426</option></select></div><div class="form-control" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u81EA\u52A8\u8FB9\u6846\u68C0\u6D4B</label><select id="autoBorderDetection" class="w-full p-2 border rounded" data-v-1b4af074><option value="true" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoBorderDetection.value) ? ssrLooseContain(autoBorderDetection.value, "true") : ssrLooseEqual(autoBorderDetection.value, "true")) ? " selected" : ""}>\u662F</option><option value="false" data-v-1b4af074${ssrIncludeBooleanAttr(Array.isArray(autoBorderDetection.value) ? ssrLooseContain(autoBorderDetection.value, "false") : ssrLooseEqual(autoBorderDetection.value, "false")) ? " selected" : ""}>\u5426</option></select></div></div><div class="flex flex-wrap gap-2 mt-6" data-v-1b4af074><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-1b4af074> \u83B7\u53D6\u8BBE\u5907\u5217\u8868 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> \u5F00\u59CB\u626B\u63CF </button><button class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors" data-v-1b4af074> \u6E05\u7A7A\u626B\u63CF\u7ED3\u679C </button><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-1b4af074> \u83B7\u53D6\u6240\u6709\u56FE\u50CF </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> \u663E\u793A\u754C\u9762 </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> \u9690\u85CF\u754C\u9762 </button><button class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" data-v-1b4af074> \u672C\u5730\u53E6\u5B58 </button></div></div><div class="mb-8" data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>\u4E0A\u4F20\u8BBE\u7F6E</h3><div class="bg-gray-100 p-4 rounded" data-v-1b4af074><div class="mb-4" data-v-1b4af074><label class="font-medium mb-1 block" data-v-1b4af074>\u4E0A\u4F20\u5730\u5740</label><input type="text" id="uploadUrl"${ssrRenderAttr("value", uploadUrl.value)} class="w-full p-2 border rounded" placeholder="\u8BF7\u8F93\u5165\u670D\u52A1\u5668\u4E0A\u4F20\u5730\u5740" data-v-1b4af074></div><div class="flex flex-wrap gap-2" data-v-1b4af074><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> PDF\u683C\u5F0F\u4E0A\u4F20 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-1b4af074> DOM\u56FE\u50CF\u4E0A\u4F20 </button></div></div></div><div data-v-1b4af074><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-1b4af074>\u626B\u63CF\u7ED3\u679C</h3><div id="imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-1b4af074></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-1b4af074><p class="font-bold" data-v-1b4af074> \u6CE8\u610F\uFF1A\u672C\u6F14\u793A\u9700\u8981\u4E0B\u8F7D\u5B89\u88C5\u6258\u76D8\u626B\u63CF\u670D\u52A1\u624D\u53EF\u6B63\u5E38\u5DE5\u4F5C\uFF0C\u4E0B\u8F7D\u94FE\u63A5\u5728\u4E0B\u9762\u3002 </p></div><div class="mt-6 flex justify-center gap-4 border-t pt-4" data-v-1b4af074><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> \u626B\u63CF\u670D\u52A1\u6258\u76D8\u7A0B\u5E8F\u4E0B\u8F7D </a><a href="https://www.brainysoft.cn/video/scanh5.mp4" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> \u89C6\u9891\u6559\u7A0B </a><a href="https://www.brainysoft.cn" target="_blank" class="text-blue-600 hover:underline" data-v-1b4af074> \u5B98\u65B9\u7F51\u7AD9 </a></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/ScannerDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ScannerDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-1b4af074"]]);

export { ScannerDemo as default };
//# sourceMappingURL=ScannerDemo-CJ7uu1tH.mjs.map
