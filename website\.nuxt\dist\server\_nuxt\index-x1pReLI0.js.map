{"version": 3, "file": "index-x1pReLI0.js", "sources": ["../../../../components/Carousel.vue", "../../../../components/DownloadSection.vue", "../../../../components/ProductFeatures.vue"], "sourcesContent": ["<template>\r\n  <div class=\"relative h-[500px] overflow-hidden\"> <!-- 调整高度 -->\r\n    <div v-for=\"(slide, index) in slides\" :key=\"index\" \r\n         class=\"absolute top-0 left-0 w-full h-full transition-opacity duration-500 ease-in-out\"\r\n         :class=\"{ 'opacity-100': currentSlide === index, 'opacity-0': currentSlide !== index }\">\r\n      <img :src=\"slide.image\" :alt=\"slide.alt\" class=\"w-full h-full object-cover\">\r\n      <div class=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-6\"> <!-- 增加内边距 -->\r\n        <h2 class=\"text-3xl font-bold mb-2\">{{ slide.title }}</h2> <!-- 增加字体大小 -->\r\n        <p class=\"text-lg\">{{ slide.description }}</p> <!-- 增加字体大小 -->\r\n      </div>\r\n    </div>\r\n    <button @click=\"prevSlide\" class=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full\">\r\n      &#8249; <!-- 左箭头 -->\r\n    </button>\r\n    <button @click=\"nextSlide\" class=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full\">\r\n      &#8250; <!-- 右箭头 -->\r\n    </button>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from 'vue';\r\n\r\nconst slides = [\r\n  { image: '/images/slide1.png', alt: 'Slide 1', title: '跨平台扫描插件解决方案', description: '为您的业务提供最先进的技术' },\r\n  { image: '/images/slide2.png', alt: 'Slide 2', title: '专业团队', description: '经验丰富的开发者为您服务' },\r\n  { image: '/images/slide3.png', alt: 'Slide 3', title: '客户满意度', description: '我们以客户的成功为己任' },\r\n];\r\n\r\nconst currentSlide = ref(0);\r\nconst timer = ref(null);\r\n\r\nconst nextSlide = () => {\r\n  currentSlide.value = (currentSlide.value + 1) % slides.length;\r\n};\r\n\r\nconst prevSlide = () => {\r\n  currentSlide.value = (currentSlide.value - 1 + slides.length) % slides.length;\r\n};\r\n\r\nonMounted(() => {\r\n  timer.value = setInterval(nextSlide, 5000);\r\n});\r\n\r\nonUnmounted(() => {\r\n  clearInterval(timer.value);\r\n});\r\n</script>", "<template>\n  <section class=\"py-20 bg-business-gradient\">\n    <div class=\"container mx-auto px-4\">\n      <div class=\"text-center mb-16\">\n        <h2 class=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n          专业Web控件解决方案\n        </h2>\n        <p class=\"text-xl text-blue-100 mb-4 max-w-4xl mx-auto\">\n          为企业级应用提供稳定可靠的扫描仪、摄像头、高拍仪控件，支持Windows和Linux多平台\n        </p>\n        <div\n          class=\"flex justify-center items-center space-x-8 text-blue-100 text-sm\"\n        >\n          <div class=\"flex items-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>10年+技术积累</span>\n          </div>\n          <div class=\"flex items-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>1000+企业客户</span>\n          </div>\n          <div class=\"flex items-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>信创认证产品</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n        <div\n          v-for=\"product in products\"\n          :key=\"product.id\"\n          class=\"bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300\"\n        >\n          <div class=\"flex items-center mb-4\">\n            <div\n              class=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4\"\n            >\n              <svg\n                class=\"w-6 h-6 text-orange-500\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  stroke-width=\"2\"\n                  d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                ></path>\n              </svg>\n            </div>\n            <div>\n              <h3 class=\"text-xl font-bold text-gray-900\">\n                {{ product.name }}\n              </h3>\n              <p class=\"text-sm text-gray-500\">{{ product.version }}</p>\n            </div>\n          </div>\n          <p class=\"text-gray-600 mb-4\">{{ product.description }}</p>\n          <div class=\"space-y-2\">\n            <div class=\"flex items-center text-sm text-gray-500\">\n              <svg\n                class=\"w-4 h-4 mr-2\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  stroke-width=\"2\"\n                  d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                ></path>\n              </svg>\n              支持平台：{{ product.platforms }}\n            </div>\n            <div class=\"flex items-center text-sm text-gray-500\">\n              <svg\n                class=\"w-4 h-4 mr-2\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  stroke-width=\"2\"\n                  d=\"M13 10V3L4 14h7v7l9-11h-7z\"\n                ></path>\n              </svg>\n              企业级稳定性\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"text-center\">\n        <div\n          class=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\"\n        >\n          <NuxtLink\n            to=\"/download\"\n            class=\"bg-orange-500 hover:bg-orange-600 text-white px-10 py-4 rounded-lg text-lg font-bold transition-all duration-300 shadow-lg flex items-center\"\n          >\n            <svg\n              class=\"w-5 h-5 mr-2\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              ></path>\n            </svg>\n            免费下载试用\n          </NuxtLink>\n\n          <NuxtLink\n            to=\"/products\"\n            class=\"bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 px-10 py-4 rounded-lg text-lg font-bold transition-all duration-300\"\n          >\n            查看产品详情\n          </NuxtLink>\n        </div>\n\n        <div\n          class=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-blue-100 text-sm\"\n        >\n          <div class=\"flex items-center justify-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>无需注册即可下载试用</span>\n          </div>\n          <div class=\"flex items-center justify-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>支持多种CPU架构</span>\n          </div>\n          <div class=\"flex items-center justify-center\">\n            <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path\n                fill-rule=\"evenodd\"\n                d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                clip-rule=\"evenodd\"\n              ></path>\n            </svg>\n            <span>7×24小时技术支持</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n</template>\n\n<script setup>\nconst products = [\n  {\n    id: 1,\n    name: \"ScanOnWeb\",\n    description: \"专业扫描仪控件\",\n    platforms: \"Windows / Linux\",\n    version: \"v3.5.0\",\n  },\n  {\n    id: 2,\n    name: \"ImageCapOnWeb\",\n    description: \"摄像头图像采集控件\",\n    platforms: \"Windows\",\n    version: \"v2.8.0\",\n  },\n  {\n    id: 3,\n    name: \"GaoPaiYi\",\n    description: \"高拍仪控件\",\n    platforms: \"Windows\",\n    version: \"v2.0.1\",\n  },\n];\n</script>\n", "<template>\r\n  <section class=\"py-20 bg-gray-50\">\r\n    <div class=\"container mx-auto px-4\">\r\n      <div class=\"text-center mb-16\">\r\n        <h2 class=\"heading-primary mb-6\">为什么选择我们</h2>\r\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n          专注Web控件技术10年，为1000+企业客户提供稳定可靠的解决方案\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n        <div\r\n          v-for=\"feature in features\"\r\n          :key=\"feature.title\"\r\n          class=\"card-business p-8 text-center group\"\r\n        >\r\n          <div\r\n            class=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-200 transition-colors duration-200\"\r\n          >\r\n            <component :is=\"feature.icon\" class=\"w-8 h-8 text-orange-500\" />\r\n          </div>\r\n          <h3 class=\"heading-tertiary mb-4\">{{ feature.title }}</h3>\r\n          <p class=\"text-business\">{{ feature.description }}</p>\r\n\r\n          <!-- 添加数据支撑 -->\r\n          <div v-if=\"feature.stats\" class=\"mt-6 pt-6 border-t border-gray-100\">\r\n            <div class=\"flex justify-center space-x-6\">\r\n              <div\r\n                v-for=\"stat in feature.stats\"\r\n                :key=\"stat.label\"\r\n                class=\"text-center\"\r\n              >\r\n                <div class=\"text-2xl font-bold text-orange-500\">\r\n                  {{ stat.value }}\r\n                </div>\r\n                <div class=\"text-sm text-gray-500\">{{ stat.label }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 客户案例数据 -->\r\n      <div class=\"mt-20 bg-white rounded-xl p-8 shadow-sm\">\r\n        <div class=\"text-center mb-12\">\r\n          <h3 class=\"heading-secondary mb-4\">客户遍布各行各业</h3>\r\n          <p class=\"text-business\">服务税务、公安、银行、教育等多个重要行业</p>\r\n        </div>\r\n\r\n        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\r\n          <div\r\n            v-for=\"industry in industries\"\r\n            :key=\"industry.name\"\r\n            class=\"text-center\"\r\n          >\r\n            <div\r\n              class=\"w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n            >\r\n              <component :is=\"industry.icon\" class=\"w-10 h-10 text-blue-600\" />\r\n            </div>\r\n            <h4 class=\"font-semibold text-gray-800 mb-2\">\r\n              {{ industry.name }}\r\n            </h4>\r\n            <p class=\"text-sm text-gray-500\">{{ industry.count }}+ 客户</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 典型用户列表 -->\r\n        <div class=\"mt-16 pt-12 border-t border-gray-200\">\r\n          <div class=\"text-center mb-12\">\r\n            <h3 class=\"heading-secondary mb-4\">典型用户</h3>\r\n            <p class=\"text-business\">部分合作伙伴和客户（以下排名不分先后）</p>\r\n          </div>\r\n\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            <div\r\n              v-for=\"client in typicalClients\"\r\n              :key=\"client.name\"\r\n              class=\"bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors duration-200\"\r\n            >\r\n              <div\r\n                class=\"w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\"\r\n              >\r\n                <component :is=\"client.icon\" class=\"w-8 h-8 text-gray-600\" />\r\n              </div>\r\n              <h4 class=\"font-semibold text-gray-900 text-sm leading-relaxed\">\r\n                {{ client.name }}\r\n              </h4>\r\n              <p class=\"text-xs text-gray-500 mt-2\">{{ client.category }}</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"text-center mt-8\">\r\n            <p class=\"text-sm text-gray-500\">\r\n              感谢所有合作伙伴的信任与支持，我们将持续为您提供优质的产品和服务\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script setup>\r\nimport { h } from \"vue\";\r\n\r\n// 图标组件\r\nconst PlatformIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\",\r\n    }),\r\n  ]);\r\n\r\nconst EaseIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\",\r\n    }),\r\n  ]);\r\n\r\nconst ScaleIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4\",\r\n    }),\r\n  ]);\r\n\r\nconst SecurityIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\",\r\n    }),\r\n  ]);\r\n\r\nconst SupportIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944l7.071 7.071-7.071 7.071-7.071-7.071L12 2.944z\",\r\n    }),\r\n  ]);\r\n\r\nconst CustomIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\",\r\n    }),\r\n  ]);\r\n\r\n// 行业图标\r\nconst TaxIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\",\r\n    }),\r\n  ]);\r\n\r\nconst PoliceIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\",\r\n    }),\r\n  ]);\r\n\r\nconst BankIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\",\r\n    }),\r\n  ]);\r\n\r\nconst EduIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M12 14l9-5-9-5-9 5 9 5z\",\r\n    }),\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\",\r\n    }),\r\n  ]);\r\n\r\nconst features = [\r\n  {\r\n    title: \"跨平台兼容\",\r\n    description:\r\n      \"国产软件厂商，产品全面支持Windows和信创国产化软硬件应用环境，通过多项认证。\",\r\n    icon: PlatformIcon,\r\n    stats: [\r\n      { value: \"100%\", label: \"兼容性\" },\r\n      { value: \"10+\", label: \"平台支持\" },\r\n    ],\r\n  },\r\n  {\r\n    title: \"简单易用\",\r\n    description:\r\n      \"直观的API设计和完善的文档，让开发者可以快速集成，大幅降低开发成本。\",\r\n    icon: EaseIcon,\r\n    stats: [\r\n      { value: \"30分钟\", label: \"快速集成\" },\r\n      { value: \"99%\", label: \"客户满意度\" },\r\n    ],\r\n  },\r\n  {\r\n    title: \"高度可扩展\",\r\n    description:\r\n      \"灵活的架构设计，支持二次开发和定制，可根据业务需求进行功能扩展。\",\r\n    icon: ScaleIcon,\r\n  },\r\n  {\r\n    title: \"安全可靠\",\r\n    description:\r\n      \"产品历经10年研发应用，在1000+企业客户中稳定运行，经受了实际生产环境考验。\",\r\n    icon: SecurityIcon,\r\n    stats: [\r\n      { value: \"99.9%\", label: \"稳定性\" },\r\n      { value: \"0\", label: \"安全事故\" },\r\n    ],\r\n  },\r\n  {\r\n    title: \"专业支持\",\r\n    description: \"7×24小时技术支持团队，提供远程协助、现场服务等多种支持方式。\",\r\n    icon: SupportIcon,\r\n  },\r\n  {\r\n    title: \"定制服务\",\r\n    description: \"根据客户具体需求，提供专业的定制开发服务和行业解决方案。\",\r\n    icon: CustomIcon,\r\n  },\r\n];\r\n\r\nconst industries = [\r\n  { name: \"税务系统\", icon: TaxIcon, count: \"200\" },\r\n  { name: \"公安系统\", icon: PoliceIcon, count: \"150\" },\r\n  { name: \"银行金融\", icon: BankIcon, count: \"300\" },\r\n  { name: \"教育机构\", icon: EduIcon, count: \"350\" },\r\n];\r\n\r\n// 客户图标组件\r\nconst GovernmentIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\",\r\n    }),\r\n  ]);\r\n\r\nconst CompanyIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6\",\r\n    }),\r\n  ]);\r\n\r\nconst MediaIcon = () =>\r\n  h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n    h(\"path\", {\r\n      \"stroke-linecap\": \"round\",\r\n      \"stroke-linejoin\": \"round\",\r\n      \"stroke-width\": \"2\",\r\n      d: \"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\",\r\n    }),\r\n  ]);\r\n\r\n// 典型用户列表\r\nconst typicalClients = [\r\n  {\r\n    name: \"中共中央网信办\",\r\n    category: \"中央机关\",\r\n    icon: GovernmentIcon,\r\n  },\r\n  {\r\n    name: \"中央编办事业发展中心\",\r\n    category: \"中央机关\",\r\n    icon: GovernmentIcon,\r\n  },\r\n  {\r\n    name: \"中国人民银行宜昌支行\",\r\n    category: \"金融机构\",\r\n    icon: BankIcon,\r\n  },\r\n  {\r\n    name: \"紫光软件系统有限公司\",\r\n    category: \"科技企业\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"中国黄金集团公司\",\r\n    category: \"国有企业\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"中国家庭医生杂志社\",\r\n    category: \"媒体机构\",\r\n    icon: MediaIcon,\r\n  },\r\n  {\r\n    name: \"西北工业大学管理学院\",\r\n    category: \"教育机构\",\r\n    icon: EduIcon,\r\n  },\r\n  {\r\n    name: \"万达信息股份有限公司\",\r\n    category: \"上市公司\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"太极计算机股份有限公司\",\r\n    category: \"上市公司\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"兴业银行长沙分行\",\r\n    category: \"金融机构\",\r\n    icon: BankIcon,\r\n  },\r\n  {\r\n    name: \"用友汽车信息科技（上海）股份有限公司\",\r\n    category: \"科技企业\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"用友软件有限公司四川分公司\",\r\n    category: \"科技企业\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"中山市公安局南头分局\",\r\n    category: \"公安系统\",\r\n    icon: PoliceIcon,\r\n  },\r\n  {\r\n    name: \"首都信息发展股份有限公司\",\r\n    category: \"上市公司\",\r\n    icon: CompanyIcon,\r\n  },\r\n  {\r\n    name: \"神州数码信息系统有限公司\",\r\n    category: \"科技企业\",\r\n    icon: CompanyIcon,\r\n  },\r\n];\r\n</script>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAuBA,UAAM,SAAS;AAAA,MACb,EAAE,OAAO,sBAAsB,KAAK,WAAW,OAAO,eAAe,aAAa,gBAAiB;AAAA,MACnG,EAAE,OAAO,sBAAsB,KAAK,WAAW,OAAO,QAAQ,aAAa,eAAgB;AAAA,MAC3F,EAAE,OAAO,sBAAsB,KAAK,WAAW,OAAO,SAAS,aAAa,cAAe;AAAA,IAC7F;AAEA,UAAM,eAAe,IAAI,CAAC;AACZ,QAAI,IAAI;;;;;;;;;;;;;;;;;;;;AC6JtB,UAAM,WAAW;AAAA,MACf;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,MACV;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtGA,UAAM,eAAe,MACnB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,WAAW,MACf,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,YAAY,MAChB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,eAAe,MACnB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,cAAc,MAClB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,aAAa,MACjB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAGH,UAAM,UAAU,MACd,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,aAAa,MACjB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,WAAW,MACf,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,UAAU,MACd,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,MACD,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,WAAW;AAAA,MACf;AAAA,QACE,OAAO;AAAA,QACP,aACE;AAAA,QACF,MAAM;AAAA,QACN,OAAO;AAAA,UACL,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,UAC/B,EAAE,OAAO,OAAO,OAAO,OAAQ;AAAA,QAChC;AAAA,MACF;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aACE;AAAA,QACF,MAAM;AAAA,QACN,OAAO;AAAA,UACL,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,OAAO,OAAO,QAAS;AAAA,QACjC;AAAA,MACF;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aACE;AAAA,QACF,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aACE;AAAA,QACF,MAAM;AAAA,QACN,OAAO;AAAA,UACL,EAAE,OAAO,SAAS,OAAO,MAAO;AAAA,UAChC,EAAE,OAAO,KAAK,OAAO,OAAQ;AAAA,QAC9B;AAAA,MACF;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,MACP;AAAA,IACH;AAEA,UAAM,aAAa;AAAA,MACjB,EAAE,MAAM,QAAQ,MAAM,SAAS,OAAO,MAAO;AAAA,MAC7C,EAAE,MAAM,QAAQ,MAAM,YAAY,OAAO,MAAO;AAAA,MAChD,EAAE,MAAM,QAAQ,MAAM,UAAU,OAAO,MAAO;AAAA,MAC9C,EAAE,MAAM,QAAQ,MAAM,SAAS,OAAO,MAAO;AAAA,IAC/C;AAGA,UAAM,iBAAiB,MACrB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,cAAc,MAClB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAEH,UAAM,YAAY,MAChB,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,MACvE,EAAE,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACT,CAAK;AAAA,IACL,CAAG;AAGH,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,MACP;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}