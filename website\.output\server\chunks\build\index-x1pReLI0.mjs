import { ssr<PERSON><PERSON><PERSON>tt<PERSON>, ssr<PERSON><PERSON><PERSON>omponent, ssrRenderList, ssrRenderClass, ssrRenderAttr, ssrInterpolate, ssrRenderVNode } from 'vue/server-renderer';
import { _ as _sfc_main$1$1, a as _sfc_main$4 } from './Footer-C3PwX65Z.mjs';
import { useSSRContext, ref, mergeProps, withCtx, openBlock, createBlock, createVNode, createTextVNode, resolveDynamicComponent, h } from 'vue';
import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import './server.mjs';
import 'vue-router';

const _sfc_main$3 = {
  __name: "Carousel",
  __ssrInlineRender: true,
  setup(__props) {
    const slides = [
      { image: "/images/slide1.png", alt: "Slide 1", title: "\u8DE8\u5E73\u53F0\u626B\u63CF\u63D2\u4EF6\u89E3\u51B3\u65B9\u6848", description: "\u4E3A\u60A8\u7684\u4E1A\u52A1\u63D0\u4F9B\u6700\u5148\u8FDB\u7684\u6280\u672F" },
      { image: "/images/slide2.png", alt: "Slide 2", title: "\u4E13\u4E1A\u56E2\u961F", description: "\u7ECF\u9A8C\u4E30\u5BCC\u7684\u5F00\u53D1\u8005\u4E3A\u60A8\u670D\u52A1" },
      { image: "/images/slide3.png", alt: "Slide 3", title: "\u5BA2\u6237\u6EE1\u610F\u5EA6", description: "\u6211\u4EEC\u4EE5\u5BA2\u6237\u7684\u6210\u529F\u4E3A\u5DF1\u4EFB" }
    ];
    const currentSlide = ref(0);
    ref(null);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "relative h-[500px] overflow-hidden" }, _attrs))}><!--[-->`);
      ssrRenderList(slides, (slide, index) => {
        _push(`<div class="${ssrRenderClass([{ "opacity-100": currentSlide.value === index, "opacity-0": currentSlide.value !== index }, "absolute top-0 left-0 w-full h-full transition-opacity duration-500 ease-in-out"])}"><img${ssrRenderAttr("src", slide.image)}${ssrRenderAttr("alt", slide.alt)} class="w-full h-full object-cover"><div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-6"><h2 class="text-3xl font-bold mb-2">${ssrInterpolate(slide.title)}</h2><p class="text-lg">${ssrInterpolate(slide.description)}</p></div></div>`);
      });
      _push(`<!--]--><button class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full"> \u2039 </button><button class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full"> \u203A </button></div>`);
    };
  }
};
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Carousel.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const _sfc_main$2 = {
  __name: "DownloadSection",
  __ssrInlineRender: true,
  setup(__props) {
    const products = [
      {
        id: 1,
        name: "ScanOnWeb",
        description: "\u4E13\u4E1A\u626B\u63CF\u4EEA\u63A7\u4EF6",
        platforms: "Windows / Linux",
        version: "v3.5.0"
      },
      {
        id: 2,
        name: "ImageCapOnWeb",
        description: "\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u63A7\u4EF6",
        platforms: "Windows",
        version: "v2.8.0"
      },
      {
        id: 3,
        name: "GaoPaiYi",
        description: "\u9AD8\u62CD\u4EEA\u63A7\u4EF6",
        platforms: "Windows",
        version: "v2.0.1"
      }
    ];
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<section${ssrRenderAttrs(mergeProps({ class: "py-20 bg-business-gradient" }, _attrs))}><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl md:text-5xl font-bold text-white mb-6"> \u4E13\u4E1AWeb\u63A7\u4EF6\u89E3\u51B3\u65B9\u6848 </h2><p class="text-xl text-blue-100 mb-4 max-w-4xl mx-auto"> \u4E3A\u4F01\u4E1A\u7EA7\u5E94\u7528\u63D0\u4F9B\u7A33\u5B9A\u53EF\u9760\u7684\u626B\u63CF\u4EEA\u3001\u6444\u50CF\u5934\u3001\u9AD8\u62CD\u4EEA\u63A7\u4EF6\uFF0C\u652F\u6301Windows\u548CLinux\u591A\u5E73\u53F0 </p><div class="flex justify-center items-center space-x-8 text-blue-100 text-sm"><div class="flex items-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>10\u5E74+\u6280\u672F\u79EF\u7D2F</span></div><div class="flex items-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>1000+\u4F01\u4E1A\u5BA2\u6237</span></div><div class="flex items-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>\u4FE1\u521B\u8BA4\u8BC1\u4EA7\u54C1</span></div></div></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"><!--[-->`);
      ssrRenderList(products, (product) => {
        _push(`<div class="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"><div class="flex items-center mb-4"><div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4"><svg class="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><div><h3 class="text-xl font-bold text-gray-900">${ssrInterpolate(product.name)}</h3><p class="text-sm text-gray-500">${ssrInterpolate(product.version)}</p></div></div><p class="text-gray-600 mb-4">${ssrInterpolate(product.description)}</p><div class="space-y-2"><div class="flex items-center text-sm text-gray-500"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> \u652F\u6301\u5E73\u53F0\uFF1A${ssrInterpolate(product.platforms)}</div><div class="flex items-center text-sm text-gray-500"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg> \u4F01\u4E1A\u7EA7\u7A33\u5B9A\u6027 </div></div></div>`);
      });
      _push(`<!--]--></div><div class="text-center"><div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/download",
        class: "bg-orange-500 hover:bg-orange-600 text-white px-10 py-4 rounded-lg text-lg font-bold transition-all duration-300 shadow-lg flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"${_scopeId}></path></svg> \u514D\u8D39\u4E0B\u8F7D\u8BD5\u7528 `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                class: "w-5 h-5 mr-2",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                })
              ])),
              createTextVNode(" \u514D\u8D39\u4E0B\u8F7D\u8BD5\u7528 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/products",
        class: "bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 px-10 py-4 rounded-lg text-lg font-bold transition-all duration-300"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u67E5\u770B\u4EA7\u54C1\u8BE6\u60C5 `);
          } else {
            return [
              createTextVNode(" \u67E5\u770B\u4EA7\u54C1\u8BE6\u60C5 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-blue-100 text-sm"><div class="flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>\u65E0\u9700\u6CE8\u518C\u5373\u53EF\u4E0B\u8F7D\u8BD5\u7528</span></div><div class="flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>\u652F\u6301\u591A\u79CDCPU\u67B6\u6784</span></div><div class="flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg><span>7\xD724\u5C0F\u65F6\u6280\u672F\u652F\u6301</span></div></div></div></div></section>`);
    };
  }
};
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/DownloadSection.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = {
  __name: "ProductFeatures",
  __ssrInlineRender: true,
  setup(__props) {
    const PlatformIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
      })
    ]);
    const EaseIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      })
    ]);
    const ScaleIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
      })
    ]);
    const SecurityIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
      })
    ]);
    const SupportIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944l7.071 7.071-7.071 7.071-7.071-7.071L12 2.944z"
      })
    ]);
    const CustomIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
      })
    ]);
    const TaxIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
      })
    ]);
    const PoliceIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
      })
    ]);
    const BankIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      })
    ]);
    const EduIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M12 14l9-5-9-5-9 5 9 5z"
      }),
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
      })
    ]);
    const features = [
      {
        title: "\u8DE8\u5E73\u53F0\u517C\u5BB9",
        description: "\u56FD\u4EA7\u8F6F\u4EF6\u5382\u5546\uFF0C\u4EA7\u54C1\u5168\u9762\u652F\u6301Windows\u548C\u4FE1\u521B\u56FD\u4EA7\u5316\u8F6F\u786C\u4EF6\u5E94\u7528\u73AF\u5883\uFF0C\u901A\u8FC7\u591A\u9879\u8BA4\u8BC1\u3002",
        icon: PlatformIcon,
        stats: [
          { value: "100%", label: "\u517C\u5BB9\u6027" },
          { value: "10+", label: "\u5E73\u53F0\u652F\u6301" }
        ]
      },
      {
        title: "\u7B80\u5355\u6613\u7528",
        description: "\u76F4\u89C2\u7684API\u8BBE\u8BA1\u548C\u5B8C\u5584\u7684\u6587\u6863\uFF0C\u8BA9\u5F00\u53D1\u8005\u53EF\u4EE5\u5FEB\u901F\u96C6\u6210\uFF0C\u5927\u5E45\u964D\u4F4E\u5F00\u53D1\u6210\u672C\u3002",
        icon: EaseIcon,
        stats: [
          { value: "30\u5206\u949F", label: "\u5FEB\u901F\u96C6\u6210" },
          { value: "99%", label: "\u5BA2\u6237\u6EE1\u610F\u5EA6" }
        ]
      },
      {
        title: "\u9AD8\u5EA6\u53EF\u6269\u5C55",
        description: "\u7075\u6D3B\u7684\u67B6\u6784\u8BBE\u8BA1\uFF0C\u652F\u6301\u4E8C\u6B21\u5F00\u53D1\u548C\u5B9A\u5236\uFF0C\u53EF\u6839\u636E\u4E1A\u52A1\u9700\u6C42\u8FDB\u884C\u529F\u80FD\u6269\u5C55\u3002",
        icon: ScaleIcon
      },
      {
        title: "\u5B89\u5168\u53EF\u9760",
        description: "\u4EA7\u54C1\u5386\u7ECF10\u5E74\u7814\u53D1\u5E94\u7528\uFF0C\u57281000+\u4F01\u4E1A\u5BA2\u6237\u4E2D\u7A33\u5B9A\u8FD0\u884C\uFF0C\u7ECF\u53D7\u4E86\u5B9E\u9645\u751F\u4EA7\u73AF\u5883\u8003\u9A8C\u3002",
        icon: SecurityIcon,
        stats: [
          { value: "99.9%", label: "\u7A33\u5B9A\u6027" },
          { value: "0", label: "\u5B89\u5168\u4E8B\u6545" }
        ]
      },
      {
        title: "\u4E13\u4E1A\u652F\u6301",
        description: "7\xD724\u5C0F\u65F6\u6280\u672F\u652F\u6301\u56E2\u961F\uFF0C\u63D0\u4F9B\u8FDC\u7A0B\u534F\u52A9\u3001\u73B0\u573A\u670D\u52A1\u7B49\u591A\u79CD\u652F\u6301\u65B9\u5F0F\u3002",
        icon: SupportIcon
      },
      {
        title: "\u5B9A\u5236\u670D\u52A1",
        description: "\u6839\u636E\u5BA2\u6237\u5177\u4F53\u9700\u6C42\uFF0C\u63D0\u4F9B\u4E13\u4E1A\u7684\u5B9A\u5236\u5F00\u53D1\u670D\u52A1\u548C\u884C\u4E1A\u89E3\u51B3\u65B9\u6848\u3002",
        icon: CustomIcon
      }
    ];
    const industries = [
      { name: "\u7A0E\u52A1\u7CFB\u7EDF", icon: TaxIcon, count: "200" },
      { name: "\u516C\u5B89\u7CFB\u7EDF", icon: PoliceIcon, count: "150" },
      { name: "\u94F6\u884C\u91D1\u878D", icon: BankIcon, count: "300" },
      { name: "\u6559\u80B2\u673A\u6784", icon: EduIcon, count: "350" }
    ];
    const GovernmentIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      })
    ]);
    const CompanyIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"
      })
    ]);
    const MediaIcon = () => h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
      h("path", {
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        d: "M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
      })
    ]);
    const typicalClients = [
      {
        name: "\u4E2D\u5171\u4E2D\u592E\u7F51\u4FE1\u529E",
        category: "\u4E2D\u592E\u673A\u5173",
        icon: GovernmentIcon
      },
      {
        name: "\u4E2D\u592E\u7F16\u529E\u4E8B\u4E1A\u53D1\u5C55\u4E2D\u5FC3",
        category: "\u4E2D\u592E\u673A\u5173",
        icon: GovernmentIcon
      },
      {
        name: "\u4E2D\u56FD\u4EBA\u6C11\u94F6\u884C\u5B9C\u660C\u652F\u884C",
        category: "\u91D1\u878D\u673A\u6784",
        icon: BankIcon
      },
      {
        name: "\u7D2B\u5149\u8F6F\u4EF6\u7CFB\u7EDF\u6709\u9650\u516C\u53F8",
        category: "\u79D1\u6280\u4F01\u4E1A",
        icon: CompanyIcon
      },
      {
        name: "\u4E2D\u56FD\u9EC4\u91D1\u96C6\u56E2\u516C\u53F8",
        category: "\u56FD\u6709\u4F01\u4E1A",
        icon: CompanyIcon
      },
      {
        name: "\u4E2D\u56FD\u5BB6\u5EAD\u533B\u751F\u6742\u5FD7\u793E",
        category: "\u5A92\u4F53\u673A\u6784",
        icon: MediaIcon
      },
      {
        name: "\u897F\u5317\u5DE5\u4E1A\u5927\u5B66\u7BA1\u7406\u5B66\u9662",
        category: "\u6559\u80B2\u673A\u6784",
        icon: EduIcon
      },
      {
        name: "\u4E07\u8FBE\u4FE1\u606F\u80A1\u4EFD\u6709\u9650\u516C\u53F8",
        category: "\u4E0A\u5E02\u516C\u53F8",
        icon: CompanyIcon
      },
      {
        name: "\u592A\u6781\u8BA1\u7B97\u673A\u80A1\u4EFD\u6709\u9650\u516C\u53F8",
        category: "\u4E0A\u5E02\u516C\u53F8",
        icon: CompanyIcon
      },
      {
        name: "\u5174\u4E1A\u94F6\u884C\u957F\u6C99\u5206\u884C",
        category: "\u91D1\u878D\u673A\u6784",
        icon: BankIcon
      },
      {
        name: "\u7528\u53CB\u6C7D\u8F66\u4FE1\u606F\u79D1\u6280\uFF08\u4E0A\u6D77\uFF09\u80A1\u4EFD\u6709\u9650\u516C\u53F8",
        category: "\u79D1\u6280\u4F01\u4E1A",
        icon: CompanyIcon
      },
      {
        name: "\u7528\u53CB\u8F6F\u4EF6\u6709\u9650\u516C\u53F8\u56DB\u5DDD\u5206\u516C\u53F8",
        category: "\u79D1\u6280\u4F01\u4E1A",
        icon: CompanyIcon
      },
      {
        name: "\u4E2D\u5C71\u5E02\u516C\u5B89\u5C40\u5357\u5934\u5206\u5C40",
        category: "\u516C\u5B89\u7CFB\u7EDF",
        icon: PoliceIcon
      },
      {
        name: "\u9996\u90FD\u4FE1\u606F\u53D1\u5C55\u80A1\u4EFD\u6709\u9650\u516C\u53F8",
        category: "\u4E0A\u5E02\u516C\u53F8",
        icon: CompanyIcon
      },
      {
        name: "\u795E\u5DDE\u6570\u7801\u4FE1\u606F\u7CFB\u7EDF\u6709\u9650\u516C\u53F8",
        category: "\u79D1\u6280\u4F01\u4E1A",
        icon: CompanyIcon
      }
    ];
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<section${ssrRenderAttrs(mergeProps({ class: "py-20 bg-gray-50" }, _attrs))}><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="heading-primary mb-6">\u4E3A\u4EC0\u4E48\u9009\u62E9\u6211\u4EEC</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto"> \u4E13\u6CE8Web\u63A7\u4EF6\u6280\u672F10\u5E74\uFF0C\u4E3A1000+\u4F01\u4E1A\u5BA2\u6237\u63D0\u4F9B\u7A33\u5B9A\u53EF\u9760\u7684\u89E3\u51B3\u65B9\u6848 </p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><!--[-->`);
      ssrRenderList(features, (feature) => {
        _push(`<div class="card-business p-8 text-center group"><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-200 transition-colors duration-200">`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(feature.icon), { class: "w-8 h-8 text-orange-500" }, null), _parent);
        _push(`</div><h3 class="heading-tertiary mb-4">${ssrInterpolate(feature.title)}</h3><p class="text-business">${ssrInterpolate(feature.description)}</p>`);
        if (feature.stats) {
          _push(`<div class="mt-6 pt-6 border-t border-gray-100"><div class="flex justify-center space-x-6"><!--[-->`);
          ssrRenderList(feature.stats, (stat) => {
            _push(`<div class="text-center"><div class="text-2xl font-bold text-orange-500">${ssrInterpolate(stat.value)}</div><div class="text-sm text-gray-500">${ssrInterpolate(stat.label)}</div></div>`);
          });
          _push(`<!--]--></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      });
      _push(`<!--]--></div><div class="mt-20 bg-white rounded-xl p-8 shadow-sm"><div class="text-center mb-12"><h3 class="heading-secondary mb-4">\u5BA2\u6237\u904D\u5E03\u5404\u884C\u5404\u4E1A</h3><p class="text-business">\u670D\u52A1\u7A0E\u52A1\u3001\u516C\u5B89\u3001\u94F6\u884C\u3001\u6559\u80B2\u7B49\u591A\u4E2A\u91CD\u8981\u884C\u4E1A</p></div><div class="grid grid-cols-2 md:grid-cols-4 gap-8"><!--[-->`);
      ssrRenderList(industries, (industry) => {
        _push(`<div class="text-center"><div class="w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(industry.icon), { class: "w-10 h-10 text-blue-600" }, null), _parent);
        _push(`</div><h4 class="font-semibold text-gray-800 mb-2">${ssrInterpolate(industry.name)}</h4><p class="text-sm text-gray-500">${ssrInterpolate(industry.count)}+ \u5BA2\u6237</p></div>`);
      });
      _push(`<!--]--></div><div class="mt-16 pt-12 border-t border-gray-200"><div class="text-center mb-12"><h3 class="heading-secondary mb-4">\u5178\u578B\u7528\u6237</h3><p class="text-business">\u90E8\u5206\u5408\u4F5C\u4F19\u4F34\u548C\u5BA2\u6237\uFF08\u4EE5\u4E0B\u6392\u540D\u4E0D\u5206\u5148\u540E\uFF09</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><!--[-->`);
      ssrRenderList(typicalClients, (client) => {
        _push(`<div class="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors duration-200"><div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(client.icon), { class: "w-8 h-8 text-gray-600" }, null), _parent);
        _push(`</div><h4 class="font-semibold text-gray-900 text-sm leading-relaxed">${ssrInterpolate(client.name)}</h4><p class="text-xs text-gray-500 mt-2">${ssrInterpolate(client.category)}</p></div>`);
      });
      _push(`<!--]--></div><div class="text-center mt-8"><p class="text-sm text-gray-500"> \u611F\u8C22\u6240\u6709\u5408\u4F5C\u4F19\u4F34\u7684\u4FE1\u4EFB\u4E0E\u652F\u6301\uFF0C\u6211\u4EEC\u5C06\u6301\u7EED\u4E3A\u60A8\u63D0\u4F9B\u4F18\u8D28\u7684\u4EA7\u54C1\u548C\u670D\u52A1 </p></div></div></div></div></section>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ProductFeatures.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)}>`);
      _push(ssrRenderComponent(_sfc_main$1$1, null, null, _parent));
      _push(ssrRenderComponent(_sfc_main$3, null, null, _parent));
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(ssrRenderComponent(_sfc_main$4, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-x1pReLI0.mjs.map
