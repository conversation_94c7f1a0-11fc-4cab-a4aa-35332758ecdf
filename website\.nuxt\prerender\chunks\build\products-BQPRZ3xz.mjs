import { ref, mergeProps, use<PERSON><PERSON>ontext, withCtx, openBlock, createBlock, createVNode, createTextVNode } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssr<PERSON><PERSON>Att<PERSON>, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderAttr } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { _ as _sfc_main$1$1, a as _sfc_main$3 } from './Footer-C3PwX65Z.mjs';
import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { _ as _export_sfc } from './server.mjs';
import '../_/renderer.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const _sfc_main$2 = {
  __name: "ProductSidebar",
  __ssrInlineRender: true,
  props: {
    products: Array,
    selectedProductId: Number
  },
  emits: ["select"],
  setup(__props) {
    const getShortDescription = (description) => {
      return description.length > 50 ? description.substring(0, 50) + "..." : description;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-white shadow-lg rounded-lg overflow-hidden" }, _attrs))}><div class="bg-blue-600 text-white p-4"><h2 class="text-2xl font-bold">\u4EA7\u54C1\u5217\u8868</h2></div><ul><!--[-->`);
      ssrRenderList(__props.products, (product) => {
        _push(`<li class="border-b last:border-b-0"><button class="${ssrRenderClass([{
          "bg-blue-100": product.id === __props.selectedProductId,
          "hover:bg-gray-100": product.id !== __props.selectedProductId
        }, "w-full text-left p-4 flex items-center transition-colors"])}"><div class="${ssrRenderClass([{
          "bg-gradient-to-r from-blue-500 to-indigo-600": product.id === 1,
          "bg-gradient-to-r from-purple-500 to-pink-600": product.id === 2,
          "bg-gradient-to-r from-green-500 to-teal-600": product.id === 3
        }, "w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold text-xl"])}">${ssrInterpolate(product.name.charAt(0))}</div><div><p class="font-semibold text-gray-800">${ssrInterpolate(product.name)}</p><p class="text-xs text-gray-500 mt-1">${ssrInterpolate(getShortDescription(product.description))}</p></div></button></li>`);
      });
      _push(`<!--]--></ul></div>`);
    };
  }
};
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ProductSidebar.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = {
  __name: "ProductDetails",
  __ssrInlineRender: true,
  props: {
    product: Object
  },
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      if (__props.product) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "card-business overflow-hidden" }, _attrs))}><div class="bg-business-gradient p-8"><div class="flex items-center justify-between"><div><h1 class="text-2xl md:text-3xl font-bold text-white mb-2">${ssrInterpolate(__props.product.name)}</h1><p class="text-blue-100">\u4F01\u4E1A\u7EA7Web\u63A7\u4EF6\u89E3\u51B3\u65B9\u6848</p></div><div class="text-right text-white"><div class="text-sm text-blue-100">\u8F6F\u4EF6\u8457\u4F5C\u6743</div><div class="font-semibold">2013SR145420</div></div></div></div><div class="p-8"><div class="mb-8"><p class="text-business text-lg leading-relaxed">${ssrInterpolate(__props.product.description)}</p></div><div class="mb-8 p-6 bg-orange-50 rounded-lg border border-orange-200"><div class="text-center"><h3 class="text-xl font-bold text-gray-900 mb-2">\u7ACB\u5373\u5F00\u59CB\u514D\u8D39\u8BD5\u7528</h3><p class="text-gray-600 mb-4">\u65E0\u9700\u6CE8\u518C\uFF0C\u652F\u6301\u591A\u5E73\u53F0\uFF0C\u4E13\u4E1A\u6280\u672F\u652F\u6301</p>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/download",
          class: "btn-primary text-lg px-8 py-4 inline-flex items-center"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"${_scopeId}></path></svg> \u514D\u8D39\u4E0B\u8F7D\u8BD5\u7528\u7248 `);
            } else {
              return [
                (openBlock(), createBlock("svg", {
                  class: "w-5 h-5 mr-2",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24"
                }, [
                  createVNode("path", {
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round",
                    "stroke-width": "2",
                    d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  })
                ])),
                createTextVNode(" \u514D\u8D39\u4E0B\u8F7D\u8BD5\u7528\u7248 ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div><div class="mb-8"><h3 class="heading-secondary mb-6">\u6838\u5FC3\u529F\u80FD\u7279\u6027</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><!--[-->`);
        ssrRenderList(__props.product.features, (feature) => {
          _push(`<div class="flex items-start p-4 bg-gray-50 rounded-lg"><div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0"><svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><span class="text-gray-700 font-medium">${ssrInterpolate(feature)}</span></div>`);
        });
        _push(`<!--]--></div></div>`);
        if (__props.product.tableData) {
          _push(`<div class="mb-8"><h3 class="heading-secondary mb-6">\u6280\u672F\u89C4\u683C\u8BE6\u60C5</h3><div class="overflow-x-auto"><table class="min-w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm"><thead><tr class="bg-gray-50"><th class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"> \u7279\u6027\u5206\u7C7B </th><th class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"> \u6280\u672F\u53C2\u6570 </th></tr></thead><tbody><!--[-->`);
          ssrRenderList(__props.product.tableData, (item, index) => {
            _push(`<tr class="${ssrRenderClass(index % 2 === 0 ? "bg-white" : "bg-gray-50")}"><td class="border-b border-gray-200 px-6 py-4 font-medium text-gray-900 w-1/4">${ssrInterpolate(item.category)}</td><td class="border-b border-gray-200 px-6 py-4 text-gray-700 whitespace-pre-line">${ssrInterpolate(item.value)}</td></tr>`);
          });
          _push(`<!--]--></tbody></table></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"><div class="card-business p-6"><h3 class="heading-tertiary mb-4">\u57FA\u7840\u6280\u672F\u89C4\u683C</h3><div class="space-y-3"><!--[-->`);
        ssrRenderList(__props.product.specs, (value, key) => {
          _push(`<div class="flex justify-between py-2 border-b border-gray-100 last:border-b-0"><span class="font-medium text-gray-700">${ssrInterpolate(key)}</span><span class="text-gray-600 text-right">${ssrInterpolate(value)}</span></div>`);
        });
        _push(`<!--]--></div></div><div class="card-business p-6"><h3 class="heading-tertiary mb-4">\u5178\u578B\u5E94\u7528\u573A\u666F</h3><div class="grid grid-cols-2 gap-3"><!--[-->`);
        ssrRenderList(__props.product.useCases, (useCase) => {
          _push(`<div class="flex items-center p-3 bg-orange-50 rounded-lg"><svg class="w-5 h-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg><span class="text-gray-700 font-medium">${ssrInterpolate(useCase)}</span></div>`);
        });
        _push(`<!--]--></div></div></div><div class="feature-highlight mb-8"><div class="flex items-center justify-between"><div><h3 class="heading-tertiary text-orange-700 mb-2"> \u4F01\u4E1A\u7EA7\u5B9A\u4EF7\u65B9\u6848 </h3><p class="text-orange-600">${ssrInterpolate(__props.product.pricing)}</p></div><div class="text-right"><div class="text-sm text-orange-600">\u8054\u7CFB\u9500\u552E\u83B7\u53D6</div><div class="font-bold text-orange-700">\u4E13\u5C5E\u4F18\u60E0\u4EF7\u683C</div></div></div></div><div class="flex flex-col sm:flex-row gap-4"><a${ssrRenderAttr("href", __props.product.videoUrl)} target="_blank" class="btn-secondary flex-1 text-center inline-flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> \u89C2\u770B\u4EA7\u54C1\u6F14\u793A </a><a${ssrRenderAttr("href", __props.product.pdfUrl)} target="_blank" class="btn-secondary flex-1 text-center inline-flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> \u4E0B\u8F7D\u4EA7\u54C1\u8D44\u6599 </a></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ProductDetails.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "products",
  __ssrInlineRender: true,
  setup(__props) {
    const products2 = ref([
      {
        id: 1,
        name: "ScanOnWeb",
        description: "ScanOnWeb\u63A7\u4EF6(\u8F6F\u4EF6\u8457\u4F5C\u6743\u767B\u8BB0\u53F7 2013SR145420\uFF0C\u8BC1\u4E66\u53F70651182)\u7528\u4E8E\u5904\u7406\u56FE\u50CF\u626B\u63CF\u7F16\u7A0B\uFF0C\u9002\u5408\u7528\u4E8Eweb\u73AF\u5883\u4E0B\u7684\u626B\u63CF\u4EEA\u7F16\u7A0B\u5E94\u7528\uFF0C\u53EF\u65E0\u7F1D\u96C6\u6210\u5230jsp\u3001php\u3001asp.net\u7B49\u7F16\u7A0B\u6280\u672F\u5F53\u4E2D\u3002\u63A7\u4EF6\u517C\u5BB9\u76EE\u524D\u4E3B\u6D41\u7684\u6570\u6B3E\u626B\u63CF\u8BBE\u5907\uFF0C\u5BF9\u4E8E\u4E2A\u522B\u975E\u6309\u6807\u51C6\u534F\u8BAE\u652F\u6301\u7684\u626B\u63CF\u8BBE\u5907\u4EA6\u63D0\u4F9B\u4E86\u96C6\u6210\u652F\u6301\uFF0C\u76EE\u524D\u63A7\u4EF6\u7ECF\u8FC7\u591A\u5E74\u7684\u53D1\u5C55\u5DF2\u7ECF\u5F88\u6210\u719F\u7A33\u5B9A\uFF0C\u88AB\u5E7F\u6CDB\u7684\u5E94\u7528\u4E8E\u529E\u516COA\u3001\u7535\u5B50\u653F\u52A1\u3001\u7EB8\u8D28\u6587\u6863\u7535\u5B50\u5316\u7B49\u5E94\u7528\u573A\u666F\uFF0C\u5BA2\u6237\u5355\u4F4D\u904D\u5E03\u7A0E\u52A1\u3001\u516C\u5B89\u3001\u5EFA\u7B51\u3001\u94F6\u884C\u7B49\u591A\u4E2A\u884C\u4E1A\uFF0C\u662F\u76EE\u524D\u56FD\u5185\u552F\u4E00\u6210\u719F\u7A33\u5B9A\u7684\u626B\u63CF\u63A7\u4EF6\u4EA7\u54C1\u3002",
        image: "/images/slide1.png",
        features: [
          "\u626B\u63CF\u8BBE\u5907\u9009\u62E9",
          "\u81EA\u52A8\u8FDB\u7EB8\u5668\u8FDE\u7EED\u591A\u9875\u626B\u63CF",
          "\u53CC\u9762\u626B\u63CF\u6A21\u5F0F",
          "\u81EA\u52A8\u7EA0\u504F\u6A21\u5F0F",
          "\u591A\u79CD\u56FE\u50CF\u626B\u63CF\u6A21\u5F0F",
          "\u591A\u79CD\u5206\u8FA8\u7387\u8BBE\u7F6E"
        ],
        specs: {
          \u6D4F\u89C8\u5668\u517C\u5BB9\u6027: "\u6240\u6709\u652F\u6301websocket\u7684\u73B0\u4EE3\u6D4F\u89C8\u5668,\u5305\u62EC\uFF1Achrome\u3001edge\u3001firefox\u3001IE11\u7B49",
          \u56FE\u50CF\u626B\u63CF\u534F\u8BAE: "\u652F\u6301\u517C\u5BB9twain1.9\u53CA\u4EE5\u4E0A\u534F\u8BAE\u7684\u6240\u6709\u626B\u63CF\u4EEA\u6216\u5176\u4ED6\u56FE\u50CF\u91C7\u96C6\u8BBE\u5907\uFF0C\u652F\u6301WIA\u534F\u8BAE\u7684\u6240\u6709\u56FE\u50CF\u91C7\u96C6\u8BBE\u5907",
          \u56FE\u50CF\u7F16\u8F91\u7279\u6027: "\u652F\u6301\u65CB\u8F6C\u3001\u88C1\u526A\u3001\u586B\u767D\u3001\u9A6C\u8D5B\u514B\u7B49\u591A\u79CD\u5904\u7406",
          \u56FE\u50CF\u4E0A\u4F20\u80FD\u529B: "\u652F\u6301\u591A\u79CD\u683C\u5F0F\u4E0A\u4F20\u5230\u6307\u5B9AURL"
        },
        useCases: [
          "\u529E\u516COA",
          "\u7535\u5B50\u653F\u52A1",
          "\u7EB8\u8D28\u6587\u6863\u7535\u5B50\u5316",
          "\u7A0E\u52A1\u7CFB\u7EDF",
          "\u516C\u5B89\u7CFB\u7EDF",
          "\u94F6\u884C\u4E1A\u52A1"
        ],
        pricing: "\u8BF7\u4E0B\u8F7D\u62A5\u4EF7\u5355\u4E86\u89E3\u8BE6\u60C5",
        videoUrl: "https://example.com/scanonweb-demo.mp4",
        pdfUrl: "/ScanOnWeb-quotation.pdf",
        tableData: [
          {
            category: "\u6D4F\u89C8\u5668\u517C\u5BB9\u6027",
            value: "\u6240\u6709\u652F\u6301websocket\u7684\u73B0\u4EE3\u6D4F\u89C8\u5668,\u5305\u62EC\uFF1Achrome\u3001edge\u3001firefox\u3001IE11\u7B49"
          },
          {
            category: "\u56FE\u50CF\u626B\u63CF\u534F\u8BAE",
            value: "1.\u652F\u6301\u517C\u5BB9twain1.9\u53CA\u4EE5\u4E0A\u534F\u8BAE\u7684\u6240\u6709\u626B\u63CF\u4EEA\u6216\u5176\u4ED6\u56FE\u50CF\u91C7\u96C6\u8BBE\u59072.\u652F\u6301WIA\u534F\u8BAE\u7684\u6240\u6709\u56FE\u50CF\u91C7\u96C6\u8BBE\u5907"
          },
          {
            category: "\u56FE\u50CF\u626B\u63CF\u53C2\u6570\u63A7\u5236",
            value: "1. \u626B\u63CF\u8BBE\u5907\u9009\u62E9 2. \u662F\u5426\u663E\u793A\u626B\u63CF\u4EEA\u9A71\u52A8\u7A0B\u5E8F\u5185\u7F6E\u8BBE\u7F6E\u5BF9\u8BDD\u6846 3. \u662F\u5426\u4F7F\u7528\u81EA\u52A8\u8FDB\u7EB8\u5668\u8FDB\u884C\u8FDE\u7EED\u591A\u9875\u626B\u63CF 4. \u662F\u5426\u4F7F\u7528\u81EA\u52A8\u88C5\u586B\u7EB8\u5F20\u6A21\u5F0F 5. \u662F\u5426\u4F7F\u7528\u53CC\u9762\u626B\u63CF\u6A21\u5F0F 6. \u662F\u5426\u4F7F\u7528\u81EA\u52A8\u7EA0\u504F\u6A21\u5F0F 7. \u662F\u5426\u4F7F\u7528\u81EA\u52A8\u8FB9\u6846\u68C0\u6D4B\u6A21\u5F0F 8. \u56FE\u50CF\u626B\u63CF\u6A21\u5F0F\uFF1A\u9ED1\u767D\u6A21\u5F0F\u3001\u7070\u5EA6\u6A21\u5F0F\u3001\u5F69\u8272\u6A21\u5F0F 9. dpi\u5206\u8FA8\u7387\u8BBE\u7F6E 10. \u626B\u63CF\u7ED3\u679C\u4F20\u8F93\u6A21\u5F0F\uFF1A\u5185\u5B58\u3001\u6587\u4EF6\u3001\u539F\u751F"
          },
          {
            category: "\u56FE\u50CF\u7F16\u8F91\u7279\u6027",
            value: "1. \u652F\u6301\u56FE\u50CF\u5411\u5DE6\u3001\u5411\u53F3\u65CB\u8F6C 90 \u5EA6 2. \u652F\u6301\u56FE\u50CF\u81EA\u5B9A\u4E49\u89D2\u5EA6\u65CB\u8F6C 3. \u652F\u6301\u9B54\u672F\u68D2\u9009\u62E9\u6A21\u5F0F\u56FE\u50CF\u9009\u62E9 4. \u652F\u6301\u77E9\u5F62\u56FE\u50CF\u9009\u62E9 5. \u652F\u6301\u9009\u4E2D\u533A\u57DF\u586B\u767D\u5904\u7406 6. \u652F\u6301\u9009\u4E2D\u533A\u57DF\u53CD\u9009\u586B\u767D\u5904\u7406 7. \u652F\u6301\u9009\u4E2D\u533A\u57DF\u9A6C\u8D5B\u514B\u5904\u7406 8. \u652F\u6301\u88C1\u526A\u9009\u533A\u4EE5\u5916\u6240\u6709\u56FE\u50CF\u5904\u7406 9. \u652F\u6301\u9009\u4E2D\u533A\u57DF\u5F52\u7EA2\u3001\u5F52\u7EFF\u5904\u7406 10. \u652F\u6301\u53BB\u9664\u56FE\u50CF\u9ED1\u8FB9\u5904\u7406 11. \u652F\u6301\u53BB\u9664\u56FE\u50CF\u5E95\u8272\u5904\u7406 12. UNDO \u64A4\u9500\u64CD\u4F5C 13. \u5BA2\u6237\u7AEF\u5355\u9875\u672C\u5730\u56FE\u50CF\u4FDD\u5B58 14. \u5BA2\u6237\u7AEF\u5355\u9875\u672C\u5730\u56FE\u50CF\u6253\u5370\u53CA\u6253\u5370\u9884\u89C8 15. \u6279\u91CF\u56FE\u50CF\u5220\u9664 16. \u591A\u9875\u626B\u63CF\u7ED3\u679C\u6392\u5E8F\uFF08\u76F4\u63A5\u62D6\u62FD\u987A\u5E8F\uFF09 17. \u626B\u63CF\u56FE\u50CF\u5220\u9664\u5355\u9875\u5904\u7406 18. \u5BA2\u6237\u7AEF\u591A\u9875\u56FE\u50CF\u4FDD\u5B58 19. \u5BA2\u6237\u7AEF\u591A\u9875\u56FE\u50CF\u6253\u5370\u673A\u6253\u5370\u9884\u89C8 20. \u9F20\u6807\u6EDA\u8F6E\u7F29\u653E\u56FE\u50CF"
          },
          {
            category: "\u56FE\u50CF\u4E0A\u4F20\u80FD\u529B",
            value: "1. \u56FE\u50CF\u8F6Cjpg base64\u7F16\u7801\uFF0C\u4F9B\u524D\u7AEF\u663E\u793A\u6216\u4E0A\u4F20 2. \u56FE\u50CF\u6309\u7167 tiff \u683C\u5F0F\u4E0A\u4F20\u5230\u6307\u5B9A url 3. \u56FE\u50CF\u6309\u7167 pdf \u683C\u5F0F\u4E0A\u4F20\u5230\u6307\u5B9A url 4. \u56FE\u50CF\u6309\u7167\u591A\u9875 jpg \u65B9\u5F0F\u4E0A\u4F20\u5230\u6307\u5B9A url 5. \u5355\u72EC\u5C06\u67D0\u4E00\u9875\u56FE\u50CF\u4EE5 jpg \u65B9\u5F0F\u4E0A\u4F20\u5230\u6307\u5B9A url 6. \u4E0A\u4F20\u5230\u6307\u5B9A\u7684 sftp \u5730\u5740\uFF08\u9700\u5B9A\u5236\uFF09"
          }
        ]
      },
      {
        id: 2,
        name: "ImageCapOnWeb",
        description: "ImageCapOnWeb\u63A7\u4EF6\u7528\u4E8E\u5904\u7406\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\uFF0C\u9002\u5408\u7528\u4E8Eweb\u73AF\u5883\u4E0B\u7684\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u5E94\u7528\uFF0C\u53EF\u65E0\u7F1D\u96C6\u6210\u5230jsp\u3001php\u3001asp.net\u7B49\u7F16\u7A0B\u6280\u672F\u5F53\u4E2D\u3002\u63A7\u4EF6\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u5927\u90E8\u5206\u6444\u50CF\u5934\u6570\u7801\u8BBE\u5907\u3002\u76EE\u524D\u5DF2\u7ECF\u5728\u591A\u6240\u5B66\u6821\u3001\u94F6\u884C\u3001\u653F\u5E9C\u673A\u6784\u4E2D\u8FDB\u884C\u96C6\u6210\u5E94\u7528\u3002",
        image: "/images/slide2.png",
        features: [
          "\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6",
          "Web\u73AF\u5883\u4E0B\u7684\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B",
          "\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u5927\u90E8\u5206\u6444\u50CF\u5934\u8BBE\u5907",
          "\u65E0\u7F1D\u96C6\u6210\u5230\u591A\u79CD\u7F16\u7A0B\u6280\u672F"
        ],
        specs: {
          \u517C\u5BB9\u5E73\u53F0: "Windows\u5E73\u53F0",
          \u652F\u6301\u8BBE\u5907: "\u5927\u90E8\u5206\u6444\u50CF\u5934\u6570\u7801\u8BBE\u5907",
          \u96C6\u6210\u6280\u672F: "jsp\u3001php\u3001asp.net\u7B49"
        },
        useCases: ["\u5B66\u6821\u4FE1\u606F\u7CFB\u7EDF", "\u94F6\u884C\u4E1A\u52A1\u7CFB\u7EDF", "\u653F\u5E9C\u673A\u6784\u5E94\u7528"],
        pricing: "\u8BF7\u4E0B\u8F7D\u62A5\u4EF7\u5355\u4E86\u89E3\u8BE6\u60C5",
        videoUrl: "https://example.com/imagecaponweb-demo.mp4",
        pdfUrl: "/ImageCapOnWeb-quotation.pdf"
      },
      {
        id: 3,
        name: "GaoPaiYi",
        description: "GaoPaiYi\u63A7\u4EF6\u7528\u4E8E\u5904\u7406\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\uFF0C\u9002\u5408\u7528\u4E8Eweb\u73AF\u5883\u4E0B\u7684\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u5E94\u7528\uFF0C\u53EF\u65E0\u7F1D\u96C6\u6210\u5230jsp\u3001php\u3001asp.net\u7B49\u7F16\u7A0B\u6280\u672F\u5F53\u4E2D\u3002\u63A7\u4EF6\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u4EE5\u6444\u50CF\u5934\u4E3A\u6838\u5FC3\u8BBE\u5907\u7684\u9AD8\u62CD\u4EEA\u4EA7\u54C1\u3002",
        image: "/images/slide3.png",
        features: [
          "\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6",
          "Web\u73AF\u5883\u4E0B\u7684\u7F16\u7A0B\u5E94\u7528",
          "\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u9AD8\u62CD\u4EEA\u4EA7\u54C1",
          "\u65E0\u7F1D\u96C6\u6210\u5230\u5404\u79CD\u7F16\u7A0B\u6280\u672F"
        ],
        specs: {
          \u517C\u5BB9\u5E73\u53F0: "Windows\u5E73\u53F0",
          \u652F\u6301\u8BBE\u5907: "\u4EE5\u6444\u50CF\u5934\u4E3A\u6838\u5FC3\u8BBE\u5907\u7684\u9AD8\u62CD\u4EEA\u4EA7\u54C1",
          \u96C6\u6210\u6280\u672F: "jsp\u3001php\u3001asp.net\u7B49"
        },
        useCases: ["\u6587\u6863\u626B\u63CF", "\u8BC1\u4EF6\u91C7\u96C6", "\u6559\u80B2\u57F9\u8BAD"],
        pricing: "\u8BF7\u4E0B\u8F7D\u62A5\u4EF7\u5355\u4E86\u89E3\u8BE6\u60C5",
        videoUrl: "https://example.com/gaopaiyi-demo.mp4",
        pdfUrl: "/GaoPaiYi-quotation.pdf"
      }
    ]);
    const selectedProduct = ref(products2.value[0] || null);
    const selectProduct = (product) => {
      selectedProduct.value = product;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-100 min-h-screen" }, _attrs))} data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$1$1, null, null, _parent));
      _push(`<main class="container mx-auto py-12 px-4" data-v-9e2630bc><div class="flex flex-col lg:flex-row gap-8" data-v-9e2630bc><div class="lg:w-1/4" data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$2, {
        products: products2.value,
        selectedProductId: (_a = selectedProduct.value) == null ? void 0 : _a.id,
        onSelect: selectProduct
      }, null, _parent));
      _push(`</div><div class="lg:w-3/4" data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$1, {
        key: (_b = selectedProduct.value) == null ? void 0 : _b.id,
        product: selectedProduct.value
      }, null, _parent));
      _push(`</div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$3, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/products.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const products = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-9e2630bc"]]);

export { products as default };
//# sourceMappingURL=products-BQPRZ3xz.mjs.map
