import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { u as useHead } from './index-BabADJUJ.mjs';
import { mergeProps, withCtx, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '@unhead/shared';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import 'vue-router';

const _sfc_main = {
  __name: "scanonweb-faq",
  __ssrInlineRender: true,
  setup(__props) {
    useHead({
      title: "ScanOnWeb \u5E38\u89C1\u95EE\u9898 - \u9A71\u52A8\u5B89\u88C5\u4E0E\u6545\u969C\u6392\u9664",
      meta: [
        {
          name: "description",
          content: "ScanOnWeb\u626B\u63CF\u63A7\u4EF6\u5E38\u89C1\u95EE\u9898\u89E3\u7B54\uFF0C\u5305\u542BWindows TWAIN\u9A71\u52A8\u3001Linux SANE\u9A71\u52A8\u5B89\u88C5\uFF0C\u7AEF\u53E3\u5360\u7528\u6392\u67E5\uFF0C\u6743\u9650\u914D\u7F6E\u7B49\u8BE6\u7EC6\u89E3\u51B3\u65B9\u6848\u3002"
        },
        {
          name: "keywords",
          content: "ScanOnWeb,\u5E38\u89C1\u95EE\u9898,TWAIN\u9A71\u52A8,SANE\u9A71\u52A8,\u7AEF\u53E3\u5360\u7528,Linux\u6743\u9650,\u626B\u63CF\u4EEA\u9A71\u52A8,\u6545\u969C\u6392\u9664"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-13cbb726>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-13cbb726><div class="bg-white border-b border-gray-200" data-v-13cbb726><div class="container mx-auto px-4 py-4" data-v-13cbb726><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-13cbb726>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-13cbb726><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-13cbb726></path></svg>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u6587\u6863\u8D44\u6599`);
          } else {
            return [
              createTextVNode("\u6587\u6863\u8D44\u6599")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-13cbb726><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-13cbb726></path></svg><span class="text-gray-900 font-medium" data-v-13cbb726>ScanOnWeb \u5E38\u89C1\u95EE\u9898</span></nav></div></div><div class="bg-white py-8" data-v-13cbb726><div class="container mx-auto px-4" data-v-13cbb726><div class="max-w-4xl" data-v-13cbb726><h1 class="heading-primary mb-4" data-v-13cbb726>ScanOnWeb \u5E38\u89C1\u95EE\u9898</h1><p class="text-lg text-gray-600 mb-6" data-v-13cbb726> \u89E3\u51B3ScanOnWeb\u626B\u63CF\u63A7\u4EF6\u4F7F\u7528\u8FC7\u7A0B\u4E2D\u7684\u5E38\u89C1\u6280\u672F\u95EE\u9898\uFF0C\u5305\u542B\u9A71\u52A8\u5B89\u88C5\u3001\u7AEF\u53E3\u914D\u7F6E\u3001\u6743\u9650\u8BBE\u7F6E\u7B49\u8BE6\u7EC6\u89E3\u51B3\u65B9\u6848 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-13cbb726><div class="flex items-center" data-v-13cbb726><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-13cbb726><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-13cbb726></path></svg><span data-v-13cbb726>\u9A71\u52A8\u5B89\u88C5\u6307\u5357</span></div><div class="flex items-center" data-v-13cbb726><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-13cbb726><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-13cbb726></path></svg><span data-v-13cbb726>\u7AEF\u53E3\u95EE\u9898\u6392\u67E5</span></div><div class="flex items-center" data-v-13cbb726><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-13cbb726><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-13cbb726></path></svg><span data-v-13cbb726>\u6743\u9650\u914D\u7F6E</span></div></div></div></div></div><div class="container mx-auto py-12 px-4" data-v-13cbb726><div class="card-business p-6 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-4" data-v-13cbb726>\u95EE\u9898\u5206\u7C7B</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-13cbb726><a href="#windows-drivers" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>Windows\u9A71\u52A8\u5B89\u88C5</div><div class="text-sm text-gray-600" data-v-13cbb726>TWAIN\u9A71\u52A8\u7A0B\u5E8F\u5B89\u88C5\u914D\u7F6E</div></a><a href="#linux-drivers" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>Linux\u9A71\u52A8\u5B89\u88C5</div><div class="text-sm text-gray-600" data-v-13cbb726>SANE\u9A71\u52A8\u7A0B\u5E8F\u5B89\u88C5\u914D\u7F6E</div></a><a href="#port-issues" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>\u7AEF\u53E3\u95EE\u9898</div><div class="text-sm text-gray-600" data-v-13cbb726>WebSocket\u7AEF\u53E3\u5360\u7528\u6392\u67E5</div></a><a href="#device-support" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>\u8BBE\u5907\u517C\u5BB9\u6027</div><div class="text-sm text-gray-600" data-v-13cbb726>\u534F\u8BAE\u652F\u6301\u68C0\u6D4B\u65B9\u6CD5</div></a><a href="#linux-permissions" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>Linux\u6743\u9650\u914D\u7F6E</div><div class="text-sm text-gray-600" data-v-13cbb726>USB\u8BBE\u5907udev\u6743\u9650\u8BBE\u7F6E</div></a><a href="#network-scanner" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-13cbb726><div class="font-medium text-gray-900" data-v-13cbb726>\u7F51\u7EDC\u626B\u63CF\u4EEA</div><div class="text-sm text-gray-600" data-v-13cbb726>saned\u670D\u52A1\u914D\u7F6E</div></a></div></div><div id="windows-drivers" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>Windows\u5E73\u53F0\u9A71\u52A8\u5B89\u88C5</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u5B89\u88C5TWAIN\u9A71\u52A8\u7A0B\u5E8F\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> TWAIN\u662FWindows\u5E73\u53F0\u4E0A\u6700\u5E38\u7528\u7684\u626B\u63CF\u4EEA\u9A71\u52A8\u6807\u51C6\uFF0C\u5927\u591A\u6570\u626B\u63CF\u4EEA\u5382\u5546\u90FD\u63D0\u4F9BTWAIN\u9A71\u52A8\u7A0B\u5E8F\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>\u5B89\u88C5\u6B65\u9AA4\uFF1A</h4><ol class="step-list" data-v-13cbb726><li data-v-13cbb726><strong data-v-13cbb726>\u4E0B\u8F7D\u5B98\u65B9\u9A71\u52A8\uFF1A</strong><p data-v-13cbb726> \u8BBF\u95EE\u626B\u63CF\u4EEA\u5382\u5546\u5B98\u7F51\uFF0C\u4E0B\u8F7D\u5BF9\u5E94\u578B\u53F7\u7684TWAIN\u9A71\u52A8\u7A0B\u5E8F\u3002\u786E\u4FDD\u9A71\u52A8\u7248\u672C\u652F\u6301\u60A8\u7684Windows\u7CFB\u7EDF\uFF0832\u4F4D/64\u4F4D\uFF09\u3002 </p></li><li data-v-13cbb726><strong data-v-13cbb726>\u4EE5\u7BA1\u7406\u5458\u8EAB\u4EFD\u5B89\u88C5\uFF1A</strong><p data-v-13cbb726> \u53F3\u952E\u70B9\u51FB\u9A71\u52A8\u5B89\u88C5\u7A0B\u5E8F\uFF0C\u9009\u62E9&quot;\u4EE5\u7BA1\u7406\u5458\u8EAB\u4EFD\u8FD0\u884C&quot;\uFF0C\u6309\u7167\u5B89\u88C5\u5411\u5BFC\u5B8C\u6210\u5B89\u88C5\u3002 </p></li><li data-v-13cbb726><strong data-v-13cbb726>\u8FDE\u63A5\u8BBE\u5907\uFF1A</strong><p data-v-13cbb726>\u4F7F\u7528USB\u7EBF\u7F06\u8FDE\u63A5\u626B\u63CF\u4EEA\u5230\u8BA1\u7B97\u673A\uFF0C\u786E\u4FDD\u8BBE\u5907\u7535\u6E90\u5DF2\u5F00\u542F\u3002</p></li><li data-v-13cbb726><strong data-v-13cbb726>\u9A8C\u8BC1\u5B89\u88C5\uFF1A</strong><p data-v-13cbb726> \u6253\u5F00&quot;\u8BBE\u5907\u7BA1\u7406\u5668&quot;\uFF0C\u5728&quot;\u56FE\u50CF\u8BBE\u5907&quot;\u5206\u7C7B\u4E0B\u5E94\u8BE5\u80FD\u770B\u5230\u60A8\u7684\u626B\u63CF\u4EEA\u8BBE\u5907\uFF0C\u4E14\u6CA1\u6709\u9EC4\u8272\u8B66\u544A\u56FE\u6807\u3002 </p></li></ol></div><div class="code-example" data-v-13cbb726><h4 class="code-title" data-v-13cbb726>\u68C0\u67E5TWAIN\u9A71\u52A8\u662F\u5426\u6B63\u786E\u5B89\u88C5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5728\u547D\u4EE4\u63D0\u793A\u7B26\u4E2D\u8FD0\u884C\u4EE5\u4E0B\u547D\u4EE4\u68C0\u67E5TWAIN\u6570\u636E\u6E90
# \u6253\u5F00\u6CE8\u518C\u8868\u7F16\u8F91\u5668
regedit

# \u5BFC\u822A\u5230\u4EE5\u4E0B\u8DEF\u5F84\u67E5\u770B\u5DF2\u5B89\u88C5\u7684TWAIN\u9A71\u52A8
HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}\\Instance

# \u6216\u8005\u4F7F\u7528PowerShell\u67E5\u770B
Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like &quot;*scanner*&quot; -or $_.Name -like &quot;*imaging*&quot;}</pre></div></div><div class="warning-box" data-v-13cbb726><h4 class="warning-title" data-v-13cbb726>\u5E38\u89C1\u95EE\u9898\uFF1A</h4><ul class="warning-list" data-v-13cbb726><li data-v-13cbb726>\u5982\u679C\u8BBE\u5907\u7BA1\u7406\u5668\u4E2D\u663E\u793A&quot;\u672A\u77E5\u8BBE\u5907&quot;\uFF0C\u8BF4\u660E\u9A71\u52A8\u672A\u6B63\u786E\u5B89\u88C5</li><li data-v-13cbb726> \u67D0\u4E9B\u8001\u65E7\u626B\u63CF\u4EEA\u53EF\u80FD\u4E0D\u652F\u6301Windows 10/11\uFF0C\u9700\u8981\u4F7F\u7528\u517C\u5BB9\u6A21\u5F0F </li><li data-v-13cbb726>\u5B89\u88C5\u9A71\u52A8\u524D\u5EFA\u8BAE\u5148\u5378\u8F7D\u65E7\u7248\u672C\u9A71\u52A8\uFF0C\u907F\u514D\u51B2\u7A81</li></ul></div></div></div><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>WIA\u9A71\u52A8\u4E0ETWAIN\u9A71\u52A8\u7684\u533A\u522B\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> WIA\uFF08Windows Image Acquisition\uFF09\u662F\u5FAE\u8F6F\u5F00\u53D1\u7684\u56FE\u50CF\u83B7\u53D6\u6807\u51C6\uFF0C\u800CTWAIN\u662F\u7B2C\u4E09\u65B9\u6807\u51C6\u3002 </p><div class="comparison-table" data-v-13cbb726><table class="w-full border-collapse border border-gray-300" data-v-13cbb726><thead data-v-13cbb726><tr class="bg-gray-100" data-v-13cbb726><th class="border border-gray-300 px-4 py-2" data-v-13cbb726>\u7279\u6027</th><th class="border border-gray-300 px-4 py-2" data-v-13cbb726>TWAIN</th><th class="border border-gray-300 px-4 py-2" data-v-13cbb726>WIA</th></tr></thead><tbody data-v-13cbb726><tr data-v-13cbb726><td class="border border-gray-300 px-4 py-2 font-medium" data-v-13cbb726> \u517C\u5BB9\u6027 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u8DE8\u5E73\u53F0\u652F\u6301\uFF0C\u529F\u80FD\u4E30\u5BCC </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u4EC5Windows\u5E73\u53F0 </td></tr><tr data-v-13cbb726><td class="border border-gray-300 px-4 py-2 font-medium" data-v-13cbb726> \u5B89\u88C5\u590D\u6742\u5EA6 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u9700\u8981\u5382\u5546\u63D0\u4F9B\u4E13\u7528\u9A71\u52A8 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> Windows\u5185\u7F6E\u652F\u6301 </td></tr><tr data-v-13cbb726><td class="border border-gray-300 px-4 py-2 font-medium" data-v-13cbb726> \u529F\u80FD\u652F\u6301 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u529F\u80FD\u5168\u9762\uFF0C\u652F\u6301\u9AD8\u7EA7\u7279\u6027 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u57FA\u7840\u529F\u80FD\uFF0C\u7B80\u5355\u6613\u7528 </td></tr><tr data-v-13cbb726><td class="border border-gray-300 px-4 py-2 font-medium" data-v-13cbb726> ScanOnWeb\u652F\u6301 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726> \u2705 \u5B8C\u5168\u652F\u6301 </td><td class="border border-gray-300 px-4 py-2" data-v-13cbb726>\u2705 \u652F\u6301</td></tr></tbody></table></div><div class="tip-box" data-v-13cbb726><h4 class="tip-title" data-v-13cbb726>\u63A8\u8350\u65B9\u6848\uFF1A</h4><p data-v-13cbb726> \u4F18\u5148\u4F7F\u7528TWAIN\u9A71\u52A8\uFF0C\u5982\u679C\u5382\u5546\u672A\u63D0\u4F9BTWAIN\u9A71\u52A8\uFF0C\u53EF\u4EE5\u5C1D\u8BD5\u4F7F\u7528WIA\u9A71\u52A8\u3002ScanOnWeb\u63A7\u4EF6\u5BF9\u4E24\u79CD\u9A71\u52A8\u90FD\u6709\u826F\u597D\u652F\u6301\u3002 </p></div></div></div></div><div id="linux-drivers" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>Linux\u5E73\u53F0\u9A71\u52A8\u5B89\u88C5</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u5B89\u88C5SANE\u9A71\u52A8\u7A0B\u5E8F\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> SANE\uFF08Scanner Access Now Easy\uFF09\u662FLinux\u5E73\u53F0\u4E0A\u7684\u6807\u51C6\u626B\u63CF\u4EEA\u63A5\u53E3\uFF0C\u652F\u6301\u5927\u591A\u6570\u4E3B\u6D41\u626B\u63CF\u4EEA\u54C1\u724C\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Ubuntu/Debian\u7CFB\u7EDF\u5B89\u88C5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u66F4\u65B0\u8F6F\u4EF6\u5305\u5217\u8868
sudo apt update

# \u5B89\u88C5SANE\u6838\u5FC3\u5305
sudo apt install sane-utils libsane-extras

# \u5B89\u88C5\u5E38\u7528\u626B\u63CF\u4EEA\u9A71\u52A8
sudo apt install libsane-hpaio  # HP\u626B\u63CF\u4EEA
sudo apt install sane-airscan   # \u7F51\u7EDC\u626B\u63CF\u4EEA\u652F\u6301

# \u68C0\u67E5SANE\u7248\u672C
sane-find-scanner --version</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>CentOS/RHEL\u7CFB\u7EDF\u5B89\u88C5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5B89\u88C5EPEL\u4ED3\u5E93
sudo yum install epel-release

# \u5B89\u88C5SANE\u5305
sudo yum install sane-backends sane-frontends

# \u6216\u8005\u5728\u8F83\u65B0\u7248\u672C\u4E2D\u4F7F\u7528dnf
sudo dnf install sane-backends sane-frontends</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>\u9A8C\u8BC1\u5B89\u88C5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u626B\u63CF\u68C0\u6D4B\u53EF\u7528\u7684\u626B\u63CF\u4EEA
sudo sane-find-scanner

# \u5217\u51FASANE\u652F\u6301\u7684\u8BBE\u5907
scanimage -L

# \u6D4B\u8BD5\u626B\u63CF\u529F\u80FD
scanimage --test</pre></div></div></div></div></div><div id="port-issues" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>WebSocket\u7AEF\u53E3\u95EE\u9898\u6392\u67E5</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u68C0\u67E5ScanOnWeb\u670D\u52A1\u7AEF\u53E3\u662F\u5426\u88AB\u5360\u7528\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> ScanOnWeb\u6258\u76D8\u670D\u52A1\u9ED8\u8BA4\u4F7F\u7528\u7AEF\u53E31001-5001\u8303\u56F4\u5185\u7684\u7AEF\u53E3\uFF0C\u5982\u679C\u7AEF\u53E3\u88AB\u5360\u7528\u4F1A\u5BFC\u81F4\u8FDE\u63A5\u5931\u8D25\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Windows\u7CFB\u7EDF\u68C0\u67E5\u65B9\u6CD5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u68C0\u67E5\u7279\u5B9A\u7AEF\u53E3\u662F\u5426\u88AB\u5360\u7528
netstat -ano | findstr :1001
netstat -ano | findstr :2001
netstat -ano | findstr :3001

# \u67E5\u770B\u6240\u6709\u76D1\u542C\u7AEF\u53E3
netstat -ano | findstr LISTENING

# \u4F7F\u7528PowerShell\u68C0\u67E5\u7AEF\u53E3
Get-NetTCPConnection -LocalPort 1001,2001,3001 -State Listen

# \u68C0\u67E5\u8FDB\u7A0B\u5360\u7528\u7AEF\u53E3\u60C5\u51B5
tasklist /fi &quot;pid eq [PID]&quot;</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Linux\u7CFB\u7EDF\u68C0\u67E5\u65B9\u6CD5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u68C0\u67E5\u7AEF\u53E3\u5360\u7528\u60C5\u51B5
sudo netstat -tulpn | grep :1001
sudo netstat -tulpn | grep :2001
sudo netstat -tulpn | grep :3001

# \u4F7F\u7528ss\u547D\u4EE4\u68C0\u67E5
sudo ss -tulpn | grep :1001

# \u4F7F\u7528lsof\u68C0\u67E5\u7AEF\u53E3
sudo lsof -i :1001

# \u68C0\u67E5ScanOnWeb\u670D\u52A1\u72B6\u6001
ps aux | grep scanonweb</pre></div></div><div class="warning-box" data-v-13cbb726><h4 class="warning-title" data-v-13cbb726>\u7AEF\u53E3\u51B2\u7A81\u89E3\u51B3\u65B9\u6848\uFF1A</h4><ul class="warning-list" data-v-13cbb726><li data-v-13cbb726>\u5173\u95ED\u5360\u7528\u7AEF\u53E3\u7684\u5176\u4ED6\u7A0B\u5E8F</li><li data-v-13cbb726>\u91CD\u542FScanOnWeb\u6258\u76D8\u670D\u52A1</li><li data-v-13cbb726>\u4FEE\u6539ScanOnWeb\u914D\u7F6E\u6587\u4EF6\u6307\u5B9A\u5176\u4ED6\u7AEF\u53E3</li><li data-v-13cbb726>\u4F7F\u7528\u9632\u706B\u5899\u89C4\u5219\u5141\u8BB8ScanOnWeb\u7AEF\u53E3\u901A\u4FE1</li></ul></div></div></div><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u914D\u7F6E\u9632\u706B\u5899\u5141\u8BB8ScanOnWeb\u901A\u4FE1\uFF1F</h3><div class="faq-answer" data-v-13cbb726><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Windows\u9632\u706B\u5899\u914D\u7F6E\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u4F7F\u7528\u547D\u4EE4\u884C\u6DFB\u52A0\u9632\u706B\u5899\u89C4\u5219
netsh advfirewall firewall add rule name=&quot;ScanOnWeb&quot; dir=in action=allow protocol=TCP localport=1001-5001

# \u6216\u8005\u901A\u8FC7\u56FE\u5F62\u754C\u9762\uFF1A
# 1. \u6253\u5F00&quot;Windows Defender \u9632\u706B\u5899&quot;
# 2. \u70B9\u51FB&quot;\u9AD8\u7EA7\u8BBE\u7F6E&quot;
# 3. \u9009\u62E9&quot;\u5165\u7AD9\u89C4\u5219&quot; -&gt; &quot;\u65B0\u5EFA\u89C4\u5219&quot;
# 4. \u9009\u62E9&quot;\u7AEF\u53E3&quot; -&gt; &quot;TCP&quot; -&gt; &quot;\u7279\u5B9A\u672C\u5730\u7AEF\u53E3&quot;
# 5. \u8F93\u5165\u7AEF\u53E3\u8303\u56F4\uFF1A1001-5001</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Linux\u9632\u706B\u5899\u914D\u7F6E\uFF08iptables\uFF09\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5141\u8BB8ScanOnWeb\u7AEF\u53E3
sudo iptables -A INPUT -p tcp --dport 1001:5001 -j ACCEPT

# \u4FDD\u5B58\u89C4\u5219\uFF08Ubuntu/Debian\uFF09
sudo iptables-save &gt; /etc/iptables/rules.v4

# CentOS/RHEL\u4FDD\u5B58\u89C4\u5219
sudo service iptables save</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Linux\u9632\u706B\u5899\u914D\u7F6E\uFF08firewalld\uFF09\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u6DFB\u52A0\u7AEF\u53E3\u8303\u56F4
sudo firewall-cmd --permanent --add-port=1001-5001/tcp

# \u91CD\u65B0\u52A0\u8F7D\u914D\u7F6E
sudo firewall-cmd --reload

# \u68C0\u67E5\u914D\u7F6E
sudo firewall-cmd --list-ports</pre></div></div></div></div></div><div id="device-support" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>\u8BBE\u5907\u517C\u5BB9\u6027\u68C0\u6D4B</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u5224\u65AD\u626B\u63CF\u4EEA\u662F\u5426\u652F\u6301TWAIN\u534F\u8BAE\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726>TWAIN\u534F\u8BAE\u652F\u6301\u68C0\u6D4B\u53EF\u4EE5\u901A\u8FC7\u591A\u79CD\u65B9\u5F0F\u8FDB\u884C\u9A8C\u8BC1\u3002</p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Windows\u7CFB\u7EDF\u68C0\u6D4B\u65B9\u6CD5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># 1. \u68C0\u67E5\u8BBE\u5907\u7BA1\u7406\u5668
# \u6253\u5F00\u8BBE\u5907\u7BA1\u7406\u5668\uFF0C\u67E5\u770B&quot;\u56FE\u50CF\u8BBE\u5907&quot;\u5206\u7C7B
# \u652F\u6301TWAIN\u7684\u8BBE\u5907\u901A\u5E38\u663E\u793A\u4E3A&quot;[\u54C1\u724C] [\u578B\u53F7] TWAIN&quot;

# 2. \u68C0\u67E5\u6CE8\u518C\u8868TWAIN\u6570\u636E\u6E90
# \u6253\u5F00\u6CE8\u518C\u8868\u7F16\u8F91\u5668\uFF0C\u5BFC\u822A\u5230\uFF1A
HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}\\Instance

# 3. \u4F7F\u7528TWAIN\u6D4B\u8BD5\u5DE5\u5177
# \u4E0B\u8F7DTWAIN Sample Application\u8FDB\u884C\u6D4B\u8BD5
# \u6216\u4F7F\u7528Windows\u81EA\u5E26\u7684&quot;Windows\u4F20\u771F\u548C\u626B\u63CF&quot;\u7A0B\u5E8F

# 4. PowerShell\u68C0\u6D4B\u811A\u672C
Get-WmiObject -Class Win32_PnPEntity | Where-Object {
    $_.Name -like &quot;*scanner*&quot; -and $_.Status -eq &quot;OK&quot;
} | Select-Object Name, DeviceID, Status</pre></div></div><div class="tip-box" data-v-13cbb726><h4 class="tip-title" data-v-13cbb726>TWAIN\u517C\u5BB9\u6027\u6807\u8BC6\uFF1A</h4><ul class="list-disc list-inside space-y-1 text-blue-700" data-v-13cbb726><li data-v-13cbb726>\u8BBE\u5907\u540D\u79F0\u5305\u542B&quot;TWAIN&quot;\u5B57\u6837</li><li data-v-13cbb726>\u5382\u5546\u5B98\u7F51\u660E\u786E\u6807\u6CE8TWAIN\u652F\u6301</li><li data-v-13cbb726>\u9A71\u52A8\u5B89\u88C5\u5305\u5305\u542BTWAIN\u7EC4\u4EF6</li><li data-v-13cbb726>\u53EF\u4EE5\u5728\u56FE\u50CF\u7F16\u8F91\u8F6F\u4EF6\u4E2D\u901A\u8FC7&quot;\u83B7\u53D6&quot;\u83DC\u5355\u8BBF\u95EE</li></ul></div></div></div><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u5224\u65AD\u626B\u63CF\u4EEA\u662F\u5426\u652F\u6301SANE\u534F\u8BAE\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> SANE\u534F\u8BAE\u652F\u6301\u68C0\u6D4B\u4E3B\u8981\u901A\u8FC7\u547D\u4EE4\u884C\u5DE5\u5177\u548C\u5B98\u65B9\u517C\u5BB9\u6027\u5217\u8868\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>Linux\u7CFB\u7EDF\u68C0\u6D4B\u65B9\u6CD5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># 1. \u4F7F\u7528sane-find-scanner\u68C0\u6D4B\u786C\u4EF6
sudo sane-find-scanner

# 2. \u5217\u51FASANE\u652F\u6301\u7684\u8BBE\u5907
scanimage -L

# 3. \u68C0\u67E5\u8BBE\u5907\u8BE6\u7EC6\u4FE1\u606F
scanimage --help -d [\u8BBE\u5907\u540D\u79F0]

# 4. \u67E5\u770BUSB\u8BBE\u5907\u4FE1\u606F
lsusb | grep -i scanner
lsusb -v | grep -A 10 -B 5 &quot;Scanner\\|Imaging&quot;

# 5. \u68C0\u67E5SANE\u540E\u7AEF\u652F\u6301
ls /usr/lib/sane/ | grep -i [\u5382\u5546\u540D\u79F0]

# 6. \u6D4B\u8BD5\u8BBE\u5907\u8FDE\u63A5
scanimage --test -d [\u8BBE\u5907\u540D\u79F0]</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>\u67E5\u770BSANE\u5B98\u65B9\u517C\u5BB9\u6027\u5217\u8868\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u8BBF\u95EESANE\u5B98\u65B9\u652F\u6301\u5217\u8868
# http://www.sane-project.org/sane-supported-devices.html

# \u6216\u8005\u4F7F\u7528\u547D\u4EE4\u67E5\u770B\u672C\u5730\u652F\u6301\u5217\u8868
man sane-[\u5382\u5546\u540D\u79F0]

# \u4F8B\u5982\uFF1A
man sane-epson2
man sane-hp
man sane-canon</pre></div></div><div class="warning-box" data-v-13cbb726><h4 class="warning-title" data-v-13cbb726>\u5E38\u89C1\u4E0D\u652F\u6301\u7684\u60C5\u51B5\uFF1A</h4><ul class="warning-list" data-v-13cbb726><li data-v-13cbb726>\u67D0\u4E9B\u65B0\u6B3E\u626B\u63CF\u4EEA\u53EF\u80FD\u9700\u8981\u7B49\u5F85SANE\u540E\u7AEF\u66F4\u65B0</li><li data-v-13cbb726>\u4E13\u4E1A\u9AD8\u7AEF\u626B\u63CF\u4EEA\u53EF\u80FD\u53EA\u63D0\u4F9B\u5382\u5546\u4E13\u7528\u9A71\u52A8</li><li data-v-13cbb726>\u67D0\u4E9B\u591A\u529F\u80FD\u4E00\u4F53\u673A\u7684\u626B\u63CF\u529F\u80FD\u53EF\u80FD\u4E0D\u88AB\u5B8C\u5168\u652F\u6301</li></ul></div></div></div></div><div id="linux-permissions" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>Linux USB\u8BBE\u5907\u6743\u9650\u914D\u7F6E</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u8BBE\u7F6ELinux\u4E0BUSB\u626B\u63CF\u4EEA\u7684udev\u6743\u9650\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> Linux\u7CFB\u7EDF\u9700\u8981\u6B63\u786E\u7684udev\u89C4\u5219\u624D\u80FD\u8BA9\u666E\u901A\u7528\u6237\u8BBF\u95EEUSB\u626B\u63CF\u4EEA\u8BBE\u5907\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>1. \u67E5\u770B\u8BBE\u5907\u4FE1\u606F\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u8FDE\u63A5\u626B\u63CF\u4EEA\u540E\u67E5\u770BUSB\u8BBE\u5907
lsusb

# \u67E5\u770B\u8BE6\u7EC6\u8BBE\u5907\u4FE1\u606F
lsusb -v | grep -A 10 -B 5 &quot;Scanner\\|Imaging&quot;

# \u83B7\u53D6\u8BBE\u5907\u7684Vendor ID\u548CProduct ID
# \u4F8B\u5982\u8F93\u51FA\uFF1ABus 001 Device 003: ID 04b8:0142 Seiko Epson Corp.
# \u5176\u4E2D04b8\u662FVendor ID\uFF0C0142\u662FProduct ID</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>2. \u521B\u5EFAudev\u89C4\u5219\u6587\u4EF6\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u521B\u5EFAudev\u89C4\u5219\u6587\u4EF6
sudo nano /etc/udev/rules.d/99-scanner.rules

# \u6DFB\u52A0\u4EE5\u4E0B\u5185\u5BB9\uFF08\u66FF\u6362\u4E3A\u5B9E\u9645\u7684Vendor ID\u548CProduct ID\uFF09\uFF1A
# Epson\u626B\u63CF\u4EEA\u793A\u4F8B
SUBSYSTEM==&quot;usb&quot;, ATTR{idVendor}==&quot;04b8&quot;, ATTR{idProduct}==&quot;0142&quot;, MODE=&quot;0666&quot;, GROUP=&quot;scanner&quot;

# HP\u626B\u63CF\u4EEA\u793A\u4F8B
SUBSYSTEM==&quot;usb&quot;, ATTR{idVendor}==&quot;03f0&quot;, MODE=&quot;0666&quot;, GROUP=&quot;scanner&quot;

# Canon\u626B\u63CF\u4EEA\u793A\u4F8B
SUBSYSTEM==&quot;usb&quot;, ATTR{idVendor}==&quot;04a9&quot;, MODE=&quot;0666&quot;, GROUP=&quot;scanner&quot;

# \u901A\u7528\u89C4\u5219\uFF08\u9002\u7528\u4E8E\u6240\u6709\u626B\u63CF\u4EEA\uFF09
SUBSYSTEM==&quot;usb&quot;, ENV{libsane_matched}==&quot;yes&quot;, MODE=&quot;0666&quot;, GROUP=&quot;scanner&quot;</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>3. \u521B\u5EFAscanner\u7528\u6237\u7EC4\u5E76\u6DFB\u52A0\u7528\u6237\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u521B\u5EFAscanner\u7528\u6237\u7EC4
sudo groupadd scanner

# \u5C06\u5F53\u524D\u7528\u6237\u6DFB\u52A0\u5230scanner\u7EC4
sudo usermod -a -G scanner $USER

# \u5C06\u7528\u6237\u6DFB\u52A0\u5230\u5176\u4ED6\u76F8\u5173\u7EC4
sudo usermod -a -G lp $USER
sudo usermod -a -G dialout $USER

# \u68C0\u67E5\u7528\u6237\u7EC4\u6210\u5458
groups $USER</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>4. \u91CD\u65B0\u52A0\u8F7Dudev\u89C4\u5219\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u91CD\u65B0\u52A0\u8F7Dudev\u89C4\u5219
sudo udevadm control --reload-rules
sudo udevadm trigger

# \u91CD\u65B0\u63D2\u62D4USB\u8BBE\u5907\u6216\u91CD\u542F\u7CFB\u7EDF
# \u68C0\u67E5\u8BBE\u5907\u6743\u9650
ls -l /dev/bus/usb/[bus]/[device]</pre></div></div><div class="tip-box" data-v-13cbb726><h4 class="tip-title" data-v-13cbb726>\u6743\u9650\u9A8C\u8BC1\uFF1A</h4><p data-v-13cbb726> \u914D\u7F6E\u5B8C\u6210\u540E\uFF0C\u666E\u901A\u7528\u6237\u5E94\u8BE5\u80FD\u591F\u8FD0\u884C <code data-v-13cbb726>scanimage -L</code> \u547D\u4EE4\u800C\u65E0\u9700sudo\u6743\u9650\u3002 </p></div></div></div></div><div id="network-scanner" class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>\u7F51\u7EDC\u626B\u63CF\u4EEA\u914D\u7F6E</h2><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u914D\u7F6Esaned\u670D\u52A1\u8BBF\u95EE\u7F51\u7EDC\u626B\u63CF\u4EEA\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> saned\u662FSANE\u7684\u7F51\u7EDC\u5B88\u62A4\u8FDB\u7A0B\uFF0C\u5141\u8BB8\u901A\u8FC7\u7F51\u7EDC\u8BBF\u95EE\u8FDC\u7A0B\u626B\u63CF\u4EEA\u8BBE\u5907\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>1. \u5B89\u88C5saned\u670D\u52A1\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># Ubuntu/Debian\u7CFB\u7EDF
sudo apt install sane-utils

# CentOS/RHEL\u7CFB\u7EDF
sudo yum install sane-backends-daemon

# \u68C0\u67E5saned\u662F\u5426\u5B89\u88C5
which saned</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>2. \u914D\u7F6Esaned.conf\u6587\u4EF6\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u7F16\u8F91saned\u914D\u7F6E\u6587\u4EF6
sudo nano /etc/sane.d/saned.conf

# \u6DFB\u52A0\u5141\u8BB8\u8BBF\u95EE\u7684\u5BA2\u6237\u7AEFIP\u5730\u5740\u6216\u7F51\u6BB5
# \u4F8B\u5982\uFF1A
***********/24    # \u5141\u8BB8\u6574\u4E2A192.168.1.x\u7F51\u6BB5
*************     # \u5141\u8BB8\u7279\u5B9AIP\u5730\u5740
localhost         # \u5141\u8BB8\u672C\u5730\u8BBF\u95EE

# \u6CE8\u91CA\u6389\u4E0D\u9700\u8981\u7684\u9ED8\u8BA4\u914D\u7F6E</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>3. \u914D\u7F6E\u7F51\u7EDC\u626B\u63CF\u4EEA\u540E\u7AEF\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u7F16\u8F91net\u540E\u7AEF\u914D\u7F6E
sudo nano /etc/sane.d/net.conf

# \u6DFB\u52A0\u7F51\u7EDC\u626B\u63CF\u4EEA\u670D\u52A1\u5668\u5730\u5740
************      # \u626B\u63CF\u4EEA\u670D\u52A1\u5668IP
scanner.local     # \u6216\u8005\u4F7F\u7528\u4E3B\u673A\u540D

# \u5982\u679C\u9700\u8981\u6307\u5B9A\u7AEF\u53E3\uFF08\u9ED8\u8BA46566\uFF09
************:6566</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>4. \u542F\u52A8saned\u670D\u52A1\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u4F7F\u7528systemd\u542F\u52A8\uFF08\u63A8\u8350\uFF09
sudo systemctl enable saned.socket
sudo systemctl start saned.socket

# \u68C0\u67E5\u670D\u52A1\u72B6\u6001
sudo systemctl status saned.socket

# \u6216\u8005\u4F7F\u7528inetd\u65B9\u5F0F\u542F\u52A8
# \u7F16\u8F91/etc/inetd.conf\uFF0C\u6DFB\u52A0\uFF1A
# sane-port stream tcp nowait saned:saned /usr/sbin/saned saned

# \u91CD\u542Finetd\u670D\u52A1
sudo systemctl restart inetd</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>5. \u9632\u706B\u5899\u914D\u7F6E\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5F00\u653Esaned\u7AEF\u53E3\uFF08\u9ED8\u8BA46566\uFF09
sudo ufw allow 6566/tcp

# \u6216\u8005\u4F7F\u7528iptables
sudo iptables -A INPUT -p tcp --dport 6566 -j ACCEPT

# firewalld\u914D\u7F6E
sudo firewall-cmd --permanent --add-port=6566/tcp
sudo firewall-cmd --reload</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>6. \u6D4B\u8BD5\u7F51\u7EDC\u626B\u63CF\u4EEA\u8FDE\u63A5\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5728\u5BA2\u6237\u7AEF\u6D4B\u8BD5\u7F51\u7EDC\u626B\u63CF\u4EEA
scanimage -L

# \u5E94\u8BE5\u663E\u793A\u7C7B\u4F3C\u8F93\u51FA\uFF1A
# device \`net:************:epson2:libusb:001:003&#39; is a Epson...

# \u6D4B\u8BD5\u626B\u63CF\u529F\u80FD
scanimage --test -d &quot;net:************:epson2:libusb:001:003&quot;</pre></div></div><div class="warning-box" data-v-13cbb726><h4 class="warning-title" data-v-13cbb726>\u7F51\u7EDC\u626B\u63CF\u4EEA\u5E38\u89C1\u95EE\u9898\uFF1A</h4><ul class="warning-list" data-v-13cbb726><li data-v-13cbb726>\u786E\u4FDD\u7F51\u7EDC\u8FDE\u901A\u6027\uFF0C\u53EF\u4EE5ping\u901A\u626B\u63CF\u4EEA\u670D\u52A1\u5668</li><li data-v-13cbb726>\u68C0\u67E5\u9632\u706B\u5899\u662F\u5426\u963B\u6B62\u4E866566\u7AEF\u53E3</li><li data-v-13cbb726>\u786E\u8BA4saned\u670D\u52A1\u6B63\u5728\u8FD0\u884C</li><li data-v-13cbb726>\u68C0\u67E5saned.conf\u4E2D\u7684\u8BBF\u95EE\u6743\u9650\u914D\u7F6E</li></ul></div></div></div><div class="faq-item" data-v-13cbb726><h3 class="faq-question" data-v-13cbb726>\u5982\u4F55\u81EA\u52A8\u53D1\u73B0\u7F51\u7EDC\u4E2D\u7684\u626B\u63CF\u4EEA\u8BBE\u5907\uFF1F</h3><div class="faq-answer" data-v-13cbb726><p class="mb-4" data-v-13cbb726> \u73B0\u4EE3\u7F51\u7EDC\u626B\u63CF\u4EEA\u652F\u6301\u591A\u79CD\u81EA\u52A8\u53D1\u73B0\u534F\u8BAE\uFF0C\u5982WSD\u3001AirScan\u7B49\u3002 </p><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>1. \u5B89\u88C5AirScan\u652F\u6301\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># Ubuntu/Debian\u5B89\u88C5AirScan
sudo apt install sane-airscan

# \u6216\u8005\u4ECE\u6E90\u7801\u7F16\u8BD1\u5B89\u88C5
git clone https://github.com/alexpevzner/sane-airscan.git
cd sane-airscan
make
sudo make install</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>2. \u914D\u7F6EAirScan\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u7F16\u8F91AirScan\u914D\u7F6E\u6587\u4EF6
sudo nano /etc/sane.d/airscan.conf

# \u542F\u7528\u81EA\u52A8\u53D1\u73B0
[devices]
discovery = true

# \u624B\u52A8\u6DFB\u52A0\u8BBE\u5907\uFF08\u5982\u679C\u81EA\u52A8\u53D1\u73B0\u5931\u8D25\uFF09
[devices]
&quot;My Scanner&quot; = http://*************/eSCL, WSD</pre></div></div><div class="step-guide" data-v-13cbb726><h4 class="step-title" data-v-13cbb726>3. \u4F7F\u7528avahi\u53D1\u73B0\u670D\u52A1\uFF1A</h4><div class="code-block" data-v-13cbb726><pre data-v-13cbb726># \u5B89\u88C5avahi\u5DE5\u5177
sudo apt install avahi-utils

# \u641C\u7D22\u7F51\u7EDC\u4E2D\u7684\u626B\u63CF\u4EEA\u670D\u52A1
avahi-browse -rt _uscan._tcp
avahi-browse -rt _ipp._tcp

# \u67E5\u770B\u5177\u4F53\u670D\u52A1\u4FE1\u606F
avahi-resolve -n [\u670D\u52A1\u540D\u79F0]</pre></div></div><div class="tip-box" data-v-13cbb726><h4 class="tip-title" data-v-13cbb726>\u652F\u6301\u7684\u7F51\u7EDC\u534F\u8BAE\uFF1A</h4><ul class="list-disc list-inside space-y-1 text-blue-700" data-v-13cbb726><li data-v-13cbb726><strong data-v-13cbb726>eSCL (AirScan)\uFF1A</strong> Apple\u548C\u5176\u4ED6\u5382\u5546\u652F\u6301\u7684\u6807\u51C6 </li><li data-v-13cbb726><strong data-v-13cbb726>WSD (Web Services for Devices)\uFF1A</strong> \u5FAE\u8F6F\u5F00\u53D1\u7684\u534F\u8BAE </li><li data-v-13cbb726><strong data-v-13cbb726>IPP (Internet Printing Protocol)\uFF1A</strong> \u652F\u6301\u626B\u63CF\u529F\u80FD\u7684\u6253\u5370\u534F\u8BAE </li><li data-v-13cbb726><strong data-v-13cbb726>SANE\u7F51\u7EDC\u534F\u8BAE\uFF1A</strong> \u4F20\u7EDF\u7684SANE\u7F51\u7EDC\u5171\u4EAB</li></ul></div></div></div></div><div class="card-business p-8 mb-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>\u6545\u969C\u6392\u9664\u603B\u7ED3</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-13cbb726><div class="troubleshoot-card" data-v-13cbb726><h3 class="troubleshoot-title" data-v-13cbb726>\u8FDE\u63A5\u95EE\u9898</h3><ul class="troubleshoot-list" data-v-13cbb726><li data-v-13cbb726>\u68C0\u67E5USB\u7EBF\u7F06\u8FDE\u63A5</li><li data-v-13cbb726>\u786E\u8BA4\u8BBE\u5907\u7535\u6E90\u72B6\u6001</li><li data-v-13cbb726>\u9A8C\u8BC1\u9A71\u52A8\u7A0B\u5E8F\u5B89\u88C5</li><li data-v-13cbb726>\u68C0\u67E5\u8BBE\u5907\u7BA1\u7406\u5668\u72B6\u6001</li></ul></div><div class="troubleshoot-card" data-v-13cbb726><h3 class="troubleshoot-title" data-v-13cbb726>\u6743\u9650\u95EE\u9898</h3><ul class="troubleshoot-list" data-v-13cbb726><li data-v-13cbb726>\u914D\u7F6Eudev\u89C4\u5219</li><li data-v-13cbb726>\u6DFB\u52A0\u7528\u6237\u5230scanner\u7EC4</li><li data-v-13cbb726>\u68C0\u67E5\u8BBE\u5907\u6587\u4EF6\u6743\u9650</li><li data-v-13cbb726>\u91CD\u65B0\u52A0\u8F7Dudev\u89C4\u5219</li></ul></div><div class="troubleshoot-card" data-v-13cbb726><h3 class="troubleshoot-title" data-v-13cbb726>\u7F51\u7EDC\u95EE\u9898</h3><ul class="troubleshoot-list" data-v-13cbb726><li data-v-13cbb726>\u68C0\u67E5\u7F51\u7EDC\u8FDE\u901A\u6027</li><li data-v-13cbb726>\u914D\u7F6E\u9632\u706B\u5899\u89C4\u5219</li><li data-v-13cbb726>\u9A8C\u8BC1saned\u670D\u52A1\u72B6\u6001</li><li data-v-13cbb726>\u786E\u8BA4\u7AEF\u53E3\u5F00\u653E\u60C5\u51B5</li></ul></div><div class="troubleshoot-card" data-v-13cbb726><h3 class="troubleshoot-title" data-v-13cbb726>\u7AEF\u53E3\u95EE\u9898</h3><ul class="troubleshoot-list" data-v-13cbb726><li data-v-13cbb726>\u68C0\u67E5\u7AEF\u53E3\u5360\u7528\u60C5\u51B5</li><li data-v-13cbb726>\u5173\u95ED\u51B2\u7A81\u7684\u7A0B\u5E8F</li><li data-v-13cbb726>\u91CD\u542FScanOnWeb\u670D\u52A1</li><li data-v-13cbb726>\u914D\u7F6E\u66FF\u4EE3\u7AEF\u53E3</li></ul></div></div></div><div class="card-business p-8" data-v-13cbb726><h2 class="heading-secondary mb-6" data-v-13cbb726>\u6280\u672F\u652F\u6301</h2><div class="bg-orange-50 border border-orange-200 rounded-lg p-6" data-v-13cbb726><div class="flex items-start" data-v-13cbb726><svg class="w-6 h-6 text-orange-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" data-v-13cbb726><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" data-v-13cbb726></path></svg><div data-v-13cbb726><h4 class="font-bold text-orange-800 mb-2" data-v-13cbb726>\u9700\u8981\u66F4\u591A\u5E2E\u52A9\uFF1F</h4><p class="text-orange-700 mb-4" data-v-13cbb726> \u5982\u679C\u4EE5\u4E0A\u89E3\u51B3\u65B9\u6848\u65E0\u6CD5\u89E3\u51B3\u60A8\u7684\u95EE\u9898\uFF0C\u8BF7\u8054\u7CFB\u6211\u4EEC\u7684\u6280\u672F\u652F\u6301\u56E2\u961F\u83B7\u53D6\u4E13\u4E1A\u5E2E\u52A9\u3002 </p><div class="space-y-2 text-orange-700" data-v-13cbb726><p data-v-13cbb726><strong data-v-13cbb726>\u6280\u672F\u652F\u6301\u90AE\u7BB1\uFF1A</strong> <EMAIL></p><p data-v-13cbb726><strong data-v-13cbb726>\u6280\u672F\u652F\u6301QQ\uFF1A</strong> 123456789</p><p data-v-13cbb726><strong data-v-13cbb726>\u5DE5\u4F5C\u65F6\u95F4\uFF1A</strong> \u5468\u4E00\u81F3\u5468\u4E94 9:00-18:00</p><p data-v-13cbb726><strong data-v-13cbb726>\u8FDC\u7A0B\u534F\u52A9\uFF1A</strong> \u652F\u6301TeamViewer\u8FDC\u7A0B\u6280\u672F\u652F\u6301</p></div><div class="mt-4" data-v-13cbb726>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/contact",
        class: "btn-primary mr-4"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u8054\u7CFB\u6280\u672F\u652F\u6301 `);
          } else {
            return [
              createTextVNode(" \u8054\u7CFB\u6280\u672F\u652F\u6301 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/docs/scanonweb-api",
        class: "btn-secondary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u67E5\u770BAPI\u6587\u6863 `);
          } else {
            return [
              createTextVNode(" \u67E5\u770BAPI\u6587\u6863 ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/docs/scanonweb-faq.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const scanonwebFaq = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-13cbb726"]]);

export { scanonwebFaq as default };
//# sourceMappingURL=scanonweb-faq--Vv0c0xK.mjs.map
