# Web控件产品展示网站

## 项目介绍

这是一个专业的Web控件产品展示网站，主要展示和演示我们公司开发的扫描仪控件、摄像头图像采集控件等产品。网站提供产品详细介绍、在线演示、技术文档和下载服务。

### 主要产品

- **ScanOnWeb** - 专业扫描仪控件解决方案（软件著作权登记号：2013SR145420）
- **ImageCapOnWeb** - 摄像头图像采集控件
- **GaoPaiYi** - 高拍仪图像采集控件

## 技术架构

本项目基于现代化的前端技术栈构建：

- **框架**: Nuxt 3 (基于 Vue 3)
- **样式**: Tailwind CSS
- **构建工具**: Vite
- **包管理**: npm/pnpm/yarn
- **开发语言**: TypeScript/JavaScript

### 项目结构

```
website/
├── components/          # Vue组件
│   ├── demos/          # 产品演示组件
│   ├── Header.vue      # 网站头部
│   ├── Footer.vue      # 网站底部
│   └── ...
├── pages/              # 页面路由
│   ├── index.vue       # 首页
│   ├── products.vue    # 产品页面
│   ├── demo.vue        # 演示页面
│   ├── documents.vue   # 文档页面
│   └── ...
├── assets/             # 静态资源
├── public/             # 公共文件
└── nuxt.config.ts      # Nuxt配置
```

## 安装教程

### 环境要求

- Node.js 16.x 或更高版本
- npm/pnpm/yarn 包管理器

### 安装步骤

1. 克隆项目到本地
```bash
git clone [项目地址]
cd website
```

2. 安装依赖
```bash
# 使用 npm
npm install

# 使用 pnpm
pnpm install

# 使用 yarn
yarn install
```

3. 启动开发服务器
```bash
# 使用 npm
npm run dev

# 使用 pnpm
pnpm run dev

# 使用 yarn
yarn dev
```

4. 访问 `http://localhost:3000` 查看网站

## 使用说明

### 开发环境

启动开发服务器后，可以：
- 实时预览网站效果
- 修改代码自动热重载
- 使用浏览器开发者工具调试

### 生产构建

#### 服务端渲染构建（SSR）

```bash
# 构建生产版本（服务端渲染）
npm run build

# 预览生产版本
npm run preview
```

#### 静态文件生成（推荐用于静态部署）

Nuxt 3 支持静态站点生成（SSG），可以将整个应用预渲染为静态HTML文件，非常适合静态文件服务器部署。

##### 1. 配置静态生成

首先需要修改 `website/nuxt.config.ts` 配置文件：

```typescript
// website/nuxt.config.ts
export default defineNuxtConfig({
  css: ['~/assets/css/main.css'],
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },

  // 静态生成配置
  nitro: {
    prerender: {
      routes: ['/sitemap.xml', '/robots.txt']
    }
  },

  // 可选：配置基础路径（如果部署在子目录）
  // app: {
  //   baseURL: '/your-subdirectory/'
  // }
})
```

##### 2. 生成静态文件

```bash
# 进入website目录
cd website

# 生成静态文件
npm run generate

# 或者使用其他包管理器
pnpm run generate
yarn generate
```

##### 3. 静态文件输出

生成完成后，静态文件将输出到 `website/.output/public/` 目录，包含：

```
.output/public/
├── index.html              # 首页
├── products/
│   └── index.html          # 产品页面
├── demo/
│   └── index.html          # 演示页面
├── documents/
│   └── index.html          # 文档页面
├── contact/
│   └── index.html          # 联系页面
├── downloads/
│   └── index.html          # 下载页面
├── _nuxt/                  # 静态资源（CSS、JS、图片等）
│   ├── *.css
│   ├── *.js
│   └── *.png/jpg/svg
└── ...                     # 其他静态文件
```

##### 4. 部署静态文件

将 `.output/public/` 目录中的所有文件上传到您的静态文件服务器即可：

**方法一：直接复制文件**
```bash
# 复制所有静态文件到服务器目录
cp -r website/.output/public/* /path/to/your/webserver/
```

**方法二：使用rsync同步**
```bash
# 同步到远程服务器
rsync -avz website/.output/public/ user@server:/path/to/webserver/
```

**方法三：使用FTP/SFTP工具**
- 使用FileZilla、WinSCP等工具
- 上传 `.output/public/` 目录下的所有文件

##### 5. 服务器配置

**Apache服务器 (.htaccess)**
```apache
# 启用重写引擎
RewriteEngine On

# 处理SPA路由
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# 缓存静态资源
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

**Nginx服务器配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/webserver;
    index index.html;

    # 处理静态文件
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 缓存静态资源
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

##### 6. 验证部署

部署完成后，访问您的域名验证：
- 首页是否正常显示
- 各个页面路由是否工作正常
- 静态资源（图片、CSS、JS）是否正确加载
- 响应式设计在不同设备上是否正常

##### 7. 常见问题解决

**问题1：页面刷新后出现404错误**
- 确保服务器配置了正确的重写规则
- 检查 `.htaccess` 或 Nginx 配置是否生效

**问题2：静态资源加载失败**
- 检查文件路径是否正确
- 确认 `_nuxt/` 目录及其内容已正确上传

**问题3：部署在子目录时路径错误**
- 在 `nuxt.config.ts` 中配置正确的 `baseURL`
- 重新生成静态文件

##### 8. 自动化部署脚本

创建部署脚本 `deploy.sh`：
```bash
#!/bin/bash
echo "开始构建静态文件..."
cd website
npm run generate

echo "部署到服务器..."
rsync -avz --delete .output/public/ user@server:/path/to/webserver/

echo "部署完成！"
```

使用方法：
```bash
chmod +x deploy.sh
./deploy.sh
```

### 功能特性

1. **响应式设计** - 支持桌面端和移动端访问
2. **产品展示** - 详细的产品介绍和技术规格
3. **在线演示** - 实时体验产品功能
4. **技术文档** - 完整的API文档和使用指南
5. **多平台下载** - 支持Windows/Linux多架构下载

## 参与贡献

欢迎参与项目贡献，请遵循以下流程：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范

- 遵循 Vue 3 Composition API 规范
- 使用 Tailwind CSS 进行样式开发
- 保持代码简洁和注释完整
- 提交前请确保代码通过 ESLint 检查

## 许可证

本项目仅供内部使用，未经授权不得用于商业用途。

