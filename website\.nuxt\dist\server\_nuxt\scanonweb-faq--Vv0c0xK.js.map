{"version": 3, "file": "scanonweb-faq--Vv0c0xK.js", "sources": ["../../../../pages/docs/scanonweb-faq.vue"], "sourcesContent": ["<template>\n  <div class=\"bg-gray-50 min-h-screen\">\n    <Header />\n    <main>\n      <!-- 面包屑导航 -->\n      <div class=\"bg-white border-b border-gray-200\">\n        <div class=\"container mx-auto px-4 py-4\">\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <NuxtLink to=\"/documents\" class=\"hover:text-orange-500\"\n              >文档资料</NuxtLink\n            >\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <span class=\"text-gray-900 font-medium\">ScanOnWeb 常见问题</span>\n          </nav>\n        </div>\n      </div>\n\n      <!-- 页面标题区域 -->\n      <div class=\"bg-white py-8\">\n        <div class=\"container mx-auto px-4\">\n          <div class=\"max-w-4xl\">\n            <h1 class=\"heading-primary mb-4\">ScanOnWeb 常见问题</h1>\n            <p class=\"text-lg text-gray-600 mb-6\">\n              解决ScanOnWeb扫描控件使用过程中的常见技术问题，包含驱动安装、端口配置、权限设置等详细解决方案\n            </p>\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>驱动安装指南</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>端口问题排查</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>权限配置</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 主要内容区域 -->\n      <div class=\"container mx-auto py-12 px-4\">\n        <!-- 问题分类导航 -->\n        <div class=\"card-business p-6 mb-8\">\n          <h2 class=\"heading-secondary mb-4\">问题分类</h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            <a\n              href=\"#windows-drivers\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">Windows驱动安装</div>\n              <div class=\"text-sm text-gray-600\">TWAIN驱动程序安装配置</div>\n            </a>\n            <a\n              href=\"#linux-drivers\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">Linux驱动安装</div>\n              <div class=\"text-sm text-gray-600\">SANE驱动程序安装配置</div>\n            </a>\n            <a\n              href=\"#port-issues\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">端口问题</div>\n              <div class=\"text-sm text-gray-600\">WebSocket端口占用排查</div>\n            </a>\n            <a\n              href=\"#device-support\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">设备兼容性</div>\n              <div class=\"text-sm text-gray-600\">协议支持检测方法</div>\n            </a>\n            <a\n              href=\"#linux-permissions\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">Linux权限配置</div>\n              <div class=\"text-sm text-gray-600\">USB设备udev权限设置</div>\n            </a>\n            <a\n              href=\"#network-scanner\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">网络扫描仪</div>\n              <div class=\"text-sm text-gray-600\">saned服务配置</div>\n            </a>\n          </div>\n        </div>\n\n        <!-- Windows驱动安装 -->\n        <div id=\"windows-drivers\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">Windows平台驱动安装</h2>\n\n          <!-- TWAIN驱动安装 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何安装TWAIN驱动程序？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                TWAIN是Windows平台上最常用的扫描仪驱动标准，大多数扫描仪厂商都提供TWAIN驱动程序。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">安装步骤：</h4>\n                <ol class=\"step-list\">\n                  <li>\n                    <strong>下载官方驱动：</strong>\n                    <p>\n                      访问扫描仪厂商官网，下载对应型号的TWAIN驱动程序。确保驱动版本支持您的Windows系统（32位/64位）。\n                    </p>\n                  </li>\n                  <li>\n                    <strong>以管理员身份安装：</strong>\n                    <p>\n                      右键点击驱动安装程序，选择\"以管理员身份运行\"，按照安装向导完成安装。\n                    </p>\n                  </li>\n                  <li>\n                    <strong>连接设备：</strong>\n                    <p>使用USB线缆连接扫描仪到计算机，确保设备电源已开启。</p>\n                  </li>\n                  <li>\n                    <strong>验证安装：</strong>\n                    <p>\n                      打开\"设备管理器\"，在\"图像设备\"分类下应该能看到您的扫描仪设备，且没有黄色警告图标。\n                    </p>\n                  </li>\n                </ol>\n              </div>\n\n              <div class=\"code-example\">\n                <h4 class=\"code-title\">检查TWAIN驱动是否正确安装：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 在命令提示符中运行以下命令检查TWAIN数据源\n# 打开注册表编辑器\nregedit\n\n# 导航到以下路径查看已安装的TWAIN驱动\nHKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}\\Instance\n\n# 或者使用PowerShell查看\nGet-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like \"*scanner*\" -or $_.Name -like \"*imaging*\"}</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"warning-box\">\n                <h4 class=\"warning-title\">常见问题：</h4>\n                <ul class=\"warning-list\">\n                  <li>如果设备管理器中显示\"未知设备\"，说明驱动未正确安装</li>\n                  <li>\n                    某些老旧扫描仪可能不支持Windows 10/11，需要使用兼容模式\n                  </li>\n                  <li>安装驱动前建议先卸载旧版本驱动，避免冲突</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- WIA驱动说明 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">WIA驱动与TWAIN驱动的区别？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                WIA（Windows Image\n                Acquisition）是微软开发的图像获取标准，而TWAIN是第三方标准。\n              </p>\n\n              <div class=\"comparison-table\">\n                <table class=\"w-full border-collapse border border-gray-300\">\n                  <thead>\n                    <tr class=\"bg-gray-100\">\n                      <th class=\"border border-gray-300 px-4 py-2\">特性</th>\n                      <th class=\"border border-gray-300 px-4 py-2\">TWAIN</th>\n                      <th class=\"border border-gray-300 px-4 py-2\">WIA</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td class=\"border border-gray-300 px-4 py-2 font-medium\">\n                        兼容性\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        跨平台支持，功能丰富\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        仅Windows平台\n                      </td>\n                    </tr>\n                    <tr>\n                      <td class=\"border border-gray-300 px-4 py-2 font-medium\">\n                        安装复杂度\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        需要厂商提供专用驱动\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        Windows内置支持\n                      </td>\n                    </tr>\n                    <tr>\n                      <td class=\"border border-gray-300 px-4 py-2 font-medium\">\n                        功能支持\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        功能全面，支持高级特性\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        基础功能，简单易用\n                      </td>\n                    </tr>\n                    <tr>\n                      <td class=\"border border-gray-300 px-4 py-2 font-medium\">\n                        ScanOnWeb支持\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">\n                        ✅ 完全支持\n                      </td>\n                      <td class=\"border border-gray-300 px-4 py-2\">✅ 支持</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n\n              <div class=\"tip-box\">\n                <h4 class=\"tip-title\">推荐方案：</h4>\n                <p>\n                  优先使用TWAIN驱动，如果厂商未提供TWAIN驱动，可以尝试使用WIA驱动。ScanOnWeb控件对两种驱动都有良好支持。\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Linux驱动安装 -->\n        <div id=\"linux-drivers\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">Linux平台驱动安装</h2>\n\n          <!-- SANE驱动安装 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何安装SANE驱动程序？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                SANE（Scanner Access Now\n                Easy）是Linux平台上的标准扫描仪接口，支持大多数主流扫描仪品牌。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Ubuntu/Debian系统安装：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 更新软件包列表\nsudo apt update\n\n# 安装SANE核心包\nsudo apt install sane-utils libsane-extras\n\n# 安装常用扫描仪驱动\nsudo apt install libsane-hpaio  # HP扫描仪\nsudo apt install sane-airscan   # 网络扫描仪支持\n\n# 检查SANE版本\nsane-find-scanner --version</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">CentOS/RHEL系统安装：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 安装EPEL仓库\nsudo yum install epel-release\n\n# 安装SANE包\nsudo yum install sane-backends sane-frontends\n\n# 或者在较新版本中使用dnf\nsudo dnf install sane-backends sane-frontends</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">验证安装：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 扫描检测可用的扫描仪\nsudo sane-find-scanner\n\n# 列出SANE支持的设备\nscanimage -L\n\n# 测试扫描功能\nscanimage --test</pre\n                  >\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 端口问题排查 -->\n        <div id=\"port-issues\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">WebSocket端口问题排查</h2>\n\n          <!-- 端口占用检测 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何检查ScanOnWeb服务端口是否被占用？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                ScanOnWeb托盘服务默认使用端口1001-5001范围内的端口，如果端口被占用会导致连接失败。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Windows系统检查方法：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 检查特定端口是否被占用\nnetstat -ano | findstr :1001\nnetstat -ano | findstr :2001\nnetstat -ano | findstr :3001\n\n# 查看所有监听端口\nnetstat -ano | findstr LISTENING\n\n# 使用PowerShell检查端口\nGet-NetTCPConnection -LocalPort 1001,2001,3001 -State Listen\n\n# 检查进程占用端口情况\ntasklist /fi \"pid eq [PID]\"</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Linux系统检查方法：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 检查端口占用情况\nsudo netstat -tulpn | grep :1001\nsudo netstat -tulpn | grep :2001\nsudo netstat -tulpn | grep :3001\n\n# 使用ss命令检查\nsudo ss -tulpn | grep :1001\n\n# 使用lsof检查端口\nsudo lsof -i :1001\n\n# 检查ScanOnWeb服务状态\nps aux | grep scanonweb</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"warning-box\">\n                <h4 class=\"warning-title\">端口冲突解决方案：</h4>\n                <ul class=\"warning-list\">\n                  <li>关闭占用端口的其他程序</li>\n                  <li>重启ScanOnWeb托盘服务</li>\n                  <li>修改ScanOnWeb配置文件指定其他端口</li>\n                  <li>使用防火墙规则允许ScanOnWeb端口通信</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- 防火墙配置 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何配置防火墙允许ScanOnWeb通信？</h3>\n            <div class=\"faq-answer\">\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Windows防火墙配置：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 使用命令行添加防火墙规则\nnetsh advfirewall firewall add rule name=\"ScanOnWeb\" dir=in action=allow protocol=TCP localport=1001-5001\n\n# 或者通过图形界面：\n# 1. 打开\"Windows Defender 防火墙\"\n# 2. 点击\"高级设置\"\n# 3. 选择\"入站规则\" -> \"新建规则\"\n# 4. 选择\"端口\" -> \"TCP\" -> \"特定本地端口\"\n# 5. 输入端口范围：1001-5001</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Linux防火墙配置（iptables）：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 允许ScanOnWeb端口\nsudo iptables -A INPUT -p tcp --dport 1001:5001 -j ACCEPT\n\n# 保存规则（Ubuntu/Debian）\nsudo iptables-save > /etc/iptables/rules.v4\n\n# CentOS/RHEL保存规则\nsudo service iptables save</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Linux防火墙配置（firewalld）：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 添加端口范围\nsudo firewall-cmd --permanent --add-port=1001-5001/tcp\n\n# 重新加载配置\nsudo firewall-cmd --reload\n\n# 检查配置\nsudo firewall-cmd --list-ports</pre\n                  >\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 设备兼容性检测 -->\n        <div id=\"device-support\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">设备兼容性检测</h2>\n\n          <!-- TWAIN协议检测 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何判断扫描仪是否支持TWAIN协议？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">TWAIN协议支持检测可以通过多种方式进行验证。</p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Windows系统检测方法：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 1. 检查设备管理器\n# 打开设备管理器，查看\"图像设备\"分类\n# 支持TWAIN的设备通常显示为\"[品牌] [型号] TWAIN\"\n\n# 2. 检查注册表TWAIN数据源\n# 打开注册表编辑器，导航到：\nHKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{B96B3CAE-0728-11D3-9D7B-0000F81EF32E}\\Instance\n\n# 3. 使用TWAIN测试工具\n# 下载TWAIN Sample Application进行测试\n# 或使用Windows自带的\"Windows传真和扫描\"程序\n\n# 4. PowerShell检测脚本\nGet-WmiObject -Class Win32_PnPEntity | Where-Object {\n    $_.Name -like \"*scanner*\" -and $_.Status -eq \"OK\"\n} | Select-Object Name, DeviceID, Status</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"tip-box\">\n                <h4 class=\"tip-title\">TWAIN兼容性标识：</h4>\n                <ul class=\"list-disc list-inside space-y-1 text-blue-700\">\n                  <li>设备名称包含\"TWAIN\"字样</li>\n                  <li>厂商官网明确标注TWAIN支持</li>\n                  <li>驱动安装包包含TWAIN组件</li>\n                  <li>可以在图像编辑软件中通过\"获取\"菜单访问</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- SANE协议检测 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何判断扫描仪是否支持SANE协议？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                SANE协议支持检测主要通过命令行工具和官方兼容性列表。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">Linux系统检测方法：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 1. 使用sane-find-scanner检测硬件\nsudo sane-find-scanner\n\n# 2. 列出SANE支持的设备\nscanimage -L\n\n# 3. 检查设备详细信息\nscanimage --help -d [设备名称]\n\n# 4. 查看USB设备信息\nlsusb | grep -i scanner\nlsusb -v | grep -A 10 -B 5 \"Scanner\\|Imaging\"\n\n# 5. 检查SANE后端支持\nls /usr/lib/sane/ | grep -i [厂商名称]\n\n# 6. 测试设备连接\nscanimage --test -d [设备名称]</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">查看SANE官方兼容性列表：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 访问SANE官方支持列表\n# http://www.sane-project.org/sane-supported-devices.html\n\n# 或者使用命令查看本地支持列表\nman sane-[厂商名称]\n\n# 例如：\nman sane-epson2\nman sane-hp\nman sane-canon</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"warning-box\">\n                <h4 class=\"warning-title\">常见不支持的情况：</h4>\n                <ul class=\"warning-list\">\n                  <li>某些新款扫描仪可能需要等待SANE后端更新</li>\n                  <li>专业高端扫描仪可能只提供厂商专用驱动</li>\n                  <li>某些多功能一体机的扫描功能可能不被完全支持</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Linux权限配置 -->\n        <div id=\"linux-permissions\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">Linux USB设备权限配置</h2>\n\n          <!-- udev规则配置 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何设置Linux下USB扫描仪的udev权限？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                Linux系统需要正确的udev规则才能让普通用户访问USB扫描仪设备。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">1. 查看设备信息：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 连接扫描仪后查看USB设备\nlsusb\n\n# 查看详细设备信息\nlsusb -v | grep -A 10 -B 5 \"Scanner\\|Imaging\"\n\n# 获取设备的Vendor ID和Product ID\n# 例如输出：Bus 001 Device 003: ID 04b8:0142 Seiko Epson Corp.\n# 其中04b8是Vendor ID，0142是Product ID</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">2. 创建udev规则文件：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 创建udev规则文件\nsudo nano /etc/udev/rules.d/99-scanner.rules\n\n# 添加以下内容（替换为实际的Vendor ID和Product ID）：\n# Epson扫描仪示例\nSUBSYSTEM==\"usb\", ATTR{idVendor}==\"04b8\", ATTR{idProduct}==\"0142\", MODE=\"0666\", GROUP=\"scanner\"\n\n# HP扫描仪示例\nSUBSYSTEM==\"usb\", ATTR{idVendor}==\"03f0\", MODE=\"0666\", GROUP=\"scanner\"\n\n# Canon扫描仪示例\nSUBSYSTEM==\"usb\", ATTR{idVendor}==\"04a9\", MODE=\"0666\", GROUP=\"scanner\"\n\n# 通用规则（适用于所有扫描仪）\nSUBSYSTEM==\"usb\", ENV{libsane_matched}==\"yes\", MODE=\"0666\", GROUP=\"scanner\"</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">3. 创建scanner用户组并添加用户：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 创建scanner用户组\nsudo groupadd scanner\n\n# 将当前用户添加到scanner组\nsudo usermod -a -G scanner $USER\n\n# 将用户添加到其他相关组\nsudo usermod -a -G lp $USER\nsudo usermod -a -G dialout $USER\n\n# 检查用户组成员\ngroups $USER</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">4. 重新加载udev规则：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 重新加载udev规则\nsudo udevadm control --reload-rules\nsudo udevadm trigger\n\n# 重新插拔USB设备或重启系统\n# 检查设备权限\nls -l /dev/bus/usb/[bus]/[device]</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"tip-box\">\n                <h4 class=\"tip-title\">权限验证：</h4>\n                <p>\n                  配置完成后，普通用户应该能够运行\n                  <code>scanimage -L</code> 命令而无需sudo权限。\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 网络扫描仪配置 -->\n        <div id=\"network-scanner\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">网络扫描仪配置</h2>\n\n          <!-- saned服务配置 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何配置saned服务访问网络扫描仪？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                saned是SANE的网络守护进程，允许通过网络访问远程扫描仪设备。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">1. 安装saned服务：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# Ubuntu/Debian系统\nsudo apt install sane-utils\n\n# CentOS/RHEL系统\nsudo yum install sane-backends-daemon\n\n# 检查saned是否安装\nwhich saned</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">2. 配置saned.conf文件：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 编辑saned配置文件\nsudo nano /etc/sane.d/saned.conf\n\n# 添加允许访问的客户端IP地址或网段\n# 例如：\n192.168.1.0/24    # 允许整个192.168.1.x网段\n*************     # 允许特定IP地址\nlocalhost         # 允许本地访问\n\n# 注释掉不需要的默认配置</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">3. 配置网络扫描仪后端：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 编辑net后端配置\nsudo nano /etc/sane.d/net.conf\n\n# 添加网络扫描仪服务器地址\n************      # 扫描仪服务器IP\nscanner.local     # 或者使用主机名\n\n# 如果需要指定端口（默认6566）\n************:6566</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">4. 启动saned服务：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 使用systemd启动（推荐）\nsudo systemctl enable saned.socket\nsudo systemctl start saned.socket\n\n# 检查服务状态\nsudo systemctl status saned.socket\n\n# 或者使用inetd方式启动\n# 编辑/etc/inetd.conf，添加：\n# sane-port stream tcp nowait saned:saned /usr/sbin/saned saned\n\n# 重启inetd服务\nsudo systemctl restart inetd</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">5. 防火墙配置：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 开放saned端口（默认6566）\nsudo ufw allow 6566/tcp\n\n# 或者使用iptables\nsudo iptables -A INPUT -p tcp --dport 6566 -j ACCEPT\n\n# firewalld配置\nsudo firewall-cmd --permanent --add-port=6566/tcp\nsudo firewall-cmd --reload</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">6. 测试网络扫描仪连接：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 在客户端测试网络扫描仪\nscanimage -L\n\n# 应该显示类似输出：\n# device `net:************:epson2:libusb:001:003' is a Epson...\n\n# 测试扫描功能\nscanimage --test -d \"net:************:epson2:libusb:001:003\"</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"warning-box\">\n                <h4 class=\"warning-title\">网络扫描仪常见问题：</h4>\n                <ul class=\"warning-list\">\n                  <li>确保网络连通性，可以ping通扫描仪服务器</li>\n                  <li>检查防火墙是否阻止了6566端口</li>\n                  <li>确认saned服务正在运行</li>\n                  <li>检查saned.conf中的访问权限配置</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- 网络扫描仪发现 -->\n          <div class=\"faq-item\">\n            <h3 class=\"faq-question\">如何自动发现网络中的扫描仪设备？</h3>\n            <div class=\"faq-answer\">\n              <p class=\"mb-4\">\n                现代网络扫描仪支持多种自动发现协议，如WSD、AirScan等。\n              </p>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">1. 安装AirScan支持：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# Ubuntu/Debian安装AirScan\nsudo apt install sane-airscan\n\n# 或者从源码编译安装\ngit clone https://github.com/alexpevzner/sane-airscan.git\ncd sane-airscan\nmake\nsudo make install</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">2. 配置AirScan：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 编辑AirScan配置文件\nsudo nano /etc/sane.d/airscan.conf\n\n# 启用自动发现\n[devices]\ndiscovery = true\n\n# 手动添加设备（如果自动发现失败）\n[devices]\n\"My Scanner\" = http://*************/eSCL, WSD</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"step-guide\">\n                <h4 class=\"step-title\">3. 使用avahi发现服务：</h4>\n                <div class=\"code-block\">\n                  <pre>\n# 安装avahi工具\nsudo apt install avahi-utils\n\n# 搜索网络中的扫描仪服务\navahi-browse -rt _uscan._tcp\navahi-browse -rt _ipp._tcp\n\n# 查看具体服务信息\navahi-resolve -n [服务名称]</pre\n                  >\n                </div>\n              </div>\n\n              <div class=\"tip-box\">\n                <h4 class=\"tip-title\">支持的网络协议：</h4>\n                <ul class=\"list-disc list-inside space-y-1 text-blue-700\">\n                  <li>\n                    <strong>eSCL (AirScan)：</strong> Apple和其他厂商支持的标准\n                  </li>\n                  <li>\n                    <strong>WSD (Web Services for Devices)：</strong>\n                    微软开发的协议\n                  </li>\n                  <li>\n                    <strong>IPP (Internet Printing Protocol)：</strong>\n                    支持扫描功能的打印协议\n                  </li>\n                  <li><strong>SANE网络协议：</strong> 传统的SANE网络共享</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 故障排除总结 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">故障排除总结</h2>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div class=\"troubleshoot-card\">\n              <h3 class=\"troubleshoot-title\">连接问题</h3>\n              <ul class=\"troubleshoot-list\">\n                <li>检查USB线缆连接</li>\n                <li>确认设备电源状态</li>\n                <li>验证驱动程序安装</li>\n                <li>检查设备管理器状态</li>\n              </ul>\n            </div>\n\n            <div class=\"troubleshoot-card\">\n              <h3 class=\"troubleshoot-title\">权限问题</h3>\n              <ul class=\"troubleshoot-list\">\n                <li>配置udev规则</li>\n                <li>添加用户到scanner组</li>\n                <li>检查设备文件权限</li>\n                <li>重新加载udev规则</li>\n              </ul>\n            </div>\n\n            <div class=\"troubleshoot-card\">\n              <h3 class=\"troubleshoot-title\">网络问题</h3>\n              <ul class=\"troubleshoot-list\">\n                <li>检查网络连通性</li>\n                <li>配置防火墙规则</li>\n                <li>验证saned服务状态</li>\n                <li>确认端口开放情况</li>\n              </ul>\n            </div>\n\n            <div class=\"troubleshoot-card\">\n              <h3 class=\"troubleshoot-title\">端口问题</h3>\n              <ul class=\"troubleshoot-list\">\n                <li>检查端口占用情况</li>\n                <li>关闭冲突的程序</li>\n                <li>重启ScanOnWeb服务</li>\n                <li>配置替代端口</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <!-- 技术支持 -->\n        <div class=\"card-business p-8\">\n          <h2 class=\"heading-secondary mb-6\">技术支持</h2>\n\n          <div class=\"bg-orange-50 border border-orange-200 rounded-lg p-6\">\n            <div class=\"flex items-start\">\n              <svg\n                class=\"w-6 h-6 text-orange-500 mr-3 mt-1 flex-shrink-0\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fill-rule=\"evenodd\"\n                  d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\"\n                  clip-rule=\"evenodd\"\n                ></path>\n              </svg>\n              <div>\n                <h4 class=\"font-bold text-orange-800 mb-2\">需要更多帮助？</h4>\n                <p class=\"text-orange-700 mb-4\">\n                  如果以上解决方案无法解决您的问题，请联系我们的技术支持团队获取专业帮助。\n                </p>\n                <div class=\"space-y-2 text-orange-700\">\n                  <p><strong>技术支持邮箱：</strong> <EMAIL></p>\n                  <p><strong>技术支持QQ：</strong> 123456789</p>\n                  <p><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>\n                  <p><strong>远程协助：</strong> 支持TeamViewer远程技术支持</p>\n                </div>\n                <div class=\"mt-4\">\n                  <NuxtLink to=\"/contact\" class=\"btn-primary mr-4\">\n                    联系技术支持\n                  </NuxtLink>\n                  <NuxtLink to=\"/docs/scanonweb-api\" class=\"btn-secondary\">\n                    查看API文档\n                  </NuxtLink>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </main>\n    <Footer />\n  </div>\n</template>\n\n<script setup>\nimport Header from \"~/components/Header.vue\";\nimport Footer from \"~/components/Footer.vue\";\n\n// SEO设置\nuseHead({\n  title: \"ScanOnWeb 常见问题 - 驱动安装与故障排除\",\n  meta: [\n    {\n      name: \"description\",\n      content:\n        \"ScanOnWeb扫描控件常见问题解答，包含Windows TWAIN驱动、Linux SANE驱动安装，端口占用排查，权限配置等详细解决方案。\",\n    },\n    {\n      name: \"keywords\",\n      content:\n        \"ScanOnWeb,常见问题,TWAIN驱动,SANE驱动,端口占用,Linux权限,扫描仪驱动,故障排除\",\n    },\n  ],\n});\n</script>\n\n<style scoped>\n.faq-item {\n  @apply mb-8 border-b border-gray-200 pb-6;\n}\n\n.faq-question {\n  @apply text-xl font-bold text-gray-900 mb-4 cursor-pointer hover:text-orange-600 transition-colors;\n}\n\n.faq-answer {\n  @apply text-gray-700;\n}\n\n.step-guide {\n  @apply mb-6;\n}\n\n.step-title {\n  @apply text-lg font-semibold text-gray-800 mb-3;\n}\n\n.step-list {\n  @apply list-decimal list-inside space-y-4 ml-4;\n}\n\n.step-list li {\n  @apply mb-3;\n}\n\n.step-list li strong {\n  @apply text-gray-900 font-semibold;\n}\n\n.step-list li p {\n  @apply mt-1 ml-6 text-gray-600;\n}\n\n.code-example {\n  @apply mb-6;\n}\n\n.code-title {\n  @apply text-lg font-semibold text-gray-800 mb-3;\n}\n\n.code-block {\n  @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;\n}\n\n.code-block pre {\n  @apply font-mono text-sm m-0;\n}\n\n.warning-box {\n  @apply bg-red-50 border border-red-200 rounded-lg p-4 mb-4;\n}\n\n.warning-title {\n  @apply font-bold text-red-800 mb-2;\n}\n\n.warning-list {\n  @apply list-disc list-inside space-y-1 text-red-700;\n}\n\n.tip-box {\n  @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4;\n}\n\n.tip-title {\n  @apply font-bold text-blue-800 mb-2;\n}\n\n.comparison-table {\n  @apply mb-6 overflow-x-auto;\n}\n\n.comparison-table th {\n  @apply font-semibold text-gray-900;\n}\n\n.comparison-table td {\n  @apply text-gray-700;\n}\n\n.troubleshoot-card {\n  @apply bg-gray-50 border border-gray-200 rounded-lg p-4;\n}\n\n.troubleshoot-title {\n  @apply text-lg font-semibold text-gray-800 mb-3;\n}\n\n.troubleshoot-list {\n  @apply list-disc list-inside space-y-1 text-gray-600;\n}\n\n.btn-secondary {\n  @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAw+BQ,YAAA;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,MACF;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}