{"version": 3, "file": "scanonweb-getting-started-B2N3QRJP.js", "sources": ["../../../../pages/docs/scanonweb-getting-started.vue"], "sourcesContent": ["<template>\n  <div class=\"bg-gray-50 min-h-screen\">\n    <Header />\n    <main>\n      <!-- 面包屑导航 -->\n      <div class=\"bg-white border-b border-gray-200\">\n        <div class=\"container mx-auto px-4 py-4\">\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <NuxtLink to=\"/documents\" class=\"hover:text-orange-500\"\n              >文档资料</NuxtLink\n            >\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <span class=\"text-gray-900 font-medium\">ScanOnWeb 入门指南</span>\n          </nav>\n        </div>\n      </div>\n\n      <!-- 页面标题区域 -->\n      <div class=\"bg-white py-8\">\n        <div class=\"container mx-auto px-4\">\n          <div class=\"max-w-4xl\">\n            <h1 class=\"heading-primary mb-4\">ScanOnWeb 入门指南</h1>\n            <p class=\"text-lg text-gray-600 mb-6\">\n              快速上手ScanOnWeb扫描控件，支持原生HTML和Vue3项目集成\n            </p>\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>5分钟快速上手</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>完整代码示例</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>多框架支持</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 主要内容区域 -->\n      <div class=\"container mx-auto py-12 px-4\">\n        <!-- 概述 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">概述</h2>\n          <p class=\"text-business mb-4\">\n            ScanOnWeb是一款专业的Web扫描控件，通过WebSocket与本地托盘服务通信，实现在浏览器中直接控制扫描仪设备。\n            控件支持多种扫描模式、图像处理功能，并可无缝集成到各种Web应用中。\n          </p>\n          <div class=\"feature-highlight\">\n            <h4 class=\"font-bold text-orange-700 mb-2\">重要提醒</h4>\n            <p class=\"text-orange-600\">\n              ScanOnWeb控件需要用户在本地安装托盘服务程序才能正常工作。控件文件需要手动复制到项目中，未发布到npm仓库。\n            </p>\n          </div>\n        </div>\n\n        <!-- 系统要求 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">系统要求</h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 class=\"heading-tertiary mb-3\">客户端要求</h3>\n              <ul class=\"space-y-2 text-business\">\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  Windows 7/8/10/11 或 Linux\n                </li>\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  支持WebSocket的现代浏览器\n                </li>\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  兼容TWAIN 1.9+或WIA协议的扫描仪\n                </li>\n              </ul>\n            </div>\n            <div>\n              <h3 class=\"heading-tertiary mb-3\">浏览器支持</h3>\n              <ul class=\"space-y-2 text-business\">\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  Chrome 60+\n                </li>\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  Firefox 55+\n                </li>\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  Edge 79+\n                </li>\n                <li class=\"flex items-center\">\n                  <svg\n                    class=\"w-4 h-4 mr-2 text-green-500\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  Internet Explorer 11\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <!-- 安装步骤 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">安装步骤</h2>\n\n          <div class=\"space-y-6\">\n            <!-- 步骤1 -->\n            <div class=\"border-l-4 border-orange-500 pl-6\">\n              <h3 class=\"heading-tertiary mb-3\">步骤1：下载托盘服务程序</h3>\n              <p class=\"text-business mb-4\">\n                首先需要在客户端计算机上安装ScanOnWeb托盘服务程序，该程序负责与扫描仪硬件通信。\n              </p>\n              <div class=\"bg-gray-50 p-4 rounded-lg\">\n                <p class=\"text-sm text-gray-600 mb-2\">下载链接：</p>\n                <a\n                  href=\"https://www.brainysoft.cn/download/ScanOnWebH5Install.exe\"\n                  class=\"text-orange-500 hover:text-orange-600 font-medium\"\n                  target=\"_blank\"\n                >\n                  ScanOnWebH5Install.exe\n                </a>\n              </div>\n            </div>\n\n            <!-- 步骤2 -->\n            <div class=\"border-l-4 border-orange-500 pl-6\">\n              <h3 class=\"heading-tertiary mb-3\">步骤2：获取控件文件</h3>\n              <p class=\"text-business mb-4\">\n                从官方获取scanonweb.js控件文件，该文件包含了所有扫描功能的JavaScript\n                API。\n              </p>\n              <div class=\"bg-gray-50 p-4 rounded-lg\">\n                <p class=\"text-sm text-gray-600 mb-2\">文件说明：</p>\n                <ul class=\"text-sm space-y-1\">\n                  <li>\n                    •\n                    <code class=\"bg-gray-200 px-2 py-1 rounded\"\n                      >scanonweb.js</code\n                    >\n                    - 核心控件文件\n                  </li>\n                  <li>• 版本：1.0.1</li>\n                  <li>• 大小：约15KB</li>\n                </ul>\n              </div>\n            </div>\n\n            <!-- 步骤3 -->\n            <div class=\"border-l-4 border-orange-500 pl-6\">\n              <h3 class=\"heading-tertiary mb-3\">步骤3：复制文件到项目</h3>\n              <p class=\"text-business mb-4\">\n                将scanonweb.js文件复制到您的Web项目目录中。建议放在js或assets目录下。\n              </p>\n              <div class=\"bg-gray-100 border border-gray-300 p-4 rounded-lg\">\n                <div class=\"text-sm text-gray-600 mb-2\">项目结构示例：</div>\n                <div class=\"font-mono text-sm text-gray-800\">\n                  <div>your-project/</div>\n                  <div>├── js/</div>\n                  <div>\n                    │&nbsp;&nbsp;&nbsp;└── scanonweb.js&nbsp;&nbsp;<span\n                      class=\"text-orange-600\"\n                      >← 控件文件</span\n                    >\n                  </div>\n                  <div>├── index.html</div>\n                  <div>└── ...</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- HTML集成示例 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">原生HTML集成</h2>\n\n          <div class=\"space-y-6\">\n            <p class=\"text-business\">\n              在原生HTML项目中使用ScanOnWeb控件非常简单，只需要引入JavaScript文件并初始化即可。\n            </p>\n\n            <!-- HTML示例 -->\n            <div>\n              <h3 class=\"heading-tertiary mb-3\">完整HTML示例</h3>\n              <div class=\"bg-gray-50 border border-gray-300 p-4 rounded-lg\">\n                <div class=\"text-sm text-gray-600 mb-3\">\n                  复制以下代码到您的HTML文件中：\n                </div>\n                <div\n                  class=\"bg-white border border-gray-200 p-4 rounded-lg\"\n                  style=\"max-height: 600px; overflow-y: auto\"\n                >\n                  <pre\n                    class=\"font-mono text-sm text-gray-800 whitespace-pre-wrap m-0 p-0\"\n                    >{{ htmlCode }}</pre\n                  >\n                </div>\n              </div>\n            </div>\n\n            <!-- 关键代码说明 -->\n            <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <h4 class=\"font-bold text-blue-800 mb-3\">关键代码说明</h4>\n              <ul class=\"space-y-2 text-blue-700\">\n                <li>\n                  <strong>初始化：</strong>\n                  <code>let scanonweb = new ScanOnWeb();</code>\n                </li>\n                <li>\n                  <strong>获取设备：</strong>\n                  <code>scanonweb.loadDevices()</code>\n                </li>\n                <li>\n                  <strong>开始扫描：</strong> <code>scanonweb.startScan()</code>\n                </li>\n                <li>\n                  <strong>获取图像：</strong>\n                  <code>scanonweb.getAllImage()</code>\n                </li>\n                <li>\n                  <strong>事件回调：</strong> 通过\n                  <code>onScanFinishedEvent</code> 等事件处理扫描结果\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <!-- 技术支持 -->\n        <div class=\"card-business p-8\">\n          <h2 class=\"heading-secondary mb-6\">技术支持</h2>\n\n          <div class=\"bg-orange-50 border border-orange-200 rounded-lg p-6\">\n            <div class=\"flex items-start\">\n              <svg\n                class=\"w-6 h-6 text-orange-500 mr-3 mt-1 flex-shrink-0\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fill-rule=\"evenodd\"\n                  d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\"\n                  clip-rule=\"evenodd\"\n                ></path>\n              </svg>\n              <div>\n                <h4 class=\"font-bold text-orange-800 mb-2\">需要帮助？</h4>\n                <p class=\"text-orange-700 mb-4\">\n                  如果您在集成过程中遇到问题，或需要更多技术支持，请联系我们的技术团队。\n                </p>\n                <div class=\"space-y-2 text-orange-700\">\n                  <p><strong>技术支持邮箱：</strong> <EMAIL></p>\n                  <p><strong>技术支持QQ：</strong> 123456789</p>\n                  <p><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>\n                </div>\n                <div class=\"mt-4\">\n                  <NuxtLink to=\"/contact\" class=\"btn-primary\">\n                    联系技术支持\n                  </NuxtLink>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </main>\n    <Footer />\n  </div>\n</template>\n\n<script setup>\nimport Header from \"~/components/Header.vue\";\nimport Footer from \"~/components/Footer.vue\";\n\n// HTML代码示例\nconst htmlCode =\n  `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>ScanOnWeb 扫描示例</title>\n</head>\n<body>\n    <h1>ScanOnWeb 扫描控件演示</h1>\n\n    <!-- 控制面板 -->\n    <div id=\"controlPanel\">\n        <label>选择扫描设备：</label>\n        <select id=\"deviceSelect\"></select>\n\n        <label>分辨率：</label>\n        <input type=\"number\" id=\"dpiX\" value=\"300\" style=\"width: 60px;\"> x\n        <input type=\"number\" id=\"dpiY\" value=\"300\" style=\"width: 60px;\">\n\n        <label>色彩模式：</label>\n        <select id=\"colorMode\">\n            <option value=\"RGB\">彩色</option>\n            <option value=\"GRAY\">灰色</option>\n            <option value=\"BW\">黑白</option>\n        </select>\n\n        <br><br>\n\n        <button onclick=\"loadDevices()\">获取设备列表</button>\n        <button onclick=\"startScan()\">开始扫描</button>\n        <button onclick=\"getAllImages()\">获取所有图像</button>\n        <button onclick=\"clearAll()\">清空结果</button>\n    </div>\n\n    <!-- 图像显示区域 -->\n    <div id=\"imageContainer\">\n        <h3>扫描结果：</h3>\n        <div id=\"imageList\"></div>\n    </div>\n\n    <!-- 引入ScanOnWeb控件 -->\n    <scr` +\n  `ipt src=\"js/scanonweb.js\"></scr` +\n  `ipt>\n\n    <scr` +\n  `ipt>\n        // 初始化扫描控件\n        let scanonweb = new ScanOnWeb();\n        let selectedDeviceIndex = 0;\n\n        // 获取设备列表\n        function loadDevices() {\n            scanonweb.onGetDevicesListEvent = function(msg) {\n                const deviceSelect = document.getElementById('deviceSelect');\n                deviceSelect.innerHTML = '';\n\n                msg.devices.forEach((device, index) => {\n                    const option = document.createElement('option');\n                    option.value = index;\n                    option.textContent = device;\n                    deviceSelect.appendChild(option);\n                });\n\n                selectedDeviceIndex = msg.currentIndex;\n                deviceSelect.value = selectedDeviceIndex;\n            };\n\n            scanonweb.loadDevices();\n        }\n\n        // 开始扫描\n        function startScan() {\n            if (selectedDeviceIndex === -1) {\n                alert('请先选择扫描设备！');\n                return;\n            }\n\n            // 配置扫描参数\n            scanonweb.scaner_work_config = {\n                deviceIndex: selectedDeviceIndex,\n                dpi_x: parseInt(document.getElementById('dpiX').value),\n                dpi_y: parseInt(document.getElementById('dpiY').value),\n                colorMode: document.getElementById('colorMode').value,\n                showDialog: false,\n                autoFeedEnable: false,\n                autoFeed: false,\n                dupxMode: false,\n                autoDeskew: false,\n                autoBorderDetection: false\n            };\n\n            scanonweb.startScan();\n        }\n\n        // 获取所有图像\n        function getAllImages() {\n            scanonweb.onGetAllImageEvent = function(msg) {\n                const imageList = document.getElementById('imageList');\n                imageList.innerHTML = '';\n\n                msg.images.forEach((imageBase64, index) => {\n                    const img = document.createElement('img');\n                    img.src = 'data:image/jpg;base64,' + imageBase64;\n                    img.style.width = '200px';\n                    img.style.height = '200px';\n                    img.style.margin = '10px';\n                    img.style.border = '1px solid #ccc';\n                    imageList.appendChild(img);\n                });\n            };\n\n            scanonweb.getAllImage();\n        }\n\n        // 清空所有图像\n        function clearAll() {\n            scanonweb.clearAll();\n            document.getElementById('imageList').innerHTML = '';\n        }\n\n        // 设备选择变化事件\n        document.getElementById('deviceSelect').addEventListener('change', function(e) {\n            selectedDeviceIndex = parseInt(e.target.value);\n            scanonweb.selectScanDevice(selectedDeviceIndex);\n        });\n\n        // 扫描完成事件回调\n        scanonweb.onScanFinishedEvent = function(msg) {\n            console.log('扫描完成，图像数量：', msg.imageAfterCount);\n            getAllImages(); // 自动获取扫描结果\n        };\n\n        // 页面加载完成后自动获取设备列表\n        window.onload = function() {\n            loadDevices();\n        };\n    </scr` +\n  `ipt>\n</body>\n</html>`;\n\n// SEO设置\nuseHead({\n  title: \"ScanOnWeb 入门指南 - 快速上手扫描控件\",\n  meta: [\n    {\n      name: \"description\",\n      content:\n        \"ScanOnWeb扫描控件入门指南，详细介绍如何在HTML和Vue3项目中集成使用扫描功能，包含完整代码示例和最佳实践。\",\n    },\n    {\n      name: \"keywords\",\n      content:\n        \"ScanOnWeb,扫描控件,入门指南,HTML,Vue3,JavaScript,WebSocket,扫描仪,集成教程\",\n    },\n  ],\n});\n</script>\n\n<style scoped>\ncode {\n  @apply bg-gray-200 px-2 py-1 rounded text-sm font-mono;\n}\n\npre {\n  @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;\n}\n\n.code-block {\n  @apply bg-gray-50 border border-gray-200 rounded-lg p-4 font-mono text-sm;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAqZA,MAAM,WACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AA+IM,YAAA;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,MACF;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}