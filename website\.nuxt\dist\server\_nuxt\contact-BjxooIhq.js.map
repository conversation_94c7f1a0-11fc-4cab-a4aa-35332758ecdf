{"version": 3, "file": "contact-BjxooIhq.js", "sources": ["../../../../virtual:public?%2Fimages%2Fwechat-qrcode.jpg", "../../../../pages/contact.vue"], "sourcesContent": ["import { publicAssetsURL } from '#internal/nuxt/paths';export default publicAssetsURL(\"/images/wechat-qrcode.jpg\")", "<template>\r\n  <div class=\"bg-gray-50 min-h-screen\">\r\n    <Header />\r\n    <main>\r\n      <!-- 面包屑导航 -->\r\n      <div class=\"bg-white border-b border-gray-200\">\r\n        <div class=\"container mx-auto px-4 py-4\">\r\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\r\n            <svg\r\n              class=\"w-4 h-4\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 5l7 7-7 7\"\r\n              ></path>\r\n            </svg>\r\n            <span class=\"text-gray-900 font-medium\">联系我们</span>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 页面标题区域 -->\r\n      <div class=\"bg-white py-12\">\r\n        <div class=\"container mx-auto px-4\">\r\n          <div class=\"max-w-3xl\">\r\n            <h1 class=\"heading-primary mb-4\">联系我们</h1>\r\n            <p class=\"text-xl text-gray-600 mb-6\">\r\n              我们随时准备为您提供专业的技术支持和服务咨询\r\n            </p>\r\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>24小时内回复</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>专业技术团队</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>免费咨询服务</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 快速联系方式 -->\r\n      <div class=\"bg-orange-50 py-8\">\r\n        <div class=\"container mx-auto px-4\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            <div class=\"text-center\">\r\n              <div\r\n                class=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n              >\r\n                <svg\r\n                  class=\"w-8 h-8 text-orange-500\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\r\n                  ></path>\r\n                </svg>\r\n              </div>\r\n              <h3 class=\"font-bold text-gray-900 mb-2\">电话咨询</h3>\r\n              <p class=\"text-orange-600 font-semibold text-lg\">155-1196-5595</p>\r\n              <p class=\"text-sm text-gray-500\">工作日 9:00-18:00</p>\r\n            </div>\r\n\r\n            <div class=\"text-center\">\r\n              <div\r\n                class=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n              >\r\n                <svg\r\n                  class=\"w-8 h-8 text-orange-500\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\r\n                  ></path>\r\n                </svg>\r\n              </div>\r\n              <h3 class=\"font-bold text-gray-900 mb-2\">邮件联系</h3>\r\n              <p class=\"text-orange-600 font-semibold\"><EMAIL></p>\r\n              <p class=\"text-sm text-gray-500\">24小时内回复</p>\r\n            </div>\r\n\r\n            <div class=\"text-center\">\r\n              <div\r\n                class=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\"\r\n              >\r\n                <svg\r\n                  class=\"w-8 h-8 text-orange-500\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\r\n                  ></path>\r\n                </svg>\r\n              </div>\r\n              <h3 class=\"font-bold text-gray-900 mb-2\">微信咨询</h3>\r\n              <p class=\"text-orange-600 font-semibold\">20155031</p>\r\n              <p class=\"text-sm text-gray-500\">扫码添加微信</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"container mx-auto px-4 py-12\">\r\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n          <!-- 联系表单 -->\r\n          <div class=\"card-business p-8\">\r\n            <div class=\"mb-6\">\r\n              <h2 class=\"heading-secondary mb-2\">发送消息</h2>\r\n              <p class=\"text-gray-600\">请填写以下信息，我们会尽快与您联系</p>\r\n            </div>\r\n\r\n            <form @submit.prevent=\"submitForm\" class=\"space-y-6\">\r\n              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label\r\n                    for=\"name\"\r\n                    class=\"block text-sm font-medium text-gray-700 mb-2\"\r\n                  >\r\n                    姓名 <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    v-model=\"form.name\"\r\n                    required\r\n                    class=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors\"\r\n                    placeholder=\"请输入您的姓名\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label\r\n                    for=\"phone\"\r\n                    class=\"block text-sm font-medium text-gray-700 mb-2\"\r\n                  >\r\n                    电话 <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    type=\"tel\"\r\n                    id=\"phone\"\r\n                    v-model=\"form.phone\"\r\n                    required\r\n                    class=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors\"\r\n                    placeholder=\"请输入您的电话\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label\r\n                  for=\"email\"\r\n                  class=\"block text-sm font-medium text-gray-700 mb-2\"\r\n                >\r\n                  邮箱\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  v-model=\"form.email\"\r\n                  class=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors\"\r\n                  placeholder=\"请输入您的邮箱\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label\r\n                  for=\"subject\"\r\n                  class=\"block text-sm font-medium text-gray-700 mb-2\"\r\n                >\r\n                  咨询类型 <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <select\r\n                  id=\"subject\"\r\n                  v-model=\"form.subject\"\r\n                  required\r\n                  class=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors\"\r\n                >\r\n                  <option value=\"\">请选择咨询类型</option>\r\n                  <option value=\"产品咨询\">产品功能咨询</option>\r\n                  <option value=\"技术支持\">技术支持</option>\r\n                  <option value=\"商务合作\">商务合作</option>\r\n                  <option value=\"售前咨询\">售前咨询</option>\r\n                  <option value=\"其他问题\">其他问题</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label\r\n                  for=\"message\"\r\n                  class=\"block text-sm font-medium text-gray-700 mb-2\"\r\n                >\r\n                  详细描述 <span class=\"text-red-500\">*</span>\r\n                </label>\r\n                <textarea\r\n                  id=\"message\"\r\n                  v-model=\"form.message\"\r\n                  required\r\n                  rows=\"5\"\r\n                  class=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors resize-none\"\r\n                  placeholder=\"请详细描述您的问题或需求，我们会根据您的描述提供更精准的服务\"\r\n                ></textarea>\r\n              </div>\r\n\r\n              <div class=\"pt-4\">\r\n                <button type=\"submit\" class=\"btn-primary w-full py-4 text-lg\">\r\n                  提交咨询\r\n                </button>\r\n                <p class=\"text-sm text-gray-500 mt-3 text-center\">\r\n                  提交后我们会在24小时内与您联系\r\n                </p>\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          <!-- 详细联系信息和服务时间 -->\r\n          <div class=\"space-y-6\">\r\n            <!-- 联系方式详情 -->\r\n            <div class=\"card-business p-6\">\r\n              <h3 class=\"heading-tertiary mb-4\">联系方式详情</h3>\r\n              <div class=\"space-y-4\">\r\n                <div class=\"flex items-start\">\r\n                  <div\r\n                    class=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1\"\r\n                  >\r\n                    <svg\r\n                      class=\"w-5 h-5 text-orange-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"font-semibold text-gray-900\">销售热线</h4>\r\n                    <p class=\"text-orange-600 font-semibold\">155-1196-5595</p>\r\n                    <p class=\"text-sm text-gray-500\">工作日 9:00-18:00</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"flex items-start\">\r\n                  <div\r\n                    class=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1\"\r\n                  >\r\n                    <svg\r\n                      class=\"w-5 h-5 text-orange-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"font-semibold text-gray-900\">商务邮箱</h4>\r\n                    <p class=\"text-orange-600 font-semibold\"><EMAIL></p>\r\n                    <p class=\"text-sm text-gray-500\">24小时内回复</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"flex items-start\">\r\n                  <div\r\n                    class=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1\"\r\n                  >\r\n                    <svg\r\n                      class=\"w-5 h-5 text-orange-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\r\n                      ></path>\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"font-semibold text-gray-900\">公司地址</h4>\r\n                    <p class=\"text-gray-700\">广东省广州市天河区</p>\r\n                    <p class=\"text-sm text-gray-500\">可预约上门服务</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 服务时间 -->\r\n            <div class=\"card-business p-6\">\r\n              <h3 class=\"heading-tertiary mb-4\">服务时间</h3>\r\n              <div class=\"space-y-3\">\r\n                <div\r\n                  class=\"flex justify-between items-center py-2 border-b border-gray-100\"\r\n                >\r\n                  <span class=\"text-gray-700\">销售咨询</span>\r\n                  <span class=\"font-semibold text-gray-900\"\r\n                    >周一至周五 9:00-18:00</span\r\n                  >\r\n                </div>\r\n                <div\r\n                  class=\"flex justify-between items-center py-2 border-b border-gray-100\"\r\n                >\r\n                  <span class=\"text-gray-700\">技术支持</span>\r\n                  <span class=\"font-semibold text-gray-900\">7×24小时</span>\r\n                </div>\r\n                <div class=\"flex justify-between items-center py-2\">\r\n                  <span class=\"text-gray-700\">邮件回复</span>\r\n                  <span class=\"font-semibold text-gray-900\">24小时内</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 微信二维码 -->\r\n            <div class=\"card-business p-6 text-center\">\r\n              <h3 class=\"heading-tertiary mb-4\">微信咨询</h3>\r\n              <div class=\"inline-block bg-gray-50 p-4 rounded-lg\">\r\n                <img\r\n                  src=\"/images/wechat-qrcode.jpg\"\r\n                  alt=\"微信二维码\"\r\n                  class=\"w-32 h-32 mx-auto\"\r\n                />\r\n                <p class=\"text-sm text-gray-600 mt-2\">扫码添加微信</p>\r\n                <p class=\"text-xs text-gray-500\">微信号：20155031</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 常见问题部分 -->\r\n      <div class=\"container mx-auto px-4 py-12\">\r\n        <div class=\"card-business p-8\">\r\n          <div class=\"text-center mb-12\">\r\n            <h2 class=\"heading-secondary mb-4\">常见问题解答</h2>\r\n            <p class=\"text-gray-600\">快速找到您关心的问题答案</p>\r\n          </div>\r\n\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n            <div class=\"space-y-6\">\r\n              <div class=\"border-l-4 border-orange-500 pl-6\">\r\n                <h3 class=\"text-lg font-semibold mb-3 text-gray-900\">\r\n                  产品功能咨询\r\n                </h3>\r\n                <p class=\"text-gray-600 mb-4\">\r\n                  我们的产品支持多种扫描设备和图像处理功能，兼容Windows和Linux平台。如需了解具体功能特性，请查看产品详情页面或联系我们的技术顾问。\r\n                </p>\r\n                <NuxtLink\r\n                  to=\"/products\"\r\n                  class=\"text-orange-500 hover:text-orange-600 font-medium inline-flex items-center\"\r\n                >\r\n                  查看产品详情\r\n                  <svg\r\n                    class=\"w-4 h-4 ml-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M9 5l7 7-7 7\"\r\n                    ></path>\r\n                  </svg>\r\n                </NuxtLink>\r\n              </div>\r\n\r\n              <div class=\"border-l-4 border-orange-500 pl-6\">\r\n                <h3 class=\"text-lg font-semibold mb-3 text-gray-900\">\r\n                  技术支持服务\r\n                </h3>\r\n                <p class=\"text-gray-600 mb-4\">\r\n                  我们提供7×24小时技术支持服务，包括远程协助、现场服务等。技术团队具备丰富的项目经验，能够快速解决各种技术问题。\r\n                </p>\r\n                <NuxtLink\r\n                  to=\"/documents\"\r\n                  class=\"text-orange-500 hover:text-orange-600 font-medium inline-flex items-center\"\r\n                >\r\n                  查看技术文档\r\n                  <svg\r\n                    class=\"w-4 h-4 ml-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M9 5l7 7-7 7\"\r\n                    ></path>\r\n                  </svg>\r\n                </NuxtLink>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"space-y-6\">\r\n              <div class=\"border-l-4 border-orange-500 pl-6\">\r\n                <h3 class=\"text-lg font-semibold mb-3 text-gray-900\">\r\n                  购买和授权\r\n                </h3>\r\n                <p class=\"text-gray-600 mb-4\">\r\n                  我们提供灵活的授权方案和多种支付方式，支持企业采购流程。所有产品均提供正规发票和软件著作权授权文件。\r\n                </p>\r\n                <NuxtLink\r\n                  to=\"/purchase\"\r\n                  class=\"text-orange-500 hover:text-orange-600 font-medium inline-flex items-center\"\r\n                >\r\n                  了解购买流程\r\n                  <svg\r\n                    class=\"w-4 h-4 ml-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M9 5l7 7-7 7\"\r\n                    ></path>\r\n                  </svg>\r\n                </NuxtLink>\r\n              </div>\r\n\r\n              <div class=\"border-l-4 border-orange-500 pl-6\">\r\n                <h3 class=\"text-lg font-semibold mb-3 text-gray-900\">\r\n                  合作与定制\r\n                </h3>\r\n                <p class=\"text-gray-600 mb-4\">\r\n                  我们欢迎各类企业合作，提供产品定制、技术咨询、解决方案设计等服务。可根据您的具体需求提供专业的定制化方案。\r\n                </p>\r\n                <div\r\n                  class=\"text-orange-500 hover:text-orange-600 font-medium inline-flex items-center cursor-pointer\"\r\n                >\r\n                  联系商务合作\r\n                  <svg\r\n                    class=\"w-4 h-4 ml-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M9 5l7 7-7 7\"\r\n                    ></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 快速联系提示 -->\r\n          <div\r\n            class=\"mt-12 p-6 bg-orange-50 rounded-lg border border-orange-200\"\r\n          >\r\n            <div class=\"text-center\">\r\n              <h3 class=\"text-lg font-semibold text-gray-900 mb-2\">\r\n                没有找到您要的答案？\r\n              </h3>\r\n              <p class=\"text-gray-600 mb-4\">我们的专业团队随时为您提供帮助</p>\r\n              <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                <a\r\n                  href=\"tel:155-1196-5595\"\r\n                  class=\"btn-primary inline-flex items-center justify-center\"\r\n                >\r\n                  <svg\r\n                    class=\"w-4 h-4 mr-2\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\r\n                    ></path>\r\n                  </svg>\r\n                  立即致电\r\n                </a>\r\n                <button\r\n                  @click=\"scrollToForm\"\r\n                  class=\"btn-secondary inline-flex items-center justify-center\"\r\n                >\r\n                  <svg\r\n                    class=\"w-4 h-4 mr-2\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\r\n                    ></path>\r\n                  </svg>\r\n                  在线咨询\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n    <Footer />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from \"vue\";\r\nimport Header from \"~/components/Header.vue\";\r\nimport Footer from \"~/components/Footer.vue\";\r\n\r\nconst form = ref({\r\n  name: \"\",\r\n  email: \"\",\r\n  phone: \"\",\r\n  subject: \"\",\r\n  message: \"\",\r\n});\r\n\r\nconst submitForm = () => {\r\n  // 验证必填字段\r\n  if (\r\n    !form.value.name ||\r\n    !form.value.phone ||\r\n    !form.value.subject ||\r\n    !form.value.message\r\n  ) {\r\n    alert(\"请填写所有必填字段\");\r\n    return;\r\n  }\r\n\r\n  // 这里添加表单提交逻辑\r\n  alert(\"感谢您的咨询！我们会在24小时内与您联系。\");\r\n\r\n  // 重置表单\r\n  form.value = {\r\n    name: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    subject: \"\",\r\n    message: \"\",\r\n  };\r\n};\r\n\r\nconst scrollToForm = () => {\r\n  const formElement = document.querySelector(\"form\");\r\n  if (formElement) {\r\n    formElement.scrollIntoView({\r\n      behavior: \"smooth\",\r\n      block: \"start\",\r\n    });\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAuD,MAAe,aAAA,gBAAgB,2BAA2B;;;;;AC8kBjH,UAAM,OAAO,IAAI;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IAAA,CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}