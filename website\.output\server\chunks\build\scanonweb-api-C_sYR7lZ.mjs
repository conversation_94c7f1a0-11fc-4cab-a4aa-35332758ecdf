import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { u as useHead } from './index-BabADJUJ.mjs';
import { mergeProps, withCtx, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '@unhead/shared';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import 'vue-router';

const _sfc_main = {
  __name: "scanonweb-api",
  __ssrInlineRender: true,
  setup(__props) {
    useHead({
      title: "ScanOnWeb API\u6587\u6863 - \u5B8C\u6574JavaScript API\u53C2\u8003",
      meta: [
        {
          name: "description",
          content: "ScanOnWeb\u626B\u63CF\u63A7\u4EF6\u5B8C\u6574API\u6587\u6863\uFF0C\u5305\u542B\u6240\u6709JavaScript\u65B9\u6CD5\u3001\u5C5E\u6027\u3001\u4E8B\u4EF6\u56DE\u8C03\u7684\u8BE6\u7EC6\u8BF4\u660E\u548C\u4EE3\u7801\u793A\u4F8B\u3002"
        },
        {
          name: "keywords",
          content: "ScanOnWeb,API\u6587\u6863,JavaScript,\u626B\u63CF\u63A7\u4EF6,\u65B9\u6CD5\u53C2\u8003,\u4E8B\u4EF6\u56DE\u8C03,\u53C2\u6570\u8BF4\u660E"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-d2fddfc7>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-d2fddfc7><div class="bg-white border-b border-gray-200" data-v-d2fddfc7><div class="container mx-auto px-4 py-4" data-v-d2fddfc7><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-d2fddfc7>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d2fddfc7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-d2fddfc7></path></svg>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u6587\u6863\u8D44\u6599`);
          } else {
            return [
              createTextVNode("\u6587\u6863\u8D44\u6599")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d2fddfc7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-d2fddfc7></path></svg><span class="text-gray-900 font-medium" data-v-d2fddfc7>ScanOnWeb API\u6587\u6863</span></nav></div></div><div class="bg-white py-8" data-v-d2fddfc7><div class="container mx-auto px-4" data-v-d2fddfc7><div class="max-w-4xl" data-v-d2fddfc7><h1 class="heading-primary mb-4" data-v-d2fddfc7>ScanOnWeb API\u6587\u6863</h1><p class="text-lg text-gray-600 mb-6" data-v-d2fddfc7> \u5B8C\u6574\u7684ScanOnWeb\u626B\u63CF\u63A7\u4EF6JavaScript API\u53C2\u8003\u6587\u6863\uFF0C\u5305\u542B\u6240\u6709\u65B9\u6CD5\u3001\u5C5E\u6027\u548C\u4E8B\u4EF6\u56DE\u8C03 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-d2fddfc7><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>\u5B8C\u6574API\u53C2\u8003</span></div><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>\u8BE6\u7EC6\u53C2\u6570\u8BF4\u660E</span></div><div class="flex items-center" data-v-d2fddfc7><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-d2fddfc7><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-d2fddfc7></path></svg><span data-v-d2fddfc7>\u4EE3\u7801\u793A\u4F8B</span></div></div></div></div></div><div class="container mx-auto py-12 px-4" data-v-d2fddfc7><div class="card-business p-6 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-4" data-v-d2fddfc7>\u6587\u6863\u76EE\u5F55</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-d2fddfc7><a href="#constructor" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u6784\u9020\u51FD\u6570</div><div class="text-sm text-gray-600" data-v-d2fddfc7>ScanOnWeb()</div></a><a href="#config" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u914D\u7F6E\u5C5E\u6027</div><div class="text-sm text-gray-600" data-v-d2fddfc7>scaner_work_config</div></a><a href="#device-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u8BBE\u5907\u7BA1\u7406</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u8BBE\u5907\u76F8\u5173\u65B9\u6CD5</div></a><a href="#scan-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u626B\u63CF\u64CD\u4F5C</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u626B\u63CF\u76F8\u5173\u65B9\u6CD5</div></a><a href="#image-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u56FE\u50CF\u5904\u7406</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u56FE\u50CF\u76F8\u5173\u65B9\u6CD5</div></a><a href="#upload-methods" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u4E0A\u4F20\u4FDD\u5B58</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u6587\u4EF6\u64CD\u4F5C\u65B9\u6CD5</div></a><a href="#events" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u4E8B\u4EF6\u56DE\u8C03</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u6240\u6709\u4E8B\u4EF6\u56DE\u8C03</div></a><a href="#examples" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u4F7F\u7528\u793A\u4F8B</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u5B8C\u6574\u4EE3\u7801\u793A\u4F8B</div></a><a href="#server-examples" class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors" data-v-d2fddfc7><div class="font-medium text-gray-900" data-v-d2fddfc7>\u670D\u52A1\u5668\u7AEF\u4EE3\u7801</div><div class="text-sm text-gray-600" data-v-d2fddfc7>\u540E\u7AEF\u63A5\u6536\u793A\u4F8B</div></a></div></div><div id="constructor" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u6784\u9020\u51FD\u6570</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>new ScanOnWeb()</h3><p class="api-description" data-v-d2fddfc7> \u521B\u5EFAScanOnWeb\u626B\u63CF\u63A7\u4EF6\u5B9E\u4F8B\uFF0C\u81EA\u52A8\u521D\u59CB\u5316WebSocket\u8FDE\u63A5\u5E76\u5C1D\u8BD5\u8FDE\u63A5\u672C\u5730\u6258\u76D8\u670D\u52A1\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u521B\u5EFA\u626B\u63CF\u63A7\u4EF6\u5B9E\u4F8B
let scanonweb = new ScanOnWeb();

// \u8BBE\u7F6E\u4E8B\u4EF6\u56DE\u8C03
scanonweb.onScanFinishedEvent = function(msg) {
    console.log(&#39;\u626B\u63CF\u5B8C\u6210\uFF0C\u56FE\u50CF\u6570\u91CF\uFF1A&#39;, msg.imageAfterCount);
};</pre></div></div><div class="api-notes" data-v-d2fddfc7><h4 class="api-notes-title" data-v-d2fddfc7>\u6CE8\u610F\u4E8B\u9879</h4><ul class="api-notes-list" data-v-d2fddfc7><li data-v-d2fddfc7> \u6784\u9020\u51FD\u6570\u4F1A\u81EA\u52A8\u5C1D\u8BD5\u8FDE\u63A5\u672C\u5730WebSocket\u670D\u52A1\uFF08\u7AEF\u53E31001-5001\uFF09 </li><li data-v-d2fddfc7>\u9700\u8981\u786E\u4FDD\u672C\u5730\u5DF2\u5B89\u88C5\u5E76\u8FD0\u884CScanOnWeb\u6258\u76D8\u670D\u52A1\u7A0B\u5E8F</li><li data-v-d2fddfc7>\u5982\u679C\u8FDE\u63A5\u5931\u8D25\uFF0C\u76F8\u5173\u64CD\u4F5C\u65B9\u6CD5\u5C06\u65E0\u6CD5\u6B63\u5E38\u5DE5\u4F5C</li></ul></div></div></div><div id="config" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u914D\u7F6E\u5C5E\u6027</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>scaner_work_config</h3><p class="api-description" data-v-d2fddfc7> \u626B\u63CF\u5DE5\u4F5C\u914D\u7F6E\u5BF9\u8C61\uFF0C\u5305\u542B\u6240\u6709\u626B\u63CF\u53C2\u6570\u8BBE\u7F6E\u3002\u5728\u8C03\u7528startScan()\u65B9\u6CD5\u524D\u9700\u8981\u6B63\u786E\u914D\u7F6E\u8FD9\u4E9B\u53C2\u6570\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u914D\u7F6E\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u9ED8\u8BA4\u503C</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>showUI</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u663E\u793A\u626B\u63CF\u63A7\u4EF6\u5DE5\u4F5C\u754C\u9762</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dpi_x</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>300</td><td data-v-d2fddfc7>\u6C34\u5E73\u5206\u8FA8\u7387\uFF08DPI\uFF09</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dpi_y</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>300</td><td data-v-d2fddfc7>\u5782\u76F4\u5206\u8FA8\u7387\uFF08DPI\uFF09</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>deviceIndex</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>0</td><td data-v-d2fddfc7>\u9009\u4E2D\u7684\u626B\u63CF\u8BBE\u5907\u7D22\u5F15</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>showDialog</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u663E\u793A\u8BBE\u5907\u5185\u7F6E\u5BF9\u8BDD\u6846</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoFeedEnable</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>true</td><td data-v-d2fddfc7>\u662F\u5426\u542F\u7528\u81EA\u52A8\u8FDB\u7EB8\u5668</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoFeed</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u81EA\u52A8\u88C5\u586B\u7EB8\u5F20</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>dupxMode</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u542F\u7528\u53CC\u9762\u626B\u63CF\u6A21\u5F0F</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoDeskew</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u542F\u7528\u81EA\u52A8\u7EA0\u504F</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>autoBorderDetection</code></td><td data-v-d2fddfc7>Boolean</td><td data-v-d2fddfc7>false</td><td data-v-d2fddfc7>\u662F\u5426\u542F\u7528\u81EA\u52A8\u8FB9\u6846\u68C0\u6D4B</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>colorMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>&quot;RGB&quot;</td><td data-v-d2fddfc7>\u8272\u5F69\u6A21\u5F0F\uFF1ARGB(\u5F69\u8272)\u3001GRAY(\u7070\u8272)\u3001BW(\u9ED1\u767D)</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>transMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>&quot;memory&quot;</td><td data-v-d2fddfc7>\u6570\u636E\u4F20\u8F93\u6A21\u5F0F\uFF1Amemory\u3001file\u3001native</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u914D\u7F6E\u793A\u4F8B</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u914D\u7F6E\u626B\u63CF\u53C2\u6570
scanonweb.scaner_work_config = {
    showUI: false,
    dpi_x: 600,
    dpi_y: 600,
    deviceIndex: 0,
    showDialog: false,
    autoFeedEnable: true,
    autoFeed: false,
    dupxMode: true,  // \u542F\u7528\u53CC\u9762\u626B\u63CF
    autoDeskew: true,  // \u542F\u7528\u81EA\u52A8\u7EA0\u504F
    autoBorderDetection: true,  // \u542F\u7528\u8FB9\u6846\u68C0\u6D4B
    colorMode: &quot;RGB&quot;,
    transMode: &quot;memory&quot;
};</pre></div></div></div></div><div id="device-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u8BBE\u5907\u7BA1\u7406\u65B9\u6CD5</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>loadDevices()</h3><p class="api-description" data-v-d2fddfc7> \u83B7\u53D6\u7CFB\u7EDF\u4E2D\u6240\u6709\u53EF\u7528\u7684\u626B\u63CF\u8BBE\u5907\u5217\u8868\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onGetDevicesListEvent\u4E8B\u4EF6\u56DE\u8C03\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u8BBE\u5907\u5217\u8868\u56DE\u8C03
scanonweb.onGetDevicesListEvent = function(msg) {
    console.log(&#39;\u8BBE\u5907\u5217\u8868:&#39;, msg.devices);
    console.log(&#39;\u5F53\u524D\u9009\u4E2D\u8BBE\u5907\u7D22\u5F15:&#39;, msg.currentIndex);

    // \u586B\u5145\u8BBE\u5907\u9009\u62E9\u4E0B\u62C9\u6846
    const deviceSelect = document.getElementById(&#39;deviceSelect&#39;);
    deviceSelect.innerHTML = &#39;&#39;;
    msg.devices.forEach((device, index) =&gt; {
        const option = document.createElement(&#39;option&#39;);
        option.value = index;
        option.textContent = device;
        deviceSelect.appendChild(option);
    });
};

// \u83B7\u53D6\u8BBE\u5907\u5217\u8868
scanonweb.loadDevices();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>selectScanDevice(deviceIndex)</h3><p class="api-description" data-v-d2fddfc7>\u9009\u62E9\u6307\u5B9A\u7684\u626B\u63CF\u8BBE\u5907\u4F5C\u4E3A\u5F53\u524D\u5DE5\u4F5C\u8BBE\u5907\u3002</p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>deviceIndex</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u8BBE\u5907\u7D22\u5F15\uFF0C\u4ECE0\u5F00\u59CB</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u9009\u62E9\u7B2C\u4E00\u4E2A\u8BBE\u5907
scanonweb.selectScanDevice(0);

// \u9009\u62E9\u7B2C\u4E8C\u4E2A\u8BBE\u5907
scanonweb.selectScanDevice(1);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> setLicenseKey(licenseMode, key1, key2, licenseServerUrl) </h3><p class="api-description" data-v-d2fddfc7> \u8BBE\u7F6E\u8F6F\u4EF6\u6388\u6743\u4FE1\u606F\u3002\u5728\u4F7F\u7528\u626B\u63CF\u529F\u80FD\u524D\u9700\u8981\u8BBE\u7F6E\u6709\u6548\u7684\u6388\u6743\u5BC6\u94A5\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>licenseMode</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u6388\u6743\u6A21\u5F0F</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>key1</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u6388\u6743\u5BC6\u94A51</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>key2</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u6388\u6743\u5BC6\u94A52</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>licenseServerUrl</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u5426</td><td data-v-d2fddfc7>\u6388\u6743\u670D\u52A1\u5668URL</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u6388\u6743\u4FE1\u606F
scanonweb.setLicenseKey(
    &quot;online&quot;,
    &quot;your-license-key-1&quot;,
    &quot;your-license-key-2&quot;,
    &quot;https://license.brainysoft.cn&quot;
);</pre></div></div></div></div><div id="scan-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u626B\u63CF\u64CD\u4F5C\u65B9\u6CD5</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>startScan()</h3><p class="api-description" data-v-d2fddfc7> \u5F00\u59CB\u626B\u63CF\u64CD\u4F5C\u3002\u4F7F\u7528\u5F53\u524D\u7684scaner_work_config\u914D\u7F6E\u8FDB\u884C\u626B\u63CF\u3002\u626B\u63CF\u5B8C\u6210\u540E\u4F1A\u89E6\u53D1onScanFinishedEvent\u4E8B\u4EF6\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u914D\u7F6E\u626B\u63CF\u53C2\u6570
scanonweb.scaner_work_config.dpi_x = 600;
scanonweb.scaner_work_config.dpi_y = 600;
scanonweb.scaner_work_config.colorMode = &quot;RGB&quot;;
scanonweb.scaner_work_config.deviceIndex = 0;

// \u8BBE\u7F6E\u626B\u63CF\u5B8C\u6210\u56DE\u8C03
scanonweb.onScanFinishedEvent = function(msg) {
    console.log(&#39;\u626B\u63CF\u524D\u56FE\u50CF\u6570\u91CF:&#39;, msg.imageBeforeCount);
    console.log(&#39;\u626B\u63CF\u540E\u56FE\u50CF\u6570\u91CF:&#39;, msg.imageAfterCount);

    // \u81EA\u52A8\u83B7\u53D6\u626B\u63CF\u7ED3\u679C
    scanonweb.getAllImage();
};

// \u5F00\u59CB\u626B\u63CF
scanonweb.startScan();</pre></div></div><div class="api-notes" data-v-d2fddfc7><h4 class="api-notes-title" data-v-d2fddfc7>\u6CE8\u610F\u4E8B\u9879</h4><ul class="api-notes-list" data-v-d2fddfc7><li data-v-d2fddfc7>\u626B\u63CF\u524D\u786E\u4FDD\u5DF2\u9009\u62E9\u6B63\u786E\u7684\u8BBE\u5907\uFF08deviceIndex\uFF09</li><li data-v-d2fddfc7>\u786E\u4FDD\u8BBE\u5907\u5DF2\u8FDE\u63A5\u5E76\u5904\u4E8E\u5C31\u7EEA\u72B6\u6001</li><li data-v-d2fddfc7>\u626B\u63CF\u53C2\u6570\u914D\u7F6E\u4F1A\u5F71\u54CD\u626B\u63CF\u8D28\u91CF\u548C\u901F\u5EA6</li></ul></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>clearAll()</h3><p class="api-description" data-v-d2fddfc7> \u6E05\u9664\u6240\u6709\u5DF2\u626B\u63CF\u7684\u56FE\u50CF\u6570\u636E\uFF0C\u91CA\u653E\u5185\u5B58\u7A7A\u95F4\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u6E05\u9664\u6240\u6709\u56FE\u50CF
scanonweb.clearAll();

// \u540C\u65F6\u6E05\u9664\u9875\u9762\u663E\u793A
document.getElementById(&#39;imageList&#39;).innerHTML = &#39;&#39;;</pre></div></div></div></div><div id="image-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u56FE\u50CF\u5904\u7406\u65B9\u6CD5</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getAllImage()</h3><p class="api-description" data-v-d2fddfc7> \u83B7\u53D6\u6240\u6709\u5DF2\u626B\u63CF\u7684\u56FE\u50CF\u6570\u636E\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onGetAllImageEvent\u4E8B\u4EF6\u56DE\u8C03\uFF0C\u8FD4\u56DEBase64\u7F16\u7801\u7684\u56FE\u50CF\u6570\u636E\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u83B7\u53D6\u56FE\u50CF\u56DE\u8C03
scanonweb.onGetAllImageEvent = function(msg) {
    console.log(&#39;\u56FE\u50CF\u6570\u91CF:&#39;, msg.imageCount);
    console.log(&#39;\u5F53\u524D\u9009\u4E2D:&#39;, msg.currentSelected);

    // \u663E\u793A\u56FE\u50CF
    const imageList = document.getElementById(&#39;imageList&#39;);
    imageList.innerHTML = &#39;&#39;;

    msg.images.forEach((imageBase64, index) =&gt; {
        const img = document.createElement(&#39;img&#39;);
        img.src = &#39;data:image/jpg;base64,&#39; + imageBase64;
        img.style.width = &#39;200px&#39;;
        img.style.height = &#39;200px&#39;;
        img.style.margin = &#39;10px&#39;;
        img.style.border = &#39;1px solid #ccc&#39;;
        imageList.appendChild(img);
    });
};

// \u83B7\u53D6\u6240\u6709\u56FE\u50CF
scanonweb.getAllImage();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageById(index)</h3><p class="api-description" data-v-d2fddfc7> \u83B7\u53D6\u6307\u5B9A\u7D22\u5F15\u7684\u5355\u5F20\u56FE\u50CF\u6570\u636E\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onGetImageByIdEvent\u4E8B\u4EF6\u56DE\u8C03\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u7D22\u5F15\uFF0C\u4ECE0\u5F00\u59CB</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u5355\u5F20\u56FE\u50CF\u56DE\u8C03
scanonweb.onGetImageByIdEvent = function(msg) {
    console.log(&#39;\u56FE\u50CF\u7D22\u5F15:&#39;, msg.imageIndex);
    console.log(&#39;\u56FE\u50CF\u6570\u636E:&#39;, msg.imageBase64);

    // \u663E\u793A\u56FE\u50CF
    const img = document.createElement(&#39;img&#39;);
    img.src = &#39;data:image/jpg;base64,&#39; + msg.imageBase64;
    document.body.appendChild(img);
};

// \u83B7\u53D6\u7B2C\u4E00\u5F20\u56FE\u50CF
scanonweb.getImageById(0);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageCount()</h3><p class="api-description" data-v-d2fddfc7> \u83B7\u53D6\u5F53\u524D\u5DF2\u626B\u63CF\u7684\u56FE\u50CF\u603B\u6570\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onGetImageCountEvent\u4E8B\u4EF6\u56DE\u8C03\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u56FE\u50CF\u8BA1\u6570\u56DE\u8C03
scanonweb.onGetImageCountEvent = function(msg) {
    console.log(&#39;\u56FE\u50CF\u603B\u6570:&#39;, msg.imageCount);
    console.log(&#39;\u5F53\u524D\u9009\u4E2D\u56FE\u50CF\u7D22\u5F15:&#39;, msg.currentSelected);
};

// \u83B7\u53D6\u56FE\u50CF\u6570\u91CF
scanonweb.getImageCount();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>rotateImage(index, angle)</h3><p class="api-description" data-v-d2fddfc7> \u65CB\u8F6C\u6307\u5B9A\u7D22\u5F15\u7684\u56FE\u50CF\u3002\u652F\u630190\u5EA6\u7684\u500D\u6570\u65CB\u8F6C\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u7D22\u5F15\uFF0C\u4ECE0\u5F00\u59CB</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>angle</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u65CB\u8F6C\u89D2\u5EA6\uFF1A90\u3001180\u3001270</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u5C06\u7B2C\u4E00\u5F20\u56FE\u50CF\u987A\u65F6\u9488\u65CB\u8F6C90\u5EA6
scanonweb.rotateImage(0, 90);

// \u5C06\u7B2C\u4E8C\u5F20\u56FE\u50CF\u65CB\u8F6C180\u5EA6
scanonweb.rotateImage(1, 180);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>getImageSize(index)</h3><p class="api-description" data-v-d2fddfc7> \u83B7\u53D6\u6307\u5B9A\u7D22\u5F15\u56FE\u50CF\u7684\u5C3A\u5BF8\u4FE1\u606F\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onGetImageSizeEvent\u4E8B\u4EF6\u56DE\u8C03\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u7D22\u5F15\uFF0C\u4ECE0\u5F00\u59CB</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u56FE\u50CF\u5C3A\u5BF8\u56DE\u8C03
scanonweb.onGetImageSizeEvent = function(msg) {
    console.log(&#39;\u56FE\u50CF\u5BBD\u5EA6:&#39;, msg.width);
    console.log(&#39;\u56FE\u50CF\u9AD8\u5EA6:&#39;, msg.height);
    console.log(&#39;\u56FE\u50CF\u7D22\u5F15:&#39;, msg.imageIndex);
};

// \u83B7\u53D6\u7B2C\u4E00\u5F20\u56FE\u50CF\u5C3A\u5BF8
scanonweb.getImageSize(0);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>loadImageFromUrl(url)</h3><p class="api-description" data-v-d2fddfc7> \u4ECE\u8FDC\u7A0BURL\u52A0\u8F7D\u56FE\u50CF\u5230\u626B\u63CF\u63A7\u4EF6\u4E2D\u3002\u652F\u6301\u591A\u9875\u56FE\u50CF\u6587\u4EF6\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u6587\u4EF6\u7684URL\u5730\u5740</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u4ECE\u670D\u52A1\u5668\u52A0\u8F7D\u56FE\u50CF
scanonweb.loadImageFromUrl(&#39;https://example.com/document.pdf&#39;);

// \u52A0\u8F7D\u672C\u5730\u670D\u52A1\u5668\u56FE\u50CF
scanonweb.loadImageFromUrl(&#39;/uploads/scan_result.tiff&#39;);</pre></div></div></div></div><div id="upload-methods" class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u4E0A\u4F20\u4FDD\u5B58\u65B9\u6CD5</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> uploadAllImageAsPdfToUrl(url, id, desc) </h3><p class="api-description" data-v-d2fddfc7> \u5C06\u6240\u6709\u56FE\u50CF\u5408\u5E76\u4E3APDF\u683C\u5F0F\u5E76\u4E0A\u4F20\u5230\u6307\u5B9AURL\u3002\u8C03\u7528\u540E\u4F1A\u89E6\u53D1onUploadAllImageAsPdfToUrlEvent\u4E8B\u4EF6\u56DE\u8C03\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u4E0A\u4F20\u76EE\u6807URL\u5730\u5740</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>id</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u6587\u6863\u6807\u8BC6ID</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>desc</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u5426</td><td data-v-d2fddfc7>\u6587\u6863\u63CF\u8FF0\u4FE1\u606F</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u8BBE\u7F6E\u4E0A\u4F20\u56DE\u8C03
scanonweb.onUploadAllImageAsPdfToUrlEvent = function(msg) {
    const result = JSON.parse(msg.uploadResult);
    if (result.network) {
        console.log(&#39;\u4E0A\u4F20\u6210\u529F:&#39;, result.msg);
    } else {
        console.error(&#39;\u4E0A\u4F20\u5931\u8D25:&#39;, result.msg);
    }
};

// \u4E0A\u4F20PDF\u5230\u670D\u52A1\u5668
scanonweb.uploadAllImageAsPdfToUrl(
    &#39;https://api.example.com/upload&#39;,
    &#39;DOC_001&#39;,
    &#39;\u626B\u63CF\u6587\u6863&#39;
);</pre></div></div><div id="server-examples" class="api-server-examples" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u670D\u52A1\u5668\u7AEF\u63A5\u6536\u4EE3\u7801\u793A\u4F8B</h4><p class="text-gray-600 mb-4" data-v-d2fddfc7> ScanOnWeb\u63A7\u4EF6\u901A\u8FC7multipart/form-data\u65B9\u5F0F\u63D0\u4EA4\u6570\u636E\uFF0C\u5305\u542B\u4EE5\u4E0B4\u4E2A\u53C2\u6570\uFF1A </p><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6" data-v-d2fddfc7><h5 class="font-bold text-blue-800 mb-2" data-v-d2fddfc7>\u63D0\u4EA4\u53C2\u6570\u8BF4\u660E</h5><ul class="space-y-1 text-blue-700 text-sm" data-v-d2fddfc7><li data-v-d2fddfc7><strong data-v-d2fddfc7>image</strong> - \u4E0A\u4F20\u7684\u56FE\u50CF\u6587\u4EF6\u4E8C\u8FDB\u5236\u6570\u636E\uFF08PDF\u683C\u5F0F\uFF09 </li><li data-v-d2fddfc7><strong data-v-d2fddfc7>imageCount</strong> - \u672C\u6B21\u4E0A\u4F20\u7684\u56FE\u50CF\u603B\u6570</li><li data-v-d2fddfc7><strong data-v-d2fddfc7>id</strong> - \u8C03\u7528\u65B9\u6CD5\u65F6\u4F20\u5165\u7684\u4E1A\u52A1ID\u53C2\u6570</li><li data-v-d2fddfc7><strong data-v-d2fddfc7>desc</strong> - \u8C03\u7528\u65B9\u6CD5\u65F6\u4F20\u5165\u7684\u63CF\u8FF0\u4FE1\u606F\u53C2\u6570</li></ul></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Java Spring Boot</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>@RestController
@RequestMapping(&quot;/api&quot;)
public class ScanUploadController {

    @PostMapping(&quot;/upload&quot;)
    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; uploadScanImages(
            @RequestParam(&quot;image&quot;) MultipartFile imageFile,
            @RequestParam(&quot;imageCount&quot;) Integer imageCount,
            @RequestParam(&quot;id&quot;) String id,
            @RequestParam(value = &quot;desc&quot;, required = false) String desc) {

        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();

        try {
            // \u9A8C\u8BC1\u6587\u4EF6
            if (imageFile.isEmpty()) {
                response.put(&quot;network&quot;, false);
                response.put(&quot;msg&quot;, &quot;\u4E0A\u4F20\u6587\u4EF6\u4E3A\u7A7A&quot;);
                return ResponseEntity.badRequest().body(response);
            }

            // \u751F\u6210\u6587\u4EF6\u540D
            String fileName = id + &quot;_&quot; + System.currentTimeMillis() + &quot;.pdf&quot;;
            String uploadDir = &quot;/uploads/scan/&quot;;
            Path uploadPath = Paths.get(uploadDir);

            // \u521B\u5EFA\u76EE\u5F55
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // \u4FDD\u5B58\u6587\u4EF6
            Path filePath = uploadPath.resolve(fileName);
            Files.copy(imageFile.getInputStream(), filePath,
                      StandardCopyOption.REPLACE_EXISTING);

            // \u8BB0\u5F55\u5230\u6570\u636E\u5E93
            ScanDocument document = new ScanDocument();
            document.setBusinessId(id);
            document.setDescription(desc);
            document.setImageCount(imageCount);
            document.setFilePath(filePath.toString());
            document.setFileName(fileName);
            document.setFileSize(imageFile.getSize());
            document.setUploadTime(new Date());

            scanDocumentService.save(document);

            // \u8FD4\u56DE\u6210\u529F\u54CD\u5E94
            response.put(&quot;network&quot;, true);
            response.put(&quot;msg&quot;, &quot;\u4E0A\u4F20\u6210\u529F&quot;);
            response.put(&quot;fileId&quot;, document.getId());
            response.put(&quot;fileName&quot;, fileName);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error(&quot;\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25&quot;, e);
            response.put(&quot;network&quot;, false);
            response.put(&quot;msg&quot;, &quot;\u4E0A\u4F20\u5931\u8D25: &quot; + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>ASP.NET Core</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>[ApiController]
[Route(&quot;api/[controller]&quot;)]
public class ScanUploadController : ControllerBase
{
    private readonly ILogger&lt;ScanUploadController&gt; _logger;
    private readonly IScanDocumentService _scanDocumentService;

    public ScanUploadController(ILogger&lt;ScanUploadController&gt; logger,
                               IScanDocumentService scanDocumentService)
    {
        _logger = logger;
        _scanDocumentService = scanDocumentService;
    }

    [HttpPost(&quot;upload&quot;)]
    public async Task&lt;IActionResult&gt; UploadScanImages(
        [FromForm] IFormFile image,
        [FromForm] int imageCount,
        [FromForm] string id,
        [FromForm] string desc = null)
    {
        try
        {
            // \u9A8C\u8BC1\u6587\u4EF6
            if (image == null || image.Length == 0)
            {
                return BadRequest(new { network = false, msg = &quot;\u4E0A\u4F20\u6587\u4EF6\u4E3A\u7A7A&quot; });
            }

            // \u751F\u6210\u6587\u4EF6\u540D
            var fileName = $&quot;{id}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}.pdf&quot;;
            var uploadDir = Path.Combine(Directory.GetCurrentDirectory(), &quot;uploads&quot;, &quot;scan&quot;);

            // \u521B\u5EFA\u76EE\u5F55
            if (!Directory.Exists(uploadDir))
            {
                Directory.CreateDirectory(uploadDir);
            }

            // \u4FDD\u5B58\u6587\u4EF6
            var filePath = Path.Combine(uploadDir, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await image.CopyToAsync(stream);
            }

            // \u4FDD\u5B58\u5230\u6570\u636E\u5E93
            var document = new ScanDocument
            {
                BusinessId = id,
                Description = desc,
                ImageCount = imageCount,
                FilePath = filePath,
                FileName = fileName,
                FileSize = image.Length,
                UploadTime = DateTime.UtcNow
            };

            await _scanDocumentService.SaveAsync(document);

            // \u8FD4\u56DE\u6210\u529F\u54CD\u5E94
            return Ok(new
            {
                network = true,
                msg = &quot;\u4E0A\u4F20\u6210\u529F&quot;,
                fileId = document.Id,
                fileName = fileName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, &quot;\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25&quot;);
            return StatusCode(500, new { network = false, msg = $&quot;\u4E0A\u4F20\u5931\u8D25: {ex.Message}&quot; });
        }
    }
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Go (Gin Framework)</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>package main

import (
    &quot;fmt&quot;
    &quot;io&quot;
    &quot;net/http&quot;
    &quot;os&quot;
    &quot;path/filepath&quot;
    &quot;strconv&quot;
    &quot;time&quot;

    &quot;github.com/gin-gonic/gin&quot;
)

type ScanDocument struct {
    ID          uint      \`json:&quot;id&quot; gorm:&quot;primaryKey&quot;\`
    BusinessID  string    \`json:&quot;business_id&quot;\`
    Description string    \`json:&quot;description&quot;\`
    ImageCount  int       \`json:&quot;image_count&quot;\`
    FilePath    string    \`json:&quot;file_path&quot;\`
    FileName    string    \`json:&quot;file_name&quot;\`
    FileSize    int64     \`json:&quot;file_size&quot;\`
    UploadTime  time.Time \`json:&quot;upload_time&quot;\`
}

func uploadScanImages(c *gin.Context) {
    // \u83B7\u53D6\u8868\u5355\u53C2\u6570
    imageFile, header, err := c.Request.FormFile(&quot;image&quot;)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;\u83B7\u53D6\u4E0A\u4F20\u6587\u4EF6\u5931\u8D25: &quot; + err.Error(),
        })
        return
    }
    defer imageFile.Close()

    imageCount, _ := strconv.Atoi(c.PostForm(&quot;imageCount&quot;))
    id := c.PostForm(&quot;id&quot;)
    desc := c.PostForm(&quot;desc&quot;)

    // \u9A8C\u8BC1\u6587\u4EF6
    if header.Size == 0 {
        c.JSON(http.StatusBadRequest, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;\u4E0A\u4F20\u6587\u4EF6\u4E3A\u7A7A&quot;,
        })
        return
    }

    // \u751F\u6210\u6587\u4EF6\u540D
    fileName := fmt.Sprintf(&quot;%s_%d.pdf&quot;, id, time.Now().UnixMilli())
    uploadDir := &quot;./uploads/scan&quot;

    // \u521B\u5EFA\u76EE\u5F55
    if err := os.MkdirAll(uploadDir, 0755); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;\u521B\u5EFA\u76EE\u5F55\u5931\u8D25: &quot; + err.Error(),
        })
        return
    }

    // \u4FDD\u5B58\u6587\u4EF6
    filePath := filepath.Join(uploadDir, fileName)
    dst, err := os.Create(filePath)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;\u521B\u5EFA\u6587\u4EF6\u5931\u8D25: &quot; + err.Error(),
        })
        return
    }
    defer dst.Close()

    if _, err := io.Copy(dst, imageFile); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            &quot;network&quot;: false,
            &quot;msg&quot;:     &quot;\u4FDD\u5B58\u6587\u4EF6\u5931\u8D25: &quot; + err.Error(),
        })
        return
    }

    // \u4FDD\u5B58\u5230\u6570\u636E\u5E93
    document := ScanDocument{
        BusinessID:  id,
        Description: desc,
        ImageCount:  imageCount,
        FilePath:    filePath,
        FileName:    fileName,
        FileSize:    header.Size,
        UploadTime:  time.Now(),
    }

    // \u8FD9\u91CC\u5E94\u8BE5\u8C03\u7528\u6570\u636E\u5E93\u670D\u52A1\u4FDD\u5B58\u8BB0\u5F55
    // db.Create(&amp;document)

    // \u8FD4\u56DE\u6210\u529F\u54CD\u5E94
    c.JSON(http.StatusOK, gin.H{
        &quot;network&quot;:  true,
        &quot;msg&quot;:      &quot;\u4E0A\u4F20\u6210\u529F&quot;,
        &quot;fileId&quot;:   document.ID,
        &quot;fileName&quot;: fileName,
    })
}

func main() {
    r := gin.Default()
    r.POST(&quot;/api/upload&quot;, uploadScanImages)
    r.Run(&quot;:8080&quot;)
}</pre></div></div><div class="server-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>Rust (Actix-web)</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>use actix_multipart::Multipart;
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use futures::{StreamExt, TryStreamExt};
use serde::{Deserialize, Serialize};
use std::io::Write;
use tokio::fs;
use uuid::Uuid;

#[derive(Serialize, Deserialize)]
struct ScanDocument {
    id: Option&lt;u32&gt;,
    business_id: String,
    description: Option&lt;String&gt;,
    image_count: i32,
    file_path: String,
    file_name: String,
    file_size: u64,
    upload_time: chrono::DateTime&lt;chrono::Utc&gt;,
}

#[derive(Serialize)]
struct UploadResponse {
    network: bool,
    msg: String,
    #[serde(skip_serializing_if = &quot;Option::is_none&quot;)]
    file_id: Option&lt;u32&gt;,
    #[serde(skip_serializing_if = &quot;Option::is_none&quot;)]
    file_name: Option&lt;String&gt;,
}

async fn upload_scan_images(mut payload: Multipart) -&gt; Result&lt;HttpResponse&gt; {
    let mut image_data: Option&lt;Vec&lt;u8&gt;&gt; = None;
    let mut image_count: Option&lt;i32&gt; = None;
    let mut business_id: Option&lt;String&gt; = None;
    let mut description: Option&lt;String&gt; = None;

    // \u89E3\u6790multipart\u6570\u636E
    while let Ok(Some(mut field)) = payload.try_next().await {
        let content_disposition = field.content_disposition();

        if let Some(name) = content_disposition.get_name() {
            match name {
                &quot;image&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    image_data = Some(data);
                }
                &quot;imageCount&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(count_str) = String::from_utf8(data) {
                        image_count = count_str.parse().ok();
                    }
                }
                &quot;id&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(id_str) = String::from_utf8(data) {
                        business_id = Some(id_str);
                    }
                }
                &quot;desc&quot; =&gt; {
                    let mut data = Vec::new();
                    while let Some(chunk) = field.next().await {
                        let chunk = chunk?;
                        data.extend_from_slice(&amp;chunk);
                    }
                    if let Ok(desc_str) = String::from_utf8(data) {
                        description = Some(desc_str);
                    }
                }
                _ =&gt; {}
            }
        }
    }

    // \u9A8C\u8BC1\u5FC5\u8981\u53C2\u6570
    let image_data = match image_data {
        Some(data) if !data.is_empty() =&gt; data,
        _ =&gt; {
            return Ok(HttpResponse::BadRequest().json(UploadResponse {
                network: false,
                msg: &quot;\u4E0A\u4F20\u6587\u4EF6\u4E3A\u7A7A&quot;.to_string(),
                file_id: None,
                file_name: None,
            }));
        }
    };

    let business_id = business_id.unwrap_or_else(|| Uuid::new_v4().to_string());
    let image_count = image_count.unwrap_or(1);

    // \u751F\u6210\u6587\u4EF6\u540D
    let timestamp = chrono::Utc::now().timestamp_millis();
    let file_name = format!(&quot;{}_{}.pdf&quot;, business_id, timestamp);
    let upload_dir = &quot;./uploads/scan&quot;;

    // \u521B\u5EFA\u76EE\u5F55
    if let Err(e) = fs::create_dir_all(upload_dir).await {
        return Ok(HttpResponse::InternalServerError().json(UploadResponse {
            network: false,
            msg: format!(&quot;\u521B\u5EFA\u76EE\u5F55\u5931\u8D25: {}&quot;, e),
            file_id: None,
            file_name: None,
        }));
    }

    // \u4FDD\u5B58\u6587\u4EF6
    let file_path = format!(&quot;{}/{}&quot;, upload_dir, file_name);
    if let Err(e) = fs::write(&amp;file_path, &amp;image_data).await {
        return Ok(HttpResponse::InternalServerError().json(UploadResponse {
            network: false,
            msg: format!(&quot;\u4FDD\u5B58\u6587\u4EF6\u5931\u8D25: {}&quot;, e),
            file_id: None,
            file_name: None,
        }));
    }

    // \u4FDD\u5B58\u5230\u6570\u636E\u5E93
    let document = ScanDocument {
        id: None,
        business_id: business_id.clone(),
        description,
        image_count,
        file_path: file_path.clone(),
        file_name: file_name.clone(),
        file_size: image_data.len() as u64,
        upload_time: chrono::Utc::now(),
    };

    // \u8FD9\u91CC\u5E94\u8BE5\u8C03\u7528\u6570\u636E\u5E93\u670D\u52A1\u4FDD\u5B58\u8BB0\u5F55
    // let saved_document = db_service.save(document).await?;

    // \u8FD4\u56DE\u6210\u529F\u54CD\u5E94
    Ok(HttpResponse::Ok().json(UploadResponse {
        network: true,
        msg: &quot;\u4E0A\u4F20\u6210\u529F&quot;.to_string(),
        file_id: Some(1), // document.id
        file_name: Some(file_name),
    }))
}

#[actix_web::main]
async fn main() -&gt; std::io::Result&lt;()&gt; {
    HttpServer::new(|| {
        App::new()
            .route(&quot;/api/upload&quot;, web::post().to(upload_scan_images))
    })
    .bind(&quot;127.0.0.1:8080&quot;)?
    .run()
    .await
}</pre></div></div><div class="frontend-examples" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u524D\u7AEFJavaScript\u4E0A\u4F20\u793A\u4F8B</h4><p class="text-gray-600 mb-4" data-v-d2fddfc7> \u7B2C\u4E8C\u79CD\u4E0A\u4F20\u65B9\u5F0F\uFF1A\u524D\u7AEF\u901A\u8FC7WebSocket\u83B7\u53D6\u56FE\u50CF\u6570\u636E\u540E\uFF0C\u4F7F\u7528JavaScript\u8FDB\u884C\u4E0A\u4F20 </p><div class="frontend-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7> \u83B7\u53D6\u56FE\u50CF\u6570\u636E\u5E76\u4E0A\u4F20\uFF08Base64\u65B9\u5F0F\uFF09 </h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u83B7\u53D6\u626B\u63CF\u56FE\u50CF\u7684Base64\u6570\u636E
scanonweb.onGetAllImageEvent = function(msg) {
    console.log(&#39;\u83B7\u53D6\u5230\u56FE\u50CF\u6570\u636E:&#39;, msg.images);

    // \u4E0A\u4F20\u6240\u6709\u56FE\u50CF
    uploadImagesToServer(msg.images, &#39;DOC_001&#39;, &#39;\u626B\u63CF\u6587\u6863&#39;);
};

// \u4E0A\u4F20\u56FE\u50CF\u5230\u670D\u52A1\u5668
async function uploadImagesToServer(images, businessId, description) {
    try {
        for (let i = 0; i &lt; images.length; i++) {
            const base64Data = images[i];

            // \u5C06Base64\u8F6C\u6362\u4E3ABlob
            const blob = base64ToBlob(base64Data, &#39;image/jpeg&#39;);

            // \u521B\u5EFAFormData
            const formData = new FormData();
            formData.append(&#39;image&#39;, blob, \`scan_\${businessId}_\${i}.jpg\`);
            formData.append(&#39;imageCount&#39;, images.length.toString());
            formData.append(&#39;id&#39;, businessId);
            formData.append(&#39;desc&#39;, description);
            formData.append(&#39;imageIndex&#39;, i.toString());

            // \u4F7F\u7528axios\u4E0A\u4F20
            const response = await axios.post(&#39;/api/upload-image&#39;, formData, {
                headers: {
                    &#39;Content-Type&#39;: &#39;multipart/form-data&#39;
                },
                onUploadProgress: (progressEvent) =&gt; {
                    const progress = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    console.log(\`\u56FE\u50CF \${i + 1} \u4E0A\u4F20\u8FDB\u5EA6: \${progress}%\`);
                }
            });

            console.log(\`\u56FE\u50CF \${i + 1} \u4E0A\u4F20\u6210\u529F:\`, response.data);
        }

        alert(&#39;\u6240\u6709\u56FE\u50CF\u4E0A\u4F20\u5B8C\u6210\uFF01&#39;);

    } catch (error) {
        console.error(&#39;\u4E0A\u4F20\u5931\u8D25:&#39;, error);
        alert(&#39;\u4E0A\u4F20\u5931\u8D25: &#39; + error.message);
    }
}

// Base64\u8F6CBlob\u5DE5\u5177\u51FD\u6570
function base64ToBlob(base64Data, contentType = &#39;&#39;) {
    const byteCharacters = atob(base64Data);
    const byteArrays = [];

    for (let offset = 0; offset &lt; byteCharacters.length; offset += 512) {
        const slice = byteCharacters.slice(offset, offset + 512);
        const byteNumbers = new Array(slice.length);

        for (let i = 0; i &lt; slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
}

// \u83B7\u53D6\u56FE\u50CF\u6570\u636E
scanonweb.getAllImage();</pre></div></div><div class="frontend-example" data-v-d2fddfc7><h5 class="server-example-title" data-v-d2fddfc7>\u4F7F\u7528Fetch API\u4E0A\u4F20</h5><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u4F7F\u7528fetch API\u4E0A\u4F20\u56FE\u50CF
async function uploadImageWithFetch(base64Data, businessId, description, index) {
    try {
        // \u5C06Base64\u8F6C\u6362\u4E3A\u4E8C\u8FDB\u5236\u6570\u636E
        const binaryData = atob(base64Data);
        const bytes = new Uint8Array(binaryData.length);

        for (let i = 0; i &lt; binaryData.length; i++) {
            bytes[i] = binaryData.charCodeAt(i);
        }

        // \u521B\u5EFAFormData
        const formData = new FormData();
        const blob = new Blob([bytes], { type: &#39;image/jpeg&#39; });
        formData.append(&#39;image&#39;, blob, \`scan_\${businessId}_\${index}.jpg\`);
        formData.append(&#39;imageCount&#39;, &#39;1&#39;);
        formData.append(&#39;id&#39;, businessId);
        formData.append(&#39;desc&#39;, description);

        // \u53D1\u9001\u8BF7\u6C42
        const response = await fetch(&#39;/api/upload-image&#39;, {
            method: &#39;POST&#39;,
            body: formData
        });

        if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
        }

        const result = await response.json();

        if (result.network) {
            console.log(&#39;\u4E0A\u4F20\u6210\u529F:&#39;, result.msg);
            return result;
        } else {
            throw new Error(result.msg);
        }

    } catch (error) {
        console.error(&#39;\u4E0A\u4F20\u5931\u8D25:&#39;, error);
        throw error;
    }
}

// \u6279\u91CF\u4E0A\u4F20\u793A\u4F8B
async function batchUploadImages() {
    // \u5148\u83B7\u53D6\u56FE\u50CF\u6570\u636E
    scanonweb.onGetAllImageEvent = async function(msg) {
        const images = msg.images;
        const businessId = &#39;BATCH_&#39; + Date.now();

        for (let i = 0; i &lt; images.length; i++) {
            try {
                await uploadImageWithFetch(
                    images[i],
                    businessId,
                    \`\u6279\u91CF\u626B\u63CF\u6587\u6863 \${i + 1}\`,
                    i
                );
                console.log(\`\u7B2C \${i + 1} \u5F20\u56FE\u50CF\u4E0A\u4F20\u5B8C\u6210\`);
            } catch (error) {
                console.error(\`\u7B2C \${i + 1} \u5F20\u56FE\u50CF\u4E0A\u4F20\u5931\u8D25:\`, error);
            }
        }
    };

    // \u83B7\u53D6\u6240\u6709\u56FE\u50CF
    scanonweb.getAllImage();
}</pre></div></div></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>saveAllImageToLocal(filename)</h3><p class="api-description" data-v-d2fddfc7> \u5C06\u6240\u6709\u56FE\u50CF\u4FDD\u5B58\u5230\u5BA2\u6237\u7AEF\u672C\u5730\u6587\u4EF6\u3002\u652F\u6301\u591A\u79CD\u683C\u5F0F\uFF1APDF\u3001TIFF\u3001JPG\u7B49\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>filename</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u4FDD\u5B58\u7684\u6587\u4EF6\u8DEF\u5F84\u548C\u540D\u79F0</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u4FDD\u5B58\u4E3APDF\u6587\u4EF6
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.pdf&#39;);

// \u4FDD\u5B58\u4E3ATIFF\u6587\u4EF6
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.tiff&#39;);

// \u4FDD\u5B58\u4E3AJPG\u6587\u4EF6\uFF08\u4EC5\u7B2C\u4E00\u5F20\u56FE\u50CF\uFF09
scanonweb.saveAllImageToLocal(&#39;D:/Documents/scan_result.jpg&#39;);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7> uploadJpgImageByIndex(url, id, desc, index) </h3><p class="api-description" data-v-d2fddfc7> \u4E0A\u4F20\u6307\u5B9A\u7D22\u5F15\u7684\u5355\u5F20\u56FE\u50CF\uFF08JPG\u683C\u5F0F\uFF09\u5230\u670D\u52A1\u5668\u3002 </p><div class="api-params" data-v-d2fddfc7><h4 class="api-params-title" data-v-d2fddfc7>\u53C2\u6570</h4><div class="overflow-x-auto" data-v-d2fddfc7><table class="api-params-table" data-v-d2fddfc7><thead data-v-d2fddfc7><tr data-v-d2fddfc7><th data-v-d2fddfc7>\u53C2\u6570\u540D</th><th data-v-d2fddfc7>\u7C7B\u578B</th><th data-v-d2fddfc7>\u5FC5\u586B</th><th data-v-d2fddfc7>\u8BF4\u660E</th></tr></thead><tbody data-v-d2fddfc7><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>url</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u4E0A\u4F20\u76EE\u6807URL\u5730\u5740</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>id</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u6807\u8BC6ID</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>desc</code></td><td data-v-d2fddfc7>String</td><td data-v-d2fddfc7>\u5426</td><td data-v-d2fddfc7>\u56FE\u50CF\u63CF\u8FF0\u4FE1\u606F</td></tr><tr data-v-d2fddfc7><td data-v-d2fddfc7><code data-v-d2fddfc7>index</code></td><td data-v-d2fddfc7>Number</td><td data-v-d2fddfc7>\u662F</td><td data-v-d2fddfc7>\u56FE\u50CF\u7D22\u5F15\uFF0C\u4ECE0\u5F00\u59CB</td></tr></tbody></table></div></div><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u4E0A\u4F20\u7B2C\u4E00\u5F20\u56FE\u50CF
scanonweb.uploadJpgImageByIndex(
    &#39;https://api.example.com/upload-image&#39;,
    &#39;IMG_001&#39;,
    &#39;\u8EAB\u4EFD\u8BC1\u6B63\u9762&#39;,
    0
);</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>openClientLocalfile()</h3><p class="api-description" data-v-d2fddfc7> \u6253\u5F00\u5BA2\u6237\u7AEF\u6587\u4EF6\u9009\u62E9\u5BF9\u8BDD\u6846\uFF0C\u5141\u8BB8\u7528\u6237\u9009\u62E9\u672C\u5730\u56FE\u50CF\u6587\u4EF6\u52A0\u8F7D\u5230\u63A7\u4EF6\u4E2D\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u6253\u5F00\u6587\u4EF6\u9009\u62E9\u5BF9\u8BDD\u6846
scanonweb.openClientLocalfile();

// \u7528\u6237\u9009\u62E9\u6587\u4EF6\u540E\uFF0C\u4F1A\u81EA\u52A8\u52A0\u8F7D\u5230\u63A7\u4EF6\u4E2D
// \u53EF\u4EE5\u901A\u8FC7getAllImage()\u83B7\u53D6\u52A0\u8F7D\u7684\u56FE\u50CF</pre></div></div></div></div><div class="card-business p-8 mb-8" data-v-d2fddfc7><h2 class="heading-secondary mb-6" data-v-d2fddfc7>\u754C\u9762\u63A7\u5236\u65B9\u6CD5</h2><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>setFocus()</h3><p class="api-description" data-v-d2fddfc7> \u8BBE\u7F6E\u626B\u63CF\u63A7\u4EF6\u754C\u9762\u83B7\u5F97\u7126\u70B9\uFF0C\u5C06\u63A7\u4EF6\u7A97\u53E3\u7F6E\u4E8E\u524D\u53F0\u3002 </p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u5C06\u626B\u63CF\u63A7\u4EF6\u7A97\u53E3\u7F6E\u4E8E\u524D\u53F0
scanonweb.setFocus();</pre></div></div></div><div class="api-method" data-v-d2fddfc7><h3 class="api-method-name" data-v-d2fddfc7>hidden()</h3><p class="api-description" data-v-d2fddfc7>\u9690\u85CF\u626B\u63CF\u63A7\u4EF6\u754C\u9762\u7A97\u53E3\u3002</p><div class="api-example" data-v-d2fddfc7><h4 class="api-example-title" data-v-d2fddfc7>\u793A\u4F8B\u4EE3\u7801</h4><div class="code-block" data-v-d2fddfc7><pre data-v-d2fddfc7>// \u9690\u85CF\u626B\u63CF\u63A7\u4EF6\u754C\u9762
scanonweb.hidden();</pre></div></div></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/docs/scanonweb-api.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const scanonwebApi = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-d2fddfc7"]]);

export { scanonwebApi as default };
//# sourceMappingURL=scanonweb-api-C_sYR7lZ.mjs.map
