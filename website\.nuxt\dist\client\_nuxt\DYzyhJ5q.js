import{_ as d}from"./L-3nxwpR.js";import{f as x,o as r,c,a as t,b as o,w as a,d as s,j as _}from"./Dy7juzJL.js";const m=x("/images/logo.jpg"),h={class:"bg-white border-b border-gray-200 shadow-sm"},b={class:"container mx-auto px-4 py-4"},u={class:"flex justify-between items-center"},g=t("div",{class:"flex items-center"},[t("img",{src:m,alt:"brainysoft Logo",class:"h-10 mr-3"}),t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"brainysoft.cn"),t("p",{class:"text-sm text-gray-500"},"专业Web控件解决方案")])],-1),v={class:"flex items-center space-x-8"},f={class:"flex space-x-6"},p=t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),y=t("span",null,"免费下载",-1),B={__name:"Header",setup(l){return(n,i)=>{const e=d;return r(),c("header",h,[t("div",b,[t("div",u,[g,t("nav",v,[t("ul",f,[t("li",null,[o(e,{to:"/",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("首页")]),_:1})]),t("li",null,[o(e,{to:"/products",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("产品介绍")]),_:1})]),t("li",null,[o(e,{to:"/demo",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("在线演示")]),_:1})]),t("li",null,[o(e,{to:"/documents",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("文档资料")]),_:1})]),t("li",null,[o(e,{to:"/purchase",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("注册购买")]),_:1})]),t("li",null,[o(e,{to:"/contact",class:"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200","active-class":"text-orange-500 border-b-2 border-orange-500","exact-active-class":"font-semibold"},{default:a(()=>[s("联系我们")]),_:1})])]),o(e,{to:"/download",class:"btn-primary flex items-center space-x-2"},{default:a(()=>[p,y]),_:1})])])])])}}},w={class:"bg-gray-800 text-white py-8"},k=_('<div class="container mx-auto"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div><h3 class="text-xl font-bold mb-4">关于我们</h3><p>我们是一家致力于提供图像扫描处理软件解决方案的公司,为简化图像扫描编程应用而努力。</p></div><div><h3 class="text-xl font-bold mb-4">联系方式</h3><p>邮箱: <EMAIL></p><p>电话: (+86) 155-1196-5595</p><p>微信: 20155031</p></div><div><h3 class="text-xl font-bold mb-4">快速链接</h3><ul><li><a href="http://www.regsky.com" target="_blank" class="hover:text-gray-300">天空云科技</a></li><li><a href="#" class="hover:text-gray-300">联系我们</a></li></ul></div><div><h3 class="text-xl font-bold mb-4">关注我们</h3><div class="flex space-x-4"><a href="#" class="hover:text-gray-300">Facebook</a><a href="#" class="hover:text-gray-300">Twitter</a><a href="#" class="hover:text-gray-300">LinkedIn</a></div></div></div><div class="mt-8 text-center"><p>© 2024 brainysoft. 保留所有权利。</p></div></div>',1),V=[k],L={__name:"Footer",setup(l){return(n,i)=>(r(),c("footer",w,V))}};export{B as _,L as a};
