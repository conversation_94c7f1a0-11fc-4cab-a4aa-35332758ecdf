<template>
  <div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-2xl font-bold">演示列表</h2>
    </div>
    <ul>
      <li v-for="demo in demos" :key="demo.id" class="border-b last:border-b-0">
        <button
          @click="$emit('select', demo)"
          class="w-full text-left p-4 flex items-center transition-colors"
          :class="{
            'bg-blue-100': demo.id === selectedDemoId,
            'hover:bg-gray-100': demo.id !== selectedDemoId,
          }"
        >
          <!-- 图标区域 -->
          <div
            class="w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold"
            :class="{
              'bg-gradient-to-r from-blue-500 to-indigo-600':
                demo.icon === 'document-scanner',
              'bg-gradient-to-r from-green-500 to-teal-600':
                demo.icon === 'document',
              'bg-gradient-to-r from-purple-500 to-pink-600':
                demo.icon === 'camera',
              'bg-gradient-to-r from-orange-500 to-red-600':
                demo.icon === 'photo',
            }"
          >
            <!-- 根据图标类型显示不同的SVG图标 -->
            <svg
              v-if="demo.icon === 'document-scanner'"
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <svg
              v-else-if="demo.icon === 'document'"
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
              ></path>
            </svg>
            <svg
              v-else-if="demo.icon === 'camera'"
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
              ></path>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
              ></path>
            </svg>
            <svg
              v-else-if="demo.icon === 'photo'"
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
          </div>

          <!-- 文本内容区域 -->
          <div>
            <p class="font-semibold text-gray-800">{{ demo.name }}</p>
            <p class="text-xs text-gray-500 mt-1">{{ demo.description }}</p>
          </div>
        </button>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

defineProps({
  demos: Array,
  selectedDemoId: Number,
});

defineEmits(["select"]);
</script>
