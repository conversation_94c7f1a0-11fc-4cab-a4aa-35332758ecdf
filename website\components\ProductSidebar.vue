<template>
  <div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-2xl font-bold">产品列表</h2>
    </div>
    <ul>
      <li
        v-for="product in products"
        :key="product.id"
        class="border-b last:border-b-0"
      >
        <button
          @click="$emit('select', product)"
          class="w-full text-left p-4 flex items-center transition-colors"
          :class="{
            'bg-blue-100': product.id === selectedProductId,
            'hover:bg-gray-100': product.id !== selectedProductId,
          }"
        >
          <div
            class="w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold text-xl"
            :class="{
              'bg-gradient-to-r from-blue-500 to-indigo-600': product.id === 1,
              'bg-gradient-to-r from-purple-500 to-pink-600': product.id === 2,
              'bg-gradient-to-r from-green-500 to-teal-600': product.id === 3,
            }"
          >
            {{ product.name.charAt(0) }}
          </div>
          <div>
            <p class="font-semibold text-gray-800">{{ product.name }}</p>
            <p class="text-xs text-gray-500 mt-1">
              {{ getShortDescription(product.description) }}
            </p>
          </div>
        </button>
      </li>
    </ul>
  </div>
</template>

<script setup>
defineProps({
  products: Array,
  selectedProductId: Number,
});

defineEmits(["select"]);

const getShortDescription = (description) => {
  return description.length > 50
    ? description.substring(0, 50) + "..."
    : description;
};
</script>
