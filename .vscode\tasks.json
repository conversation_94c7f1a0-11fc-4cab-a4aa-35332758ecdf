{"version": "2.0.0", "tasks": [{"label": "Nuxt: Dev Server", "type": "npm", "script": "dev", "path": "website/", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/website"}, "problemMatcher": [], "detail": "启动 Nuxt.js 开发服务器"}, {"label": "Nuxt: Build", "type": "npm", "script": "build", "path": "website/", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/website"}, "problemMatcher": [], "detail": "构建 Nuxt.js 应用用于生产环境"}, {"label": "Nuxt: Generate", "type": "npm", "script": "generate", "path": "website/", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/website"}, "problemMatcher": [], "detail": "生成静态站点"}, {"label": "Nuxt: Preview", "type": "npm", "script": "preview", "path": "website/", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/website"}, "problemMatcher": [], "detail": "预览构建后的应用"}, {"label": "Install Dependencies", "type": "npm", "script": "install", "path": "website/", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/website"}, "problemMatcher": [], "detail": "安装项目依赖"}]}