import { s as scanonwebGettingStarted_vue_vue_type_style_index_0_scoped_ec61ad0e_lang } from './scanonweb-getting-started-styles-1.mjs-CR5Mgiae.mjs';

const scanonwebGettingStartedStyles_BQWHRnoY = [scanonwebGettingStarted_vue_vue_type_style_index_0_scoped_ec61ad0e_lang];

export { scanonwebGettingStartedStyles_BQWHRnoY as default };
//# sourceMappingURL=scanonweb-getting-started-styles.BQWHRnoY.mjs.map
