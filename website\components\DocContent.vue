<template>
  <div v-if="document" class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div
      class="h-32 flex items-center justify-center"
      :class="getHeaderClass(document.type)"
    >
      <h1 class="text-3xl font-bold text-white text-center">
        {{ document.title }}
      </h1>
    </div>

    <div class="p-6">
      <!-- 文档基本信息 -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b">
        <div class="flex items-center">
          <div
            class="w-10 h-10 rounded-md mr-3 flex items-center justify-center text-white"
            :class="getIconClass(document.type)"
          >
            <svg
              v-if="document.type === 'pdf'"
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
              ></path>
            </svg>
            <svg
              v-else-if="document.type === 'video'"
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
              ></path>
            </svg>
            <svg
              v-else-if="document.type === 'code'"
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
              ></path>
            </svg>
            <svg
              v-else
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
          </div>
          <div>
            <span class="text-sm text-gray-500"
              >发布日期：{{ document.date }}</span
            >
            <div class="flex mt-1">
              <span
                class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-md mr-2"
              >
                {{ getProductName(document.productId) }}
              </span>
              <span
                class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-md"
              >
                {{ document.version }}
              </span>
            </div>
          </div>
        </div>
        <a
          v-if="document.downloadUrl"
          :href="document.downloadUrl"
          target="_blank"
          class="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
            ></path>
          </svg>
          下载文档
        </a>
      </div>

      <!-- 文档描述 -->
      <div class="mb-6">
        <h3 class="text-xl font-semibold mb-3 text-blue-600">文档概述</h3>
        <p class="text-gray-700">{{ document.description }}</p>
      </div>

      <!-- 文档内容 -->
      <div class="mb-6">
        <h3 class="text-xl font-semibold mb-3 text-blue-600">内容预览</h3>

        <!-- 不同类型的文档内容展示 -->
        <div v-if="document.type === 'pdf'" class="border p-4 rounded-lg">
          <div
            class="aspect-w-16 aspect-h-9 bg-gray-100 flex items-center justify-center"
          >
            <p class="text-center text-gray-500">PDF 预览需要下载后查看</p>
          </div>
        </div>

        <div
          v-else-if="document.type === 'video'"
          class="border p-4 rounded-lg"
        >
          <div class="aspect-w-16 aspect-h-9 bg-gray-100">
            <iframe
              v-if="document.embedUrl"
              class="w-full h-full"
              :src="document.embedUrl"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen
            ></iframe>
            <div v-else class="w-full h-full flex items-center justify-center">
              <p class="text-center text-gray-500">视频预览暂不可用</p>
            </div>
          </div>
        </div>

        <div
          v-else-if="document.type === 'code'"
          class="bg-gray-900 text-white p-4 rounded-lg overflow-auto"
        >
          <pre><code>{{ document.content || '代码示例暂不可用' }}</code></pre>
        </div>

        <div
          v-else-if="document.content"
          class="prose max-w-none"
          v-html="document.content"
        ></div>

        <div v-else class="p-4 bg-yellow-50 text-yellow-800 rounded-lg">
          <p>请下载文档以查看完整内容</p>
        </div>
      </div>

      <!-- 相关文档 -->
      <div v-if="document.relatedDocs?.length > 0" class="mb-6">
        <h3 class="text-xl font-semibold mb-3 text-blue-600">相关文档</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="(doc, index) in document.relatedDocs"
            :key="index"
            class="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
          >
            <h4 class="font-semibold">{{ doc.title }}</h4>
            <p class="text-sm text-gray-600 mb-2">{{ doc.description }}</p>
            <a
              :href="doc.url"
              class="text-blue-600 hover:underline text-sm inline-flex items-center"
            >
              查看文档
              <svg
                class="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                ></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  document: Object,
  products: Array,
});

const getHeaderClass = (type) => {
  switch (type) {
    case "pdf":
      return "bg-gradient-to-r from-red-500 to-pink-600";
    case "video":
      return "bg-gradient-to-r from-blue-500 to-indigo-600";
    case "code":
      return "bg-gradient-to-r from-green-500 to-teal-600";
    default:
      return "bg-gradient-to-r from-gray-700 to-gray-900";
  }
};

const getIconClass = (type) => {
  switch (type) {
    case "pdf":
      return "bg-gradient-to-r from-red-500 to-pink-600";
    case "video":
      return "bg-gradient-to-r from-blue-500 to-indigo-600";
    case "code":
      return "bg-gradient-to-r from-green-500 to-teal-600";
    default:
      return "bg-gradient-to-r from-gray-500 to-gray-600";
  }
};

const getProductName = (productId) => {
  if (!props.products) return "";
  const product = props.products.find((p) => p.id === productId);
  return product ? product.name : "";
};
</script>
