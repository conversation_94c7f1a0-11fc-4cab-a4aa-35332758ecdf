import { mergeProps, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssrRenderAttrs } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { _ as _export_sfc } from './server.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import '../_/renderer.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const _sfc_main = {
  __name: "GaoPaiYiDemo",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "gaopaiyi-demo" }, _attrs))} data-v-9e9ef8b0><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-9e9ef8b0><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-9e9ef8b0> \u9AD8\u62CD\u4EEA\u6F14\u793A </h2><div class="p-8 bg-gray-100 rounded mb-6 text-center" data-v-9e9ef8b0><svg class="w-24 h-24 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg><h3 class="text-xl font-semibold mt-4 mb-2" data-v-9e9ef8b0>GaoPaiYi \u9AD8\u62CD\u4EEA\u529F\u80FD\u6F14\u793A</h3><p class="text-gray-600 mb-4" data-v-9e9ef8b0> \u8BE5\u529F\u80FD\u9700\u8981\u5B89\u88C5\u9AD8\u62CD\u4EEA\u63A7\u4EF6\u5E76\u8FDE\u63A5\u9AD8\u62CD\u4EEA\u8BBE\u5907\u624D\u80FD\u4F7F\u7528\u3002 </p><button class="btn bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition-colors" data-v-9e9ef8b0> \u4E0B\u8F7D\u9AD8\u62CD\u4EEA\u63A7\u4EF6 </button></div><div class="mb-8" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0>\u4EC0\u4E48\u662F\u9AD8\u62CD\u4EEA\uFF1F</h3><p class="text-gray-700 mb-4" data-v-9e9ef8b0> \u9AD8\u62CD\u4EEA\u662F\u4E00\u79CD\u7279\u6B8A\u7684\u56FE\u50CF\u91C7\u96C6\u8BBE\u5907\uFF0C\u5B83\u4E3B\u8981\u7531\u9AD8\u6E05\u6444\u50CF\u5934\u3001LED\u8865\u5149\u706F\u3001\u53EF\u8C03\u652F\u67B6\u7B49\u90E8\u4EF6\u7EC4\u6210\uFF0C\u53EF\u4EE5\u5FEB\u901F\u5C06\u7EB8\u8D28\u6587\u6863\u3001\u8BC1\u4EF6\u7B49\u8F6C\u6362\u4E3A\u7535\u5B50\u56FE\u50CF\u3002\u4E0E\u666E\u901A\u626B\u63CF\u4EEA\u76F8\u6BD4\uFF0C\u9AD8\u62CD\u4EEA\u5177\u6709\u62CD\u6444\u901F\u5EA6\u5FEB\u3001\u64CD\u4F5C\u7B80\u4FBF\u7684\u7279\u70B9\uFF0C\u5E7F\u6CDB\u5E94\u7528\u4E8E\u529E\u516C\u3001\u6559\u5B66\u3001\u94F6\u884C\u3001\u653F\u52A1\u7B49\u573A\u666F\u3002 </p><div class="bg-blue-50 p-4 rounded border border-blue-200 mt-4" data-v-9e9ef8b0><h4 class="font-semibold text-blue-700 mb-2" data-v-9e9ef8b0> \u9AD8\u62CD\u4EEA\u4E0E\u666E\u901A\u626B\u63CF\u4EEA\u7684\u533A\u522B </h4><div class="grid grid-cols-1 md:grid-cols-2 gap-4" data-v-9e9ef8b0><div data-v-9e9ef8b0><h5 class="font-medium mb-2" data-v-9e9ef8b0>\u9AD8\u62CD\u4EEA\u4F18\u52BF\uFF1A</h5><ul class="list-disc list-inside text-sm text-gray-700" data-v-9e9ef8b0><li data-v-9e9ef8b0>\u901F\u5EA6\u5FEB\uFF0C\u65E0\u9700\u9884\u70ED\uFF0C\u5373\u5F00\u5373\u7528</li><li data-v-9e9ef8b0>\u53EF\u4EE5\u91C7\u96C6\u7ACB\u4F53\u7269\u54C1\u56FE\u50CF</li><li data-v-9e9ef8b0>\u64CD\u4F5C\u7B80\u4FBF\uFF0C\u5B89\u88C5\u4F7F\u7528\u65B9\u4FBF</li><li data-v-9e9ef8b0>\u4F53\u79EF\u5C0F\uFF0C\u5360\u7528\u7A7A\u95F4\u5C11</li><li data-v-9e9ef8b0>\u5B9E\u65F6\u9884\u89C8\uFF0C\u6240\u89C1\u5373\u6240\u5F97</li></ul></div><div data-v-9e9ef8b0><h5 class="font-medium mb-2" data-v-9e9ef8b0>\u9002\u7528\u573A\u666F\uFF1A</h5><ul class="list-disc list-inside text-sm text-gray-700" data-v-9e9ef8b0><li data-v-9e9ef8b0>\u529E\u516C\u6587\u6863\u5FEB\u901F\u7535\u5B50\u5316</li><li data-v-9e9ef8b0>\u8EAB\u4EFD\u8BC1\u3001\u8BC1\u4E66\u7B49\u8BC1\u4EF6\u91C7\u96C6</li><li data-v-9e9ef8b0>\u6559\u5B66\u6F14\u793A\u548C\u7269\u4F53\u5C55\u793A</li><li data-v-9e9ef8b0>\u94F6\u884C\u67DC\u53F0\u4E1A\u52A1\u529E\u7406</li><li data-v-9e9ef8b0>\u516C\u5B89\u3001\u653F\u52A1\u7A97\u53E3\u8D44\u6599\u5F55\u5165</li></ul></div></div></div></div><div class="mb-8" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0> GaoPaiYi\u63A7\u4EF6\u529F\u80FD </h3><div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-v-9e9ef8b0><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-blue-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" data-v-9e9ef8b0></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>\u8BBE\u5907\u63A7\u5236</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> \u652F\u6301\u8BBE\u5907\u9009\u62E9\u3001\u5206\u8FA8\u7387\u8C03\u6574\u3001\u5BF9\u7126\u63A7\u5236\u3001\u4EAE\u5EA6\u5BF9\u6BD4\u5EA6\u8C03\u8282\u7B49 </p></div><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-green-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>\u56FE\u50CF\u5904\u7406</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> \u652F\u6301\u81EA\u52A8\u88C1\u526A\u3001\u65CB\u8F6C\u3001\u7FFB\u8F6C\u3001\u9510\u5316\u3001\u53BB\u5E95\u8272\u7B49\u56FE\u50CF\u4F18\u5316\u5904\u7406 </p></div><div class="bg-white shadow rounded p-4 hover:shadow-md transition-shadow" data-v-9e9ef8b0><div class="rounded-full bg-purple-100 w-12 h-12 flex items-center justify-center mb-4" data-v-9e9ef8b0><svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" data-v-9e9ef8b0></path></svg></div><h4 class="font-semibold mb-2" data-v-9e9ef8b0>\u6570\u636E\u4E0A\u4F20</h4><p class="text-sm text-gray-600" data-v-9e9ef8b0> \u652F\u6301\u591A\u79CD\u683C\u5F0F\u4FDD\u5B58\u3001\u4E0A\u4F20\u81F3\u670D\u52A1\u5668\u3001\u96C6\u6210\u5230\u4E1A\u52A1\u7CFB\u7EDF\u7B49\u529F\u80FD </p></div></div></div><div class="border-t pt-6" data-v-9e9ef8b0><h3 class="text-xl font-semibold mb-4 text-blue-600" data-v-9e9ef8b0>\u5F00\u53D1\u8D44\u6E90</h3><div class="flex flex-wrap gap-4" data-v-9e9ef8b0><a href="https://www.brainysoft.cn" target="_blank" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" data-v-9e9ef8b0></path></svg> \u5B98\u65B9\u7F51\u7AD9 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-9e9ef8b0></path></svg> \u5F00\u53D1\u6587\u6863 </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" data-v-9e9ef8b0></path></svg> SDK\u4E0B\u8F7D </a><a href="#" class="flex items-center text-blue-600 hover:underline" data-v-9e9ef8b0><svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-9e9ef8b0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-9e9ef8b0></path></svg> \u4EE3\u7801\u793A\u4F8B </a></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/GaoPaiYiDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const GaoPaiYiDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-9e9ef8b0"]]);

export { GaoPaiYiDemo as default };
//# sourceMappingURL=GaoPaiYiDemo-DoyT-eMt.mjs.map
