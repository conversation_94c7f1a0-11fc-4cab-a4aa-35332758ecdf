{"version": 3, "file": "download-wiBYTEPb.js", "sources": ["../../../../pages/download.vue"], "sourcesContent": ["<template>\n  <div class=\"bg-gray-50 min-h-screen\">\n    <Header />\n    <main class=\"container mx-auto py-12 px-4\">\n      <div class=\"text-center mb-12\">\n        <h1 class=\"heading-primary mb-4\">产品下载中心</h1>\n        <p class=\"text-xl text-gray-600\">选择适合您系统的版本，开始免费试用</p>\n      </div>\n\n      <div class=\"card-business p-8 mb-8\">\n        <div class=\"mb-8\">\n          <h2 class=\"heading-secondary mb-6\">选择产品</h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <button\n              v-for=\"product in products\"\n              :key=\"product.id\"\n              @click=\"selectProduct(product)\"\n              :class=\"[\n                'p-6 rounded-lg border-2 transition-all duration-200 text-left',\n                selectedProduct?.id === product.id\n                  ? 'border-orange-500 bg-orange-50 shadow-md'\n                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',\n              ]\"\n            >\n              <div class=\"flex items-center mb-3\">\n                <div\n                  class=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3\"\n                >\n                  <svg\n                    class=\"w-5 h-5 text-orange-500\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      stroke-width=\"2\"\n                      d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    ></path>\n                  </svg>\n                </div>\n                <h3 class=\"font-bold text-lg text-gray-900\">\n                  {{ product.name }}\n                </h3>\n              </div>\n              <p class=\"text-gray-600 mb-3\">{{ product.shortDesc }}</p>\n              <div class=\"text-sm text-gray-500\">\n                <span class=\"inline-block bg-gray-100 px-2 py-1 rounded\">{{\n                  product.version\n                }}</span>\n              </div>\n            </button>\n          </div>\n        </div>\n\n        <div v-if=\"selectedProduct\" class=\"mb-8\">\n          <h2 class=\"heading-secondary mb-6\">选择操作系统</h2>\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n            <button\n              v-for=\"os in operatingSystems\"\n              :key=\"os.id\"\n              @click=\"selectOS(os)\"\n              :class=\"[\n                'p-6 rounded-lg border-2 transition-all duration-200 text-left',\n                selectedOS?.id === os.id\n                  ? 'border-orange-500 bg-orange-50 shadow-md'\n                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',\n              ]\"\n            >\n              <div class=\"flex items-center\">\n                <div\n                  class=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\"\n                >\n                  <svg\n                    v-if=\"os.id === 'windows'\"\n                    class=\"w-6 h-6 text-blue-600\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      d=\"M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351\"\n                    />\n                  </svg>\n                  <svg\n                    v-else\n                    class=\"w-6 h-6 text-blue-600\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      stroke-width=\"2\"\n                      d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                    ></path>\n                  </svg>\n                </div>\n                <div>\n                  <h3 class=\"font-bold text-lg text-gray-900\">{{ os.name }}</h3>\n                  <p class=\"text-sm text-gray-500\">\n                    {{\n                      os.id === \"windows\"\n                        ? \"支持 Windows 7/8/10/11\"\n                        : \"支持主流Linux发行版\"\n                    }}\n                  </p>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n\n        <div v-if=\"selectedOS && selectedOS.id === 'linux'\" class=\"mb-8\">\n          <h2 class=\"heading-secondary mb-6\">选择CPU架构</h2>\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <button\n              v-for=\"arch in cpuArchitectures\"\n              :key=\"arch.id\"\n              @click=\"selectArch(arch)\"\n              :class=\"[\n                'p-4 rounded-lg border-2 transition-all duration-200 text-left',\n                selectedArch?.id === arch.id\n                  ? 'border-orange-500 bg-orange-50 shadow-md'\n                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',\n              ]\"\n            >\n              <div class=\"text-center\">\n                <div\n                  class=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2\"\n                >\n                  <svg\n                    class=\"w-5 h-5 text-gray-600\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      stroke-width=\"2\"\n                      d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                    ></path>\n                  </svg>\n                </div>\n                <h3 class=\"font-bold text-gray-900 mb-1\">{{ arch.name }}</h3>\n                <p class=\"text-xs text-gray-500\">{{ arch.description }}</p>\n              </div>\n            </button>\n          </div>\n        </div>\n\n        <div v-if=\"downloadReady\" class=\"mt-8\">\n          <div\n            class=\"bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-8 text-center border border-orange-200\"\n          >\n            <h3 class=\"text-2xl font-bold text-gray-900 mb-4\">准备下载</h3>\n            <div class=\"mb-6\">\n              <button\n                @click=\"handleDownloadClick\"\n                class=\"btn-primary text-xl px-10 py-4 inline-flex items-center shadow-lg\"\n              >\n                <svg\n                  class=\"w-6 h-6 mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  ></path>\n                </svg>\n                下载 {{ selectedProduct.name }} {{ getVersionText() }}\n              </button>\n            </div>\n\n            <div\n              class=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-6\"\n            >\n              <div class=\"flex items-center justify-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  ></path>\n                </svg>\n                版本: {{ getVersionText() }}\n              </div>\n              <div class=\"flex items-center justify-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                  ></path>\n                </svg>\n                文件大小: {{ getFileSize() }}\n              </div>\n              <div class=\"flex items-center justify-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                  ></path>\n                </svg>\n                安全无毒\n              </div>\n            </div>\n\n            <div class=\"feature-highlight text-left\">\n              <h4 class=\"font-bold text-orange-700 mb-2\">安装说明</h4>\n              <p class=\"text-orange-600\">{{ getInstallInstructions() }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card-business p-8\">\n        <h2 class=\"heading-secondary mb-6\">历史版本下载</h2>\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full border-collapse\">\n            <thead>\n              <tr class=\"bg-gray-50\">\n                <th\n                  class=\"border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900\"\n                >\n                  产品名称\n                </th>\n                <th\n                  class=\"border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900\"\n                >\n                  版本号\n                </th>\n                <th\n                  class=\"border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900\"\n                >\n                  支持平台\n                </th>\n                <th\n                  class=\"border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900\"\n                >\n                  发布日期\n                </th>\n                <th\n                  class=\"border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900\"\n                >\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr\n                v-for=\"(version, index) in historyVersions\"\n                :key=\"index\"\n                :class=\"index % 2 === 0 ? 'bg-white' : 'bg-gray-50'\"\n              >\n                <td\n                  class=\"border-b border-gray-200 py-4 px-6 font-medium text-gray-900\"\n                >\n                  {{ version.productName }}\n                </td>\n                <td class=\"border-b border-gray-200 py-4 px-6 text-gray-700\">\n                  <span\n                    class=\"inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm\"\n                    >{{ version.version }}</span\n                  >\n                </td>\n                <td class=\"border-b border-gray-200 py-4 px-6 text-gray-700\">\n                  {{ version.platform }}\n                </td>\n                <td class=\"border-b border-gray-200 py-4 px-6 text-gray-700\">\n                  {{ version.date }}\n                </td>\n                <td class=\"border-b border-gray-200 py-4 px-6\">\n                  <a\n                    :href=\"version.downloadUrl\"\n                    class=\"btn-outline text-sm px-4 py-2 inline-flex items-center\"\n                  >\n                    <svg\n                      class=\"w-4 h-4 mr-1\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        stroke-width=\"2\"\n                        d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                      ></path>\n                    </svg>\n                    下载\n                  </a>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </main>\n    <Footer />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from \"vue\";\nimport Header from \"../components/Header.vue\";\nimport Footer from \"../components/Footer.vue\";\n\nconst products = ref([\n  {\n    id: 1,\n    name: \"ScanOnWeb\",\n    shortDesc: \"扫描仪控件\",\n    version: \"3.5.0\",\n    baseUrl: \"/downloads/scanonweb\",\n  },\n  {\n    id: 2,\n    name: \"ImageCapOnWeb\",\n    shortDesc: \"摄像头图像采集控件\",\n    version: \"2.8.0\",\n    baseUrl: \"/downloads/imagecaponweb\",\n  },\n  {\n    id: 3,\n    name: \"GaoPaiYi\",\n    shortDesc: \"高拍仪控件\",\n    version: \"2.0.1\",\n    baseUrl: \"/downloads/gaopaiyi\",\n  },\n]);\n\nconst operatingSystems = ref([\n  { id: \"windows\", name: \"Windows\" },\n  { id: \"linux\", name: \"Linux\" },\n]);\n\nconst cpuArchitectures = ref([\n  { id: \"x64\", name: \"x64\", description: \"通用x86-64架构\" },\n  { id: \"phytium\", name: \"飞腾\", description: \"FT-2000/4等系列\" },\n  { id: \"loongson\", name: \"龙芯\", description: \"龙芯3A4000等系列\" },\n  { id: \"kunpeng\", name: \"鲲鹏\", description: \"华为鲲鹏920等系列\" },\n]);\n\nconst selectedProduct = ref(null);\nconst selectedOS = ref(null);\nconst selectedArch = ref(null);\n\nconst downloadReady = computed(() => {\n  if (!selectedProduct.value) return false;\n  if (!selectedOS.value) return false;\n  if (selectedOS.value.id === \"linux\" && !selectedArch.value) return false;\n  return true;\n});\n\nconst historyVersions = ref([\n  {\n    productName: \"ScanOnWeb\",\n    version: \"3.4.0\",\n    platform: \"Windows\",\n    date: \"2023-12-15\",\n    downloadUrl: \"/downloads/scanonweb/windows/ScanOnWeb_3.4.0_Setup.exe\",\n  },\n  {\n    productName: \"ScanOnWeb\",\n    version: \"3.3.5\",\n    platform: \"Linux (x64)\",\n    date: \"2023-10-20\",\n    downloadUrl: \"/downloads/scanonweb/linux/x64/ScanOnWeb_3.3.5.tar.gz\",\n  },\n  {\n    productName: \"ImageCapOnWeb\",\n    version: \"2.7.0\",\n    platform: \"Windows\",\n    date: \"2023-11-05\",\n    downloadUrl:\n      \"/downloads/imagecaponweb/windows/ImageCapOnWeb_2.7.0_Setup.exe\",\n  },\n  {\n    productName: \"GaoPaiYi\",\n    version: \"1.9.2\",\n    platform: \"Windows\",\n    date: \"2023-09-30\",\n    downloadUrl: \"/downloads/gaopaiyi/windows/GaoPaiYi_1.9.2_Setup.exe\",\n  },\n]);\n\nconst selectProduct = (product) => {\n  selectedProduct.value = product;\n  selectedOS.value = null;\n  selectedArch.value = null;\n};\n\nconst selectOS = (os) => {\n  selectedOS.value = os;\n  if (os.id !== \"linux\") {\n    selectedArch.value = null;\n  }\n};\n\nconst selectArch = (arch) => {\n  selectedArch.value = arch;\n};\n\nconst getDownloadLink = () => {\n  if (!downloadReady.value) return \"#\";\n\n  const { baseUrl } = selectedProduct.value;\n  const osPath = selectedOS.value.id;\n\n  if (selectedOS.value.id === \"linux\") {\n    return `${baseUrl}/${osPath}/${selectedArch.value.id}/${selectedProduct.value.name}_${selectedProduct.value.version}.tar.gz`;\n  } else {\n    return `${baseUrl}/${osPath}/${selectedProduct.value.name}_${selectedProduct.value.version}_Setup.exe`;\n  }\n};\n\n// 处理下载点击事件\nconst handleDownloadClick = async () => {\n  if (!downloadReady.value) return;\n\n  // 追踪下载事件\n  try {\n    const { analytics } = await import(\"~/utils/analytics\");\n    analytics.trackDownload(\n      selectedProduct.value?.name || \"Unknown\",\n      getVersionText()\n    );\n  } catch (error) {\n    console.log(\"Analytics tracking failed:\", error);\n  }\n\n  // 打开下载链接\n  const downloadUrl = getDownloadLink();\n  window.open(downloadUrl, \"_blank\");\n};\n\nconst getVersionText = () => {\n  if (!selectedProduct.value) return \"\";\n\n  let versionText = selectedProduct.value.version;\n\n  if (selectedOS.value) {\n    versionText += ` for ${selectedOS.value.name}`;\n\n    if (selectedOS.value.id === \"linux\" && selectedArch.value) {\n      versionText += ` (${selectedArch.value.name})`;\n    }\n  }\n\n  return versionText;\n};\n\nconst getFileSize = () => {\n  // 这里可以根据不同产品和平台返回实际文件大小\n  return \"15.8 MB\";\n};\n\nconst getInstallInstructions = () => {\n  if (!selectedProduct.value || !selectedOS.value) return \"\";\n\n  if (selectedOS.value.id === \"windows\") {\n    return \"下载后双击安装包运行，按照安装向导完成安装。安装完成后，重启浏览器即可使用。\";\n  } else {\n    return \"下载后解压文件，进入解压目录，运行 ./install.sh 脚本进行安装。安装完成后，重启浏览器即可使用。\";\n  }\n};\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA6UA,UAAM,WAAW,IAAI;AAAA,MACnB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IAAA,CACD;AAED,UAAM,mBAAmB,IAAI;AAAA,MAC3B,EAAE,IAAI,WAAW,MAAM,UAAU;AAAA,MACjC,EAAE,IAAI,SAAS,MAAM,QAAQ;AAAA,IAAA,CAC9B;AAED,UAAM,mBAAmB,IAAI;AAAA,MAC3B,EAAE,IAAI,OAAO,MAAM,OAAO,aAAa,aAAa;AAAA,MACpD,EAAE,IAAI,WAAW,MAAM,MAAM,aAAa,eAAe;AAAA,MACzD,EAAE,IAAI,YAAY,MAAM,MAAM,aAAa,cAAc;AAAA,MACzD,EAAE,IAAI,WAAW,MAAM,MAAM,aAAa,aAAa;AAAA,IAAA,CACxD;AAEK,UAAA,kBAAkB,IAAI,IAAI;AAC1B,UAAA,aAAa,IAAI,IAAI;AACrB,UAAA,eAAe,IAAI,IAAI;AAEvB,UAAA,gBAAgB,SAAS,MAAM;AAC/B,UAAA,CAAC,gBAAgB,MAAc,QAAA;AAC/B,UAAA,CAAC,WAAW,MAAc,QAAA;AAC9B,UAAI,WAAW,MAAM,OAAO,WAAW,CAAC,aAAa,MAAc,QAAA;AAC5D,aAAA;AAAA,IAAA,CACR;AAED,UAAM,kBAAkB,IAAI;AAAA,MAC1B;AAAA,QACE,aAAa;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,aACE;AAAA,MACJ;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IAAA,CACD;AAoDD,UAAM,iBAAiB,MAAM;AACvB,UAAA,CAAC,gBAAgB,MAAc,QAAA;AAE/B,UAAA,cAAc,gBAAgB,MAAM;AAExC,UAAI,WAAW,OAAO;AACL,uBAAA,QAAQ,WAAW,MAAM,IAAI;AAE5C,YAAI,WAAW,MAAM,OAAO,WAAW,aAAa,OAAO;AAC1C,yBAAA,KAAK,aAAa,MAAM,IAAI;AAAA,QAC7C;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAGT,UAAM,cAAc,MAAM;AAEjB,aAAA;AAAA,IAAA;AAGT,UAAM,yBAAyB,MAAM;AACnC,UAAI,CAAC,gBAAgB,SAAS,CAAC,WAAW,MAAc,QAAA;AAEpD,UAAA,WAAW,MAAM,OAAO,WAAW;AAC9B,eAAA;AAAA,MAAA,OACF;AACE,eAAA;AAAA,MACT;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}