import { ref, mergeProps, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from "vue/server-renderer";
import { _ as _export_sfc } from "../server.mjs";
import "ofetch";
import "#internal/nuxt/paths";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "@unhead/shared";
import "vue-router";
import "radix3";
import "defu";
import "ufo";
import "devalue";
const _sfc_main = {
  __name: "SimpleDemo",
  __ssrInlineRender: true,
  setup(__props) {
    const colorMode = ref("RGB");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "simple-demo" }, _attrs))} data-v-b6686b1b><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-b6686b1b><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-b6686b1b> 简易扫描工具 </h2><div class="mb-8" data-v-b6686b1b><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6" data-v-b6686b1b><div class="form-control" data-v-b6686b1b><label class="font-medium mb-1 block" data-v-b6686b1b>扫描设备</label><select id="simple-devices" class="w-full p-2 border rounded" data-v-b6686b1b></select></div><div class="form-control" data-v-b6686b1b><label class="font-medium mb-1 block" data-v-b6686b1b>色彩模式</label><select id="simple-colorMode" class="w-full p-2 border rounded" data-v-b6686b1b><option value="RGB" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "RGB") : ssrLooseEqual(colorMode.value, "RGB")) ? " selected" : ""}>彩色</option><option value="GRAY" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "GRAY") : ssrLooseEqual(colorMode.value, "GRAY")) ? " selected" : ""}>灰色</option><option value="BW" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "BW") : ssrLooseEqual(colorMode.value, "BW")) ? " selected" : ""}>黑白</option></select></div></div><div class="flex flex-wrap gap-2" data-v-b6686b1b><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-b6686b1b> 获取设备列表 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-b6686b1b> 开始扫描 </button><button class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors" data-v-b6686b1b> 清空结果 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-b6686b1b> 保存为PDF </button></div></div><div data-v-b6686b1b><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-b6686b1b>扫描结果</h3><div id="simple-imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-b6686b1b></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-b6686b1b><p class="font-bold" data-v-b6686b1b> 注意：本演示需要下载安装托盘扫描服务才可正常工作。 </p><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline mt-2 inline-block" data-v-b6686b1b> 下载扫描服务 </a></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/SimpleDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const SimpleDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-b6686b1b"]]);
export {
  SimpleDemo as default
};
//# sourceMappingURL=SimpleDemo-C6RzC_wr.js.map
