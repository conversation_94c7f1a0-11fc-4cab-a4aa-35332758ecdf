<template>
  <div class="scanner-demo">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
      <h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2">
        图像扫描管理系统
      </h2>

      <div class="mb-8">
        <h3 class="text-xl font-semibold mb-3 text-blue-600">扫描设置</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="form-control">
            <label class="font-medium mb-1 block">扫描设备</label>
            <select id="devices" class="w-full p-2 border rounded"></select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">分辨率</label>
            <div class="flex items-center">
              <input
                type="text"
                id="dpi_x"
                v-model="dpiX"
                class="w-16 p-2 border rounded"
              />
              <span class="mx-2">X</span>
              <input
                type="text"
                id="dpi_y"
                v-model="dpiY"
                class="w-16 p-2 border rounded"
              />
            </div>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">色彩模式</label>
            <select
              id="colorMode"
              v-model="colorMode"
              class="w-full p-2 border rounded"
            >
              <option value="RGB">彩色</option>
              <option value="GRAY">灰色</option>
              <option value="BW">黑白</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">显示设备对话框</label>
            <select
              id="showDialog"
              v-model="showDialog"
              class="w-full p-2 border rounded"
            >
              <option value="true">显示</option>
              <option value="false">不显示</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">自动进纸模式</label>
            <select
              id="feedEnable"
              v-model="feedEnable"
              class="w-full p-2 border rounded"
            >
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">自动装填纸张</label>
            <select
              id="autoFeed"
              v-model="autoFeed"
              class="w-full p-2 border rounded"
            >
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">双面模式</label>
            <select
              id="dupxMode"
              v-model="dupxMode"
              class="w-full p-2 border rounded"
            >
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">自动纠偏</label>
            <select
              id="autoDeskew"
              v-model="autoDeskew"
              class="w-full p-2 border rounded"
            >
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
          </div>
          <div class="form-control">
            <label class="font-medium mb-1 block">自动边框检测</label>
            <select
              id="autoBorderDetection"
              v-model="autoBorderDetection"
              class="w-full p-2 border rounded"
            >
              <option value="true">是</option>
              <option value="false">否</option>
            </select>
          </div>
        </div>

        <div class="flex flex-wrap gap-2 mt-6">
          <button
            class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            @click="loadDevices"
          >
            获取设备列表
          </button>
          <button
            class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            @click="startScan"
          >
            开始扫描
          </button>
          <button
            class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            @click="clearAll"
          >
            清空扫描结果
          </button>
          <button
            class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            @click="getAllImage"
          >
            获取所有图像
          </button>
          <button
            class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            @click="focusService"
          >
            显示界面
          </button>
          <button
            class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            @click="hideService"
          >
            隐藏界面
          </button>
          <button
            class="btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            @click="saveas"
          >
            本地另存
          </button>
        </div>
      </div>

      <div class="mb-8">
        <h3 class="text-xl font-semibold mb-3 text-blue-600">上传设置</h3>
        <div class="bg-gray-100 p-4 rounded">
          <div class="mb-4">
            <label class="font-medium mb-1 block">上传地址</label>
            <input
              type="text"
              id="uploadUrl"
              v-model="uploadUrl"
              class="w-full p-2 border rounded"
              placeholder="请输入服务器上传地址"
            />
          </div>
          <div class="flex flex-wrap gap-2">
            <button
              class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
              @click="uploadAllImageAsPdfFormat"
            >
              PDF格式上传
            </button>
            <button
              class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
              @click="uploadImageFromDom"
            >
              DOM图像上传
            </button>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-xl font-semibold mb-3 text-blue-600">扫描结果</h3>
        <div
          id="imageList"
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
        ></div>
      </div>

      <div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded">
        <p class="font-bold">
          注意：本演示需要下载安装托盘扫描服务才可正常工作，下载链接在下面。
        </p>
      </div>

      <div class="mt-6 flex justify-center gap-4 border-t pt-4">
        <a
          href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe"
          target="_blank"
          class="text-blue-600 hover:underline"
        >
          扫描服务托盘程序下载
        </a>
        <a
          href="https://www.brainysoft.cn/video/scanh5.mp4"
          target="_blank"
          class="text-blue-600 hover:underline"
        >
          视频教程
        </a>
        <a
          href="https://www.brainysoft.cn"
          target="_blank"
          class="text-blue-600 hover:underline"
        >
          官方网站
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

// 扫描配置参数
const dpiX = ref("300");
const dpiY = ref("300");
const colorMode = ref("RGB");
const showDialog = ref("false");
const feedEnable = ref("false");
const autoFeed = ref("false");
const dupxMode = ref("false");
const autoDeskew = ref("false");
const autoBorderDetection = ref("false");
const uploadUrl = ref("http://localhost:44300/api/ImageUpload/upload");

// 扫描控件实例
let scanonweb = null;

// 加载扫描设备列表
const loadDevices = () => {
  if (scanonweb) {
    scanonweb.loadDevices();
  } else {
    alert("扫描控件未初始化，请先加载扫描控件！");
  }
};

// 开始扫描
const startScan = () => {
  if (!scanonweb) {
    alert("扫描控件未初始化，请先加载扫描控件！");
    return;
  }

  const deviceSelect = document.getElementById("devices");
  if (deviceSelect.selectedIndex === -1) {
    alert("请先刷新或者选中要使用的扫描设备后再开始扫描!");
    return;
  }

  // 设置扫描参数
  scanonweb.scaner_work_config.dpi_x = dpiX.value;
  scanonweb.scaner_work_config.dpi_y = dpiY.value;
  scanonweb.scaner_work_config.deviceIndex = deviceSelect.selectedIndex;
  scanonweb.scaner_work_config.colorMode = colorMode.value;
  scanonweb.scaner_work_config.showDialog = showDialog.value;
  scanonweb.scaner_work_config.autoFeedEnable = feedEnable.value;
  scanonweb.scaner_work_config.autoFeed = autoFeed.value;
  scanonweb.scaner_work_config.dupxMode = dupxMode.value;
  scanonweb.scaner_work_config.autoDeskew = autoDeskew.value;
  scanonweb.scaner_work_config.autoBorderDetection = autoBorderDetection.value;

  // 开始扫描
  scanonweb.startScan();
};

// 清空所有扫描结果
const clearAll = () => {
  if (scanonweb) {
    scanonweb.clearAll();
    document.getElementById("imageList").innerHTML = "";
  }
};

// 获取所有图像
const getAllImage = () => {
  if (scanonweb) {
    scanonweb.getAllImage();
  }
};

// 显示扫描服务界面
const focusService = () => {
  if (scanonweb) {
    scanonweb.setFocus();
  }
};

// 隐藏扫描服务界面
const hideService = () => {
  if (scanonweb) {
    scanonweb.hidden();
  }
};

// PDF格式上传
const uploadAllImageAsPdfFormat = () => {
  if (!scanonweb) return;

  const url =
    uploadUrl.value || "http://localhost:44300/api/ImageUpload/upload";
  const id = new Date().getTime().toString();
  const desc = "PDF扫描文档_" + new Date().toLocaleDateString();

  console.log("开始上传PDF，ID:" + id + "，描述:" + desc);
  scanonweb.uploadAllImageAsPdfToUrl(url, id, desc);
};

// DOM图像上传
const uploadImageFromDom = () => {
  if (!scanonweb) return;

  const imageList = document.getElementById("imageList");
  const url =
    uploadUrl.value || "http://localhost:44300/api/ImageUpload/upload";
  const imgList = imageList.querySelectorAll("img");

  if (imgList.length === 0) {
    alert("没有可上传的图像！");
    return;
  }

  Array.from(imgList).forEach((imageDom, i) => {
    const imageBase64 = imageDom.src.split(",")[1];

    // 构造表单数据
    const formData = new FormData();
    formData.append("image", imageBase64);
    formData.append("imageIndex", i);
    formData.append("name", "test");

    // 使用fetch发送数据
    fetch(url, {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.error(error);
      });
  });

  alert("上传请求已发送！");
};

// 本地另存为文件
const saveas = () => {
  if (!scanonweb) return;

  const fileName = prompt("请输入保存文件路径", "d:/test.pdf");
  if (fileName) {
    scanonweb.saveAllImageToLocal(fileName);
    alert("文件已保存到: " + fileName);
  }
};

// 初始化扫描控件和事件
const initScanonweb = () => {
  // 检查是否已加载scanonweb.js
  if (typeof ScanOnWeb === "undefined") {
    console.error("ScanOnWeb 未定义，请确保已加载 scanonweb.js 文件");
    return;
  }

  scanonweb = new ScanOnWeb();

  // 响应返回扫描设备列表的回调函数
  scanonweb.onGetDevicesListEvent = (msg) => {
    const deviceListDom = document.getElementById("devices");

    // 清空设备列表
    deviceListDom.innerHTML = "";

    // 添加设备信息
    for (let i = 0; i < msg.devices.length; ++i) {
      const opt = document.createElement("option");
      opt.innerHTML = msg.devices[i];
      if (i === msg.currentIndex) {
        opt.selected = true;
      }
      deviceListDom.appendChild(opt);
    }

    // 设置授权信息
    const licenseMode = 4; // 4代表逐台机器绑定授权信息授权方式
    const licenseKey1 = ""; // 授权许可信息
    const licenseKey2 = ""; // 授权许可信息
    const checkLicenseUrl = "http://127.0.0.1:28110/check"; // 这里换成您的授权服务器真实ip地址
    scanonweb.setLicenseKey(
      licenseMode,
      licenseKey1,
      licenseKey2,
      checkLicenseUrl
    );
  };

  // 响应获取某一页图像的回调函数
  scanonweb.onGetImageByIdEvent = (msg) => {
    console.log(
      "获取图像事件回调,图像id:" +
        msg.imageIndex +
        " 图像总数:" +
        msg.imageCount +
        " 注意试用版随机删除图像会造成总数降低或不变"
    );
    const imageListDom = document.getElementById("imageList");

    // 创建图像容器
    const containerDiv = document.createElement("div");
    containerDiv.className =
      "image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";

    // 创建图像元素
    const imageDom = document.createElement("img");
    imageDom.src = "data:image/jpg;base64," + msg.imageBase64;
    imageDom.className = "w-full h-64 object-contain";
    // 为img标签赋予额外的图像imageIndex属性
    imageDom.setAttribute("imageIndex", msg.imageIndex); // 后续修改图像时需要用到

    // 创建图像信息覆盖层
    const overlayDiv = document.createElement("div");
    overlayDiv.className =
      "absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm";
    overlayDiv.textContent = "图像 #" + (msg.imageIndex + 1);

    // 将图像和覆盖层添加到容器
    containerDiv.appendChild(imageDom);
    containerDiv.appendChild(overlayDiv);

    // 将容器添加到图像列表
    imageListDom.appendChild(containerDiv);
  };

  // 响应用户在扫描托盘服务程序中对某一页图像进行了编辑的回调函数
  scanonweb.onImageEditedEvent = (msg) => {
    console.log("图像编辑事件回调,图像id:" + msg.imageIndex);
    // 遍历所有的img标签,如果imageIndex属性值等于msg.imageIndex,则替换图像
    const imageListDom = document.getElementById("imageList");
    const imgList = imageListDom.querySelectorAll("img");

    for (let i = 0; i < imgList.length; i++) {
      const imageDom = imgList[i];
      if (parseInt(imageDom.getAttribute("imageIndex")) === msg.imageIndex) {
        imageDom.src = "data:image/jpg;base64," + msg.imageBase64;
        // 让img强制刷新
        imageDom.style.display = "none";
        imageDom.offsetHeight;
        imageDom.style.display = "block";
        break;
      }
    }
  };

  // 响应用户在托盘程序里面对图像顺序进行了拖拽调整的回调事件
  scanonweb.onImageDrapEvent = (msg) => {
    console.log(
      "图像顺序调整事件回调,调整前:" +
        msg.beforeIndex +
        " 调整后:" +
        msg.afterIndex
    );
    const imageListDom = document.getElementById("imageList");
    // 获取所有子节点的数组
    const children = Array.from(imageListDom.children);
    // 获取需要移动的元素
    const moveElement = children[msg.beforeIndex];
    // 从原位置删除
    children.splice(msg.beforeIndex, 1);
    // 插入到新位置
    children.splice(msg.afterIndex, 0, moveElement);
    // 重新排列DOM
    children.forEach((child) => imageListDom.appendChild(child));
  };

  // 响应扫描完成事件
  scanonweb.onScanFinishedEvent = (msg) => {
    console.log(
      "扫描完成事件回调,扫描前:" +
        msg.imageBeforeCount +
        " 扫描后:" +
        msg.imageAfterCount
    );
  };

  // 响应获取所有图像的回调函数
  scanonweb.onGetAllImageEvent = (msg) => {
    console.log("图像总数:" + msg.imageCount);
    console.log("当前选中编辑的图像id:" + msg.currentSelected);
    const imageListDom = document.getElementById("imageList");
    imageListDom.innerHTML = "";

    if (msg.images.length === 0) {
      console.log("没有图像可以显示");
      return;
    }

    for (let i = 0; i < msg.images.length; i++) {
      // 创建图像容器
      const containerDiv = document.createElement("div");
      containerDiv.className =
        "image-container relative border-2 border-gray-200 rounded overflow-hidden transition-transform hover:scale-105 hover:shadow-lg";

      // 创建图像元素
      const imageDom = document.createElement("img");
      imageDom.src = "data:image/jpg;base64," + msg.images[i];
      imageDom.className = "w-full h-64 object-contain";
      imageDom.setAttribute("imageIndex", i);

      // 创建图像信息覆盖层
      const overlayDiv = document.createElement("div");
      overlayDiv.className =
        "absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2 text-center text-sm";
      overlayDiv.textContent = "图像 #" + (i + 1);

      // 将图像和覆盖层添加到容器
      containerDiv.appendChild(imageDom);
      containerDiv.appendChild(overlayDiv);

      // 将容器添加到图像列表
      imageListDom.appendChild(containerDiv);
    }
  };

  // PDF上传结果回调
  scanonweb.onUploadAllImageAsPdfToUrlEvent = (msg) => {
    console.log(msg);
    // 解析上传结果
    try {
      const uploadResult = JSON.parse(msg.uploadResult);
      if (uploadResult && uploadResult.network === true) {
        alert("PDF上传成功！");
      } else {
        alert("PDF上传失败！错误信息: " + (uploadResult.msg || "未知错误"));
      }
    } catch (e) {
      console.error("解析上传结果失败:", e);
      alert("PDF上传处理失败！请检查网络连接和服务器状态。");
    }
  };

  // 绑定托盘上传按钮事件
  scanonweb.onUploadEvent = (msg) => {
    console.log("用户点击了开始上传按钮,当前图像总数:" + msg.imageCount);
    uploadAllImageAsPdfFormat();
  };
};

// 加载扫描控件脚本
const loadScanonwebScript = () => {
  return new Promise((resolve, reject) => {
    // 检查是否已加载
    if (document.getElementById("scanonweb-script")) {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.id = "scanonweb-script";
    script.src = "/scanonweb.js";
    script.type = "text/javascript";
    script.async = true;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

onMounted(async () => {
  try {
    await loadScanonwebScript();
    initScanonweb();
  } catch (err) {
    console.error("加载扫描控件失败:", err);
    alert("加载扫描控件失败，请确保服务器上存在scanonweb.js文件。");
  }
});

onUnmounted(() => {
  // 清理资源
  scanonweb = null;
});
</script>

<style scoped>
.scanner-demo {
  font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
}

.form-control {
  margin-bottom: 1rem;
}

.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

.image-container {
  display: block;
  margin-bottom: 1rem;
}

@media (max-width: 640px) {
  .image-container img {
    height: auto;
  }
}
</style>
