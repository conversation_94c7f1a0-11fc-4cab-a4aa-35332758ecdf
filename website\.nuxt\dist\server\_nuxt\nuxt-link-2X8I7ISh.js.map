{"version": 3, "file": "nuxt-link-2X8I7ISh.js", "sources": ["../../../../node_modules/nuxt/dist/app/components/nuxt-link.js"], "sourcesContent": ["import { computed, defineComponent, h, inject, onBeforeUnmount, onMounted, provide, ref, resolveComponent } from \"vue\";\nimport { hasProtocol, joinURL, parseQuery, withTrailingSlash, withoutTrailingSlash } from \"ufo\";\nimport { preloadRouteComponents } from \"../composables/preload.js\";\nimport { onNuxtReady } from \"../composables/ready.js\";\nimport { navigateTo, resolveRouteObject, useRouter } from \"../composables/router.js\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { cancelIdleCallback, requestIdleCallback } from \"../compat/idle-callback.js\";\nimport { nuxtLinkDefaults } from \"#build/nuxt.config.mjs\";\nconst firstNonUndefined = (...args) => args.find((arg) => arg !== void 0);\nconst NuxtLinkDevKeySymbol = Symbol(\"nuxt-link-dev-key\");\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtLink(options) {\n  const componentName = options.componentName || \"NuxtLink\";\n  function checkPropConflicts(props, main, sub) {\n    if (import.meta.dev && props[main] !== void 0 && props[sub] !== void 0) {\n      console.warn(`[${componentName}] \\`${main}\\` and \\`${sub}\\` cannot be used together. \\`${sub}\\` will be ignored.`);\n    }\n  }\n  function resolveTrailingSlashBehavior(to, resolve) {\n    if (!to || options.trailingSlash !== \"append\" && options.trailingSlash !== \"remove\") {\n      return to;\n    }\n    if (typeof to === \"string\") {\n      return applyTrailingSlashBehavior(to, options.trailingSlash);\n    }\n    const path = \"path\" in to && to.path !== void 0 ? to.path : resolve(to).path;\n    const resolvedPath = {\n      ...to,\n      name: void 0,\n      // named routes would otherwise always override trailing slash behavior\n      path: applyTrailingSlashBehavior(path, options.trailingSlash)\n    };\n    return resolvedPath;\n  }\n  function useNuxtLink(props) {\n    const router = useRouter();\n    const config = useRuntimeConfig();\n    const hasTarget = computed(() => !!props.target && props.target !== \"_self\");\n    const isAbsoluteUrl = computed(() => {\n      const path = props.to || props.href || \"\";\n      return typeof path === \"string\" && hasProtocol(path, { acceptRelative: true });\n    });\n    const builtinRouterLink = resolveComponent(\"RouterLink\");\n    const useBuiltinLink = builtinRouterLink && typeof builtinRouterLink !== \"string\" ? builtinRouterLink.useLink : void 0;\n    const isExternal = computed(() => {\n      if (props.external) {\n        return true;\n      }\n      const path = props.to || props.href || \"\";\n      if (typeof path === \"object\") {\n        return false;\n      }\n      return path === \"\" || isAbsoluteUrl.value;\n    });\n    const to = computed(() => {\n      checkPropConflicts(props, \"to\", \"href\");\n      const path = props.to || props.href || \"\";\n      if (isExternal.value) {\n        return path;\n      }\n      return resolveTrailingSlashBehavior(path, router.resolve);\n    });\n    const link = isExternal.value ? void 0 : useBuiltinLink?.({ ...props, to });\n    const href = computed(() => {\n      if (!to.value || isAbsoluteUrl.value) {\n        return to.value;\n      }\n      if (isExternal.value) {\n        const path = typeof to.value === \"object\" && \"path\" in to.value ? resolveRouteObject(to.value) : to.value;\n        const href2 = typeof path === \"object\" ? router.resolve(path).href : path;\n        return resolveTrailingSlashBehavior(\n          href2,\n          router.resolve\n          /* will not be called */\n        );\n      }\n      if (typeof to.value === \"object\") {\n        return router.resolve(to.value)?.href ?? null;\n      }\n      return resolveTrailingSlashBehavior(\n        joinURL(config.app.baseURL, to.value),\n        router.resolve\n        /* will not be called */\n      );\n    });\n    return {\n      to,\n      hasTarget,\n      isAbsoluteUrl,\n      isExternal,\n      //\n      href,\n      isActive: link?.isActive ?? computed(() => to.value === router.currentRoute.value.path),\n      isExactActive: link?.isExactActive ?? computed(() => to.value === router.currentRoute.value.path),\n      route: link?.route ?? computed(() => router.resolve(to.value)),\n      async navigate() {\n        await navigateTo(href.value, { replace: props.replace, external: isExternal.value || hasTarget.value });\n      }\n    };\n  }\n  return defineComponent({\n    name: componentName,\n    props: {\n      // Routing\n      to: {\n        type: [String, Object],\n        default: void 0,\n        required: false\n      },\n      href: {\n        type: [String, Object],\n        default: void 0,\n        required: false\n      },\n      // Attributes\n      target: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      rel: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      noRel: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Prefetching\n      prefetch: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      noPrefetch: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Styling\n      activeClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      exactActiveClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      prefetchedClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      // Vue Router's `<RouterLink>` additional props\n      replace: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      ariaCurrentValue: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      // Edge cases handling\n      external: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Slot API\n      custom: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      }\n    },\n    useLink: useNuxtLink,\n    setup(props, { slots }) {\n      const router = useRouter();\n      const { to, href, navigate, isExternal, hasTarget, isAbsoluteUrl } = useNuxtLink(props);\n      const prefetched = ref(false);\n      const el = import.meta.server ? void 0 : ref(null);\n      const elRef = import.meta.server ? void 0 : (ref2) => {\n        el.value = props.custom ? ref2?.$el?.nextElementSibling : ref2?.$el;\n      };\n      if (import.meta.client) {\n        checkPropConflicts(props, \"prefetch\", \"noPrefetch\");\n        const shouldPrefetch = props.prefetch !== false && props.noPrefetch !== true && props.target !== \"_blank\" && !isSlowConnection();\n        if (shouldPrefetch) {\n          const nuxtApp = useNuxtApp();\n          let idleId;\n          let unobserve = null;\n          onMounted(() => {\n            const observer = useObserver();\n            onNuxtReady(() => {\n              idleId = requestIdleCallback(() => {\n                if (el?.value?.tagName) {\n                  unobserve = observer.observe(el.value, async () => {\n                    unobserve?.();\n                    unobserve = null;\n                    const path = typeof to.value === \"string\" ? to.value : isExternal.value ? resolveRouteObject(to.value) : router.resolve(to.value).fullPath;\n                    await Promise.all([\n                      nuxtApp.hooks.callHook(\"link:prefetch\", path).catch(() => {\n                      }),\n                      !isExternal.value && !hasTarget.value && preloadRouteComponents(to.value, router).catch(() => {\n                      })\n                    ]);\n                    prefetched.value = true;\n                  });\n                }\n              });\n            });\n          });\n          onBeforeUnmount(() => {\n            if (idleId) {\n              cancelIdleCallback(idleId);\n            }\n            unobserve?.();\n            unobserve = null;\n          });\n        }\n      }\n      if (import.meta.dev && import.meta.server && !props.custom) {\n        const isNuxtLinkChild = inject(NuxtLinkDevKeySymbol, false);\n        if (isNuxtLinkChild) {\n          console.log(\"[nuxt] [NuxtLink] You can't nest one <a> inside another <a>. This will cause a hydration error on client-side. You can pass the `custom` prop to take full control of the markup.\");\n        } else {\n          provide(NuxtLinkDevKeySymbol, true);\n        }\n      }\n      return () => {\n        if (!isExternal.value && !hasTarget.value) {\n          const routerLinkProps = {\n            ref: elRef,\n            to: to.value,\n            activeClass: props.activeClass || options.activeClass,\n            exactActiveClass: props.exactActiveClass || options.exactActiveClass,\n            replace: props.replace,\n            ariaCurrentValue: props.ariaCurrentValue,\n            custom: props.custom\n          };\n          if (!props.custom) {\n            if (prefetched.value) {\n              routerLinkProps.class = props.prefetchedClass || options.prefetchedClass;\n            }\n            routerLinkProps.rel = props.rel || void 0;\n          }\n          return h(\n            resolveComponent(\"RouterLink\"),\n            routerLinkProps,\n            slots.default\n          );\n        }\n        const target = props.target || null;\n        checkPropConflicts(props, \"noRel\", \"rel\");\n        const rel = firstNonUndefined(\n          // converts `\"\"` to `null` to prevent the attribute from being added as empty (`rel=\"\"`)\n          props.noRel ? \"\" : props.rel,\n          options.externalRelAttribute,\n          /*\n          * A fallback rel of `noopener noreferrer` is applied for external links or links that open in a new tab.\n          * This solves a reverse tabnapping security flaw in browsers pre-2021 as well as improving privacy.\n          */\n          isAbsoluteUrl.value || hasTarget.value ? \"noopener noreferrer\" : \"\"\n        ) || null;\n        if (props.custom) {\n          if (!slots.default) {\n            return null;\n          }\n          return slots.default({\n            href: href.value,\n            navigate,\n            get route() {\n              if (!href.value) {\n                return void 0;\n              }\n              const url = new URL(href.value, import.meta.client ? window.location.href : \"http://localhost\");\n              return {\n                path: url.pathname,\n                fullPath: url.pathname,\n                get query() {\n                  return parseQuery(url.search);\n                },\n                hash: url.hash,\n                params: {},\n                name: void 0,\n                matched: [],\n                redirectedFrom: void 0,\n                meta: {},\n                href: href.value\n              };\n            },\n            rel,\n            target,\n            isExternal: isExternal.value || hasTarget.value,\n            isActive: false,\n            isExactActive: false\n          });\n        }\n        return h(\"a\", { ref: el, href: href.value || null, rel, target }, slots.default?.());\n      };\n    }\n  });\n}\nexport default /* @__PURE__ */ defineNuxtLink(nuxtLinkDefaults);\nfunction applyTrailingSlashBehavior(to, trailingSlash) {\n  const normalizeFn = trailingSlash === \"append\" ? withTrailingSlash : withoutTrailingSlash;\n  const hasProtocolDifferentFromHttp = hasProtocol(to) && !to.startsWith(\"http\");\n  if (hasProtocolDifferentFromHttp) {\n    return to;\n  }\n  return normalizeFn(to, true);\n}\nfunction useObserver() {\n  if (import.meta.server) {\n    return;\n  }\n  const nuxtApp = useNuxtApp();\n  if (nuxtApp._observer) {\n    return nuxtApp._observer;\n  }\n  let observer = null;\n  const callbacks = /* @__PURE__ */ new Map();\n  const observe = (element, callback) => {\n    if (!observer) {\n      observer = new IntersectionObserver((entries) => {\n        for (const entry of entries) {\n          const callback2 = callbacks.get(entry.target);\n          const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n          if (isVisible && callback2) {\n            callback2();\n          }\n        }\n      });\n    }\n    callbacks.set(element, callback);\n    observer.observe(element);\n    return () => {\n      callbacks.delete(element);\n      observer.unobserve(element);\n      if (callbacks.size === 0) {\n        observer.disconnect();\n        observer = null;\n      }\n    };\n  };\n  const _observer = nuxtApp._observer = {\n    observe\n  };\n  return _observer;\n}\nfunction isSlowConnection() {\n  if (import.meta.server) {\n    return;\n  }\n  const cn = navigator.connection;\n  if (cn && (cn.saveData || /2g/.test(cn.effectiveType))) {\n    return true;\n  }\n  return false;\n}\n"], "names": [], "mappings": ";;;AAQA,MAAM,oBAAoB,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM;AAAA;AAGjE,SAAS,eAAe,SAAS;AAChC,QAAA,gBAAgB,QAAQ,iBAAiB;AAMtC,WAAA,6BAA6B,IAAI,SAAS;AACjD,QAAI,CAAC,MAAM,QAAQ,kBAAkB,YAAY,QAAQ,kBAAkB,UAAU;AAC5E,aAAA;AAAA,IACT;AACI,QAAA,OAAO,OAAO,UAAU;AACnB,aAAA,2BAA2B,IAAI,QAAQ,aAAa;AAAA,IAC7D;AACM,UAAA,OAAO,UAAU,MAAM,GAAG,SAAS,SAAS,GAAG,OAAO,QAAQ,EAAE,EAAE;AACxE,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,MAAM;AAAA;AAAA,MAEN,MAAM,2BAA2B,MAAM,QAAQ,aAAa;AAAA,IAAA;AAEvD,WAAA;AAAA,EACT;AACA,WAAS,YAAY,OAAO;AAC1B,UAAM,SAAS;AACf,UAAM,SAAS;AACT,UAAA,YAAY,SAAS,MAAM,CAAC,CAAC,MAAM,UAAU,MAAM,WAAW,OAAO;AACrE,UAAA,gBAAgB,SAAS,MAAM;AACnC,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AAChC,aAAA,OAAO,SAAS,YAAY,YAAY,MAAM,EAAE,gBAAgB,MAAM;AAAA,IAAA,CAC9E;AACK,UAAA,oBAAoB,iBAAiB,YAAY;AACvD,UAAM,iBAAiB,qBAAqB,OAAO,sBAAsB,WAAW,kBAAkB,UAAU;AAC1G,UAAA,aAAa,SAAS,MAAM;AAChC,UAAI,MAAM,UAAU;AACX,eAAA;AAAA,MACT;AACA,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AACnC,UAAA,OAAO,SAAS,UAAU;AACrB,eAAA;AAAA,MACT;AACO,aAAA,SAAS,MAAM,cAAc;AAAA,IAAA,CACrC;AACK,UAAA,KAAK,SAAS,MAAM;AAExB,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AACvC,UAAI,WAAW,OAAO;AACb,eAAA;AAAA,MACT;AACO,aAAA,6BAA6B,MAAM,OAAO,OAAO;AAAA,IAAA,CACzD;AACK,UAAA,OAAO,WAAW,QAAQ,SAAS,iDAAiB,EAAE,GAAG,OAAO,GAAA;AAChE,UAAA,OAAO,SAAS,MAAM;;AAC1B,UAAI,CAAC,GAAG,SAAS,cAAc,OAAO;AACpC,eAAO,GAAG;AAAA,MACZ;AACA,UAAI,WAAW,OAAO;AACpB,cAAM,OAAO,OAAO,GAAG,UAAU,YAAY,UAAU,GAAG,QAAQ,mBAAmB,GAAG,KAAK,IAAI,GAAG;AAC9F,cAAA,QAAQ,OAAO,SAAS,WAAW,OAAO,QAAQ,IAAI,EAAE,OAAO;AAC9D,eAAA;AAAA,UACL;AAAA,UACA,OAAO;AAAA;AAAA,QAAA;AAAA,MAGX;AACI,UAAA,OAAO,GAAG,UAAU,UAAU;AAChC,iBAAO,YAAO,QAAQ,GAAG,KAAK,MAAvB,mBAA0B,SAAQ;AAAA,MAC3C;AACO,aAAA;AAAA,QACL,QAAQ,OAAO,IAAI,SAAS,GAAG,KAAK;AAAA,QACpC,OAAO;AAAA;AAAA,MAAA;AAAA,IAET,CACD;AACM,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,WAAU,6BAAM,aAAY,SAAS,MAAM,GAAG,UAAU,OAAO,aAAa,MAAM,IAAI;AAAA,MACtF,gBAAe,6BAAM,kBAAiB,SAAS,MAAM,GAAG,UAAU,OAAO,aAAa,MAAM,IAAI;AAAA,MAChG,QAAO,6BAAM,UAAS,SAAS,MAAM,OAAO,QAAQ,GAAG,KAAK,CAAC;AAAA,MAC7D,MAAM,WAAW;AACf,cAAM,WAAW,KAAK,OAAO,EAAE,SAAS,MAAM,SAAS,UAAU,WAAW,SAAS,UAAU,MAAO,CAAA;AAAA,MACxG;AAAA,IAAA;AAAA,EAEJ;AACA,SAAO,gBAAgB;AAAA,IACrB,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,MAEL,IAAI;AAAA,QACF,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,MAAM;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,KAAK;AAAA,QACH,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,UAAU;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,aAAa;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,iBAAiB;AAAA,QACf,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,SAAS;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,UAAU;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA;AAAA,MAEA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,MAAM,OAAO,EAAE,SAAS;AACP,gBAAU;AACnB,YAAA,EAAE,IAAI,MAAM,UAAU,YAAY,WAAW,cAAkB,IAAA,YAAY,KAAK;AAChF,YAAA,aAAa,IAAI,KAAK;AAC5B,YAAM,KAA0B;AAChC,YAAM,QAA6B;AAgDnC,aAAO,MAAM;;AACX,YAAI,CAAC,WAAW,SAAS,CAAC,UAAU,OAAO;AACzC,gBAAM,kBAAkB;AAAA,YACtB,KAAK;AAAA,YACL,IAAI,GAAG;AAAA,YACP,aAAa,MAAM,eAAe,QAAQ;AAAA,YAC1C,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,YACpD,SAAS,MAAM;AAAA,YACf,kBAAkB,MAAM;AAAA,YACxB,QAAQ,MAAM;AAAA,UAAA;AAEZ,cAAA,CAAC,MAAM,QAAQ;AACjB,gBAAI,WAAW,OAAO;AACJ,8BAAA,QAAQ,MAAM,mBAAmB,QAAQ;AAAA,YAC3D;AACgB,4BAAA,MAAM,MAAM,OAAO;AAAA,UACrC;AACO,iBAAA;AAAA,YACL,iBAAiB,YAAY;AAAA,YAC7B;AAAA,YACA,MAAM;AAAA,UAAA;AAAA,QAEV;AACM,cAAA,SAAS,MAAM,UAAU;AAE/B,cAAM,MAAM;AAAA;AAAA,UAEV,MAAM,QAAQ,KAAK,MAAM;AAAA,UACzB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,UAKR,cAAc,SAAS,UAAU,QAAQ,wBAAwB;AAAA,QAC9D,KAAA;AACL,YAAI,MAAM,QAAQ;AACZ,cAAA,CAAC,MAAM,SAAS;AACX,mBAAA;AAAA,UACT;AACA,iBAAO,MAAM,QAAQ;AAAA,YACnB,MAAM,KAAK;AAAA,YACX;AAAA,YACA,IAAI,QAAQ;AACN,kBAAA,CAAC,KAAK,OAAO;AACR,uBAAA;AAAA,cACT;AACM,oBAAA,MAAM,IAAI,IAAI,KAAK,OAAmD,kBAAkB;AACvF,qBAAA;AAAA,gBACL,MAAM,IAAI;AAAA,gBACV,UAAU,IAAI;AAAA,gBACd,IAAI,QAAQ;AACH,yBAAA,WAAW,IAAI,MAAM;AAAA,gBAC9B;AAAA,gBACA,MAAM,IAAI;AAAA,gBACV,QAAQ,CAAC;AAAA,gBACT,MAAM;AAAA,gBACN,SAAS,CAAC;AAAA,gBACV,gBAAgB;AAAA,gBAChB,MAAM,CAAC;AAAA,gBACP,MAAM,KAAK;AAAA,cAAA;AAAA,YAEf;AAAA,YACA;AAAA,YACA;AAAA,YACA,YAAY,WAAW,SAAS,UAAU;AAAA,YAC1C,UAAU;AAAA,YACV,eAAe;AAAA,UAAA,CAChB;AAAA,QACH;AACA,eAAO,EAAE,KAAK,EAAE,KAAK,IAAI,MAAM,KAAK,SAAS,MAAM,KAAK,OAAA,IAAU,WAAM,YAAN,8BAAiB;AAAA,MAAA;AAAA,IAEvF;AAAA,EAAA,CACD;AACH;AACA,MAAA,oDAA8C,gBAAgB;AAC9D,SAAS,2BAA2B,IAAI,eAAe;AAC/C,QAAA,cAAc,kBAAkB,WAAW,oBAAoB;AACrE,QAAM,+BAA+B,YAAY,EAAE,KAAK,CAAC,GAAG,WAAW,MAAM;AAC7E,MAAI,8BAA8B;AACzB,WAAA;AAAA,EACT;AACO,SAAA,YAAY,IAAI,IAAI;AAC7B;", "x_google_ignoreList": [0]}