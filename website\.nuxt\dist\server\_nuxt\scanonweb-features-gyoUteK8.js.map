{"version": 3, "file": "scanonweb-features-gyoUteK8.js", "sources": ["../../../../pages/docs/scanonweb-features.vue"], "sourcesContent": ["<template>\n  <div class=\"bg-gray-50 min-h-screen\">\n    <Header />\n    <main>\n      <!-- 面包屑导航 -->\n      <div class=\"bg-white border-b border-gray-200\">\n        <div class=\"container mx-auto px-4 py-4\">\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <NuxtLink to=\"/documents\" class=\"hover:text-orange-500\"\n              >文档资料</NuxtLink\n            >\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <span class=\"text-gray-900 font-medium\">ScanOnWeb 技术特性</span>\n          </nav>\n        </div>\n      </div>\n\n      <!-- 页面标题区域 -->\n      <div class=\"bg-white py-8\">\n        <div class=\"container mx-auto px-4\">\n          <div class=\"max-w-6xl\">\n            <h1 class=\"heading-primary mb-4\">ScanOnWeb 技术特性表格</h1>\n            <p class=\"text-lg text-gray-600 mb-6\">\n              全面了解ScanOnWeb扫描控件的技术规格、平台支持和功能特性，包含独有的国产化平台支持\n            </p>\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>国产化平台支持</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>多架构CPU支持</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>全面技术规格</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 平台兼容性概览 -->\n      <div class=\"container mx-auto py-8 px-4\">\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6 text-center\">平台兼容性概览</h2>\n\n          <div class=\"overflow-x-auto\">\n            <table class=\"w-full border-collapse\">\n              <thead>\n                <tr class=\"bg-orange-500 text-white\">\n                  <th\n                    class=\"border border-orange-600 px-4 py-3 text-left font-semibold\"\n                  >\n                    支持的客户端操作系统\n                  </th>\n                  <th\n                    class=\"border border-orange-600 px-4 py-3 text-left font-semibold\"\n                  >\n                    扫描技术\n                  </th>\n                  <th\n                    class=\"border border-orange-600 px-4 py-3 text-left font-semibold\"\n                  >\n                    支持的浏览器\n                  </th>\n                  <th\n                    class=\"border border-orange-600 px-4 py-3 text-left font-semibold\"\n                  >\n                    支持的框架\n                  </th>\n                  <th\n                    class=\"border border-orange-600 px-4 py-3 text-left font-semibold\"\n                  >\n                    服务器端支持\n                  </th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr class=\"bg-white hover:bg-gray-50\">\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-2\">\n                      <div class=\"font-medium text-gray-900\">\n                        Windows 7/8/10/11\n                      </div>\n                      <div class=\"text-sm text-gray-600\">32位和64位</div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"feature-tag\">TWAIN 1.9+</div>\n                      <div class=\"feature-tag\">WIA 2.0</div>\n                      <div class=\"feature-tag\">eSCL</div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\" rowspan=\"3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"browser-tag\">Chrome 68+</div>\n                      <div class=\"browser-tag\">Firefox 68+</div>\n                      <div class=\"browser-tag\">Edge 79+</div>\n                      <div class=\"browser-tag\">IE 11</div>\n                      <div class=\"browser-tag\">Safari 7+</div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\" rowspan=\"3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"framework-tag\">Vue.js</div>\n                      <div class=\"framework-tag\">React</div>\n                      <div class=\"framework-tag\">Angular</div>\n                      <div class=\"framework-tag\">原生JavaScript</div>\n                      <div class=\"framework-tag\">jQuery</div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\" rowspan=\"3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"server-tag\">Java Spring Boot</div>\n                      <div class=\"server-tag\">ASP.NET Core</div>\n                      <div class=\"server-tag\">PHP</div>\n                      <div class=\"server-tag\">Node.js</div>\n                      <div class=\"server-tag\">Python</div>\n                      <div class=\"server-tag\">Go</div>\n                      <div class=\"server-tag\">Rust</div>\n                    </div>\n                  </td>\n                </tr>\n                <tr class=\"bg-blue-50 hover:bg-blue-100\">\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-2\">\n                      <div class=\"font-medium text-blue-900\">macOS 10.15+</div>\n                      <div class=\"text-sm text-blue-700\">\n                        x86-64, ARM64 (M1/M2)\n                      </div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"feature-tag\">TWAIN 1.9</div>\n                      <div class=\"feature-tag\">ICA</div>\n                      <div class=\"feature-tag\">eSCL</div>\n                    </div>\n                  </td>\n                </tr>\n                <tr class=\"bg-green-50 hover:bg-green-100\">\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-2\">\n                      <div class=\"font-medium text-green-900\">Linux</div>\n                      <div class=\"text-sm text-green-700\">\n                        <div>• Ubuntu 12.04+, Debian 8+</div>\n                        <div>• Fedora 24+, CentOS 7+</div>\n                        <div class=\"font-semibold text-orange-600\">\n                          • 统信UOS, 麒麟操作系统\n                        </div>\n                      </div>\n                      <div class=\"text-sm font-semibold text-green-800\">\n                        <div>架构支持：</div>\n                        <div>• x86-64, ARM64</div>\n                        <div class=\"text-orange-600\">• LoongArch (龙芯)</div>\n                        <div class=\"text-orange-600\">• 华为鲲鹏960</div>\n                        <div>• MIPS</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td class=\"border border-gray-300 px-4 py-3\">\n                    <div class=\"space-y-1\">\n                      <div class=\"feature-tag\">SANE</div>\n                      <div class=\"feature-tag\">eSCL</div>\n                    </div>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n\n          <!-- 国产化特色说明 -->\n          <div\n            class=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-orange-200 rounded-lg p-6\"\n          >\n            <h3 class=\"font-bold text-orange-800 mb-3 flex items-center\">\n              <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path\n                  fill-rule=\"evenodd\"\n                  d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zm8 0a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1h-6a1 1 0 01-1-1v-6z\"\n                  clip-rule=\"evenodd\"\n                ></path>\n              </svg>\n              国产化平台支持优势\n            </h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-orange-700\">\n              <div>\n                <h4 class=\"font-semibold mb-2\">操作系统支持</h4>\n                <ul class=\"space-y-1 text-sm\">\n                  <li>• 统信UOS桌面版和服务器版</li>\n                  <li>• 银河麒麟桌面版和服务器版</li>\n                  <li>• 中科方德桌面操作系统</li>\n                  <li>• 深度Deepin操作系统</li>\n                </ul>\n              </div>\n              <div>\n                <h4 class=\"font-semibold mb-2\">CPU架构支持</h4>\n                <ul class=\"space-y-1 text-sm\">\n                  <li>• 龙芯LoongArch 64位架构</li>\n                  <li>• 华为鲲鹏960 ARM64架构</li>\n                  <li>• 飞腾Phytium ARM64架构</li>\n                  <li>• 海光x86-64架构</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 详细技术特性表格 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">详细技术特性</h2>\n\n          <!-- 图像采集和扫描 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">图像采集和文档扫描</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">TWAIN协议支持</td>\n                    <td class=\"feature-value\">\n                      支持TWAIN 1.9及以上规范；WIA 2.0\n                    </td>\n                    <td class=\"feature-platform\">Windows客户端</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">macOS扫描支持</td>\n                    <td class=\"feature-value\">支持TWAIN 1.9规范；ICA兼容</td>\n                    <td class=\"feature-platform\">macOS客户端</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">Linux扫描支持</td>\n                    <td class=\"feature-value\">SANE兼容；支持国产操作系统</td>\n                    <td class=\"feature-platform\">Linux客户端</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">网络扫描支持</td>\n                    <td class=\"feature-value\">eSCL兼容；支持网络扫描仪</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">RESTful API</td>\n                    <td class=\"feature-value\">支持RESTful API接口</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">高容量扫描</td>\n                    <td class=\"feature-value\">\n                      可选磁盘缓存机制，支持数千页扫描\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">自动进纸器(ADF)</td>\n                    <td class=\"feature-value\">内置ADF支持和多图像采集</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">双面扫描</td>\n                    <td class=\"feature-value\">支持双面扫描模式</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">空白页检测</td>\n                    <td class=\"feature-value\">支持空白页检测功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">智能向导模式</td>\n                    <td class=\"feature-value\">内置向导模式智能管理TWAIN状态</td>\n                    <td class=\"feature-platform\">Windows</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">扫描参数设置</td>\n                    <td class=\"feature-value\">\n                      支持分辨率、像素类型、位深度、亮度、对比度、页面大小等参数设置\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图像传输模式</td>\n                    <td class=\"feature-value\">\n                      提供原生和磁盘文件图像传输模式\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">缓冲内存传输</td>\n                    <td class=\"feature-value\">缓冲内存传输模式</td>\n                    <td class=\"feature-platform\">Windows客户端</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- 图像编辑功能 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">图像编辑功能</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">图像编辑器</td>\n                    <td class=\"feature-value\">提供GUI和非GUI图像编辑器</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">基础编辑功能</td>\n                    <td class=\"feature-value\">\n                      内置旋转、裁剪、镜像、翻转、擦除、改变图像大小等功能\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图形标注</td>\n                    <td class=\"feature-value\">支持添加彩色矩形到图像</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">文本标注</td>\n                    <td class=\"feature-value\">支持文本注释功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图像交换</td>\n                    <td class=\"feature-value\">提供图像交换功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">区域处理</td>\n                    <td class=\"feature-value\">支持清除指定区域并用颜色填充</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图像缩放</td>\n                    <td class=\"feature-value\">内置缩放功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">多选功能</td>\n                    <td class=\"feature-value\">提供多图像选择功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图像纠偏</td>\n                    <td class=\"feature-value\">提供图像去倾斜功能</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">高级编辑</td>\n                    <td class=\"feature-value\">\n                      魔术棒选择、矩形选择、填白处理、马赛克处理、去黑边、去底色等\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">撤销操作</td>\n                    <td class=\"feature-value\">支持UNDO撤销操作</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">鼠标交互</td>\n                    <td class=\"feature-value\">鼠标滚轮缩放图像</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- 保存、上传和下载 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">保存、上传和下载</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">HTTP/HTTPS传输</td>\n                    <td class=\"feature-value\">通过HTTP/HTTPS下载/上传图像</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">FTP传输</td>\n                    <td class=\"feature-value\">\n                      通过FTP下载/上传图像（目前不支持FTPS）\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">多格式支持</td>\n                    <td class=\"feature-value\">\n                      保存和上传BMP、JPEG、PNG、TIFF和PDF文件\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">多页文档</td>\n                    <td class=\"feature-value\">支持保存为多页TIFF和多页PDF</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">Base64编码</td>\n                    <td class=\"feature-value\">支持将图像保存为base64字符串</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">Cookie和会话</td>\n                    <td class=\"feature-value\">提供cookie和会话集成支持</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">上传绑定</td>\n                    <td class=\"feature-value\">支持在上传过程中绑定cookie</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">压缩选项</td>\n                    <td class=\"feature-value\">\n                      提供RLE、G3、G4、JBIG2、LZW、PackBits、JPEG、JPEG2000和TIFF压缩选项\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">上传进度</td>\n                    <td class=\"feature-value\">\n                      上传进度对话框，可随时取消上传\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">流下载</td>\n                    <td class=\"feature-value\">支持从HTTP流下载</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">内置编码器</td>\n                    <td class=\"feature-value\">\n                      内置JPEG、PNG、PDF和TIFF编码器，支持图像压缩\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">元数据上传</td>\n                    <td class=\"feature-value\">\n                      上传每个图像的元数据到服务器或数据库（SQL\n                      Server、MySQL、MS Access等）\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">分块上传</td>\n                    <td class=\"feature-value\">数据量大时支持分块上传</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">多线程上传</td>\n                    <td class=\"feature-value\">后台多线程上传</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- 本地文件处理 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">本地文件处理</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">PDF文件支持</td>\n                    <td class=\"feature-value\">\n                      通过PDF光栅化SDK（附加模块），支持加载基于文本、图像和混合PDF文件\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">图像格式支持</td>\n                    <td class=\"feature-value\">\n                      支持加载本地JPEG、PNG、BMP、单页和多页TIFF文件\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">剪贴板支持</td>\n                    <td class=\"feature-value\">从剪贴板加载DIB</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">数据格式支持</td>\n                    <td class=\"feature-value\">从Base64和二进制数据加载图像</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- 安全特性 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">安全特性</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">数据保护</td>\n                    <td class=\"feature-value\">\n                      SDK意外退出时，所有缓存数据将被销毁\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">域名绑定</td>\n                    <td class=\"feature-value\">许可过程中可选域名绑定</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">来源控制</td>\n                    <td class=\"feature-value\">可配置为仅响应指定来源的请求</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">文件访问授权</td>\n                    <td class=\"feature-value\">访问本地文件需要授权</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">设备访问授权</td>\n                    <td class=\"feature-value\">访问扫描仪或摄像头需要授权</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">自定义证书</td>\n                    <td class=\"feature-value\">\n                      可自定义支持的证书以替代Dynamsoft默认证书\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">数字签名</td>\n                    <td class=\"feature-value\">由VeriSign数字签名</td>\n                    <td class=\"feature-platform\">Windows</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">代理支持</td>\n                    <td class=\"feature-value\">支持代理连接</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">SSL支持</td>\n                    <td class=\"feature-value\">支持SSL用于HTTP上传和下载</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">数据加密</td>\n                    <td class=\"feature-value\">\n                      扫描图像数据或缓存扫描数据已加密，只能由Dynamsoft扫描服务访问\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">设备访问限制</td>\n                    <td class=\"feature-value\">\n                      限制扫描仪设备仅供注册的Web扫描应用程序访问\n                    </td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- 集成和兼容性 -->\n          <div class=\"feature-section\">\n            <h3 class=\"feature-section-title\">集成和兼容性</h3>\n            <div class=\"overflow-x-auto\">\n              <table class=\"feature-table\">\n                <tbody>\n                  <tr>\n                    <td class=\"feature-name\">Citrix环境</td>\n                    <td class=\"feature-value\">支持Citrix环境部署</td>\n                    <td class=\"feature-platform\">Windows</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">Electron支持</td>\n                    <td class=\"feature-value\">支持Electron跨平台应用</td>\n                    <td class=\"feature-platform\">全平台</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">国产化环境</td>\n                    <td class=\"feature-value\">\n                      完全支持统信UOS、麒麟操作系统等国产化环境\n                    </td>\n                    <td class=\"feature-platform\">Linux</td>\n                  </tr>\n                  <tr>\n                    <td class=\"feature-name\">多架构CPU</td>\n                    <td class=\"feature-value\">\n                      支持LoongArch、华为鲲鹏960、飞腾等国产CPU架构\n                    </td>\n                    <td class=\"feature-platform\">Linux</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n\n        <!-- 生产力优势 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">生产力优势</h2>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">大幅减少开发时间</h3>\n              <p class=\"productivity-desc\">\n                从零开始熟悉TWAIN规范并构建TWAIN插件可能需要数月时间。使用ScanOnWeb，您可以在几天内实现TWAIN功能。对于大多数应用程序，您只需几行代码即可实现TWAIN支持。\n              </p>\n            </div>\n\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">浏览器兼容性保障</h3>\n              <p class=\"productivity-desc\">\n                各大浏览器厂商不断更新其浏览器版本。您可以放心，我们会持续更新ScanOnWeb以保持兼容性，确保您的应用程序能够持续稳定运行。\n              </p>\n            </div>\n\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">更大的时间节省</h3>\n              <p class=\"productivity-desc\">\n                通过我们的专业技术支持服务，使用ScanOnWeb构建扫描解决方案所需的总时间可以从数月缩短到数天。\n              </p>\n            </div>\n\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">缩短产品上市时间</h3>\n              <p class=\"productivity-desc\">\n                产品交付速度至关重要。ScanOnWeb可以显著减少开发时间，让您的产品更快进入市场。\n              </p>\n            </div>\n\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">国产化支持优势</h3>\n              <p class=\"productivity-desc\">\n                作为国内唯一成熟稳定的扫描控件产品，ScanOnWeb在国产化环境下具有独特优势，支持信创项目和政府采购需求。\n              </p>\n            </div>\n\n            <div class=\"productivity-item\">\n              <h3 class=\"productivity-title\">多年行业经验</h3>\n              <p class=\"productivity-desc\">\n                经过多年发展，ScanOnWeb已被广泛应用于税务、公安、建筑、银行等多个行业，积累了丰富的实践经验和稳定的技术基础。\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 行动号召 -->\n        <div class=\"card-business p-8 text-center\">\n          <h2 class=\"heading-secondary mb-4\">准备开始使用？</h2>\n          <p class=\"text-lg text-gray-600 mb-6\">\n            立即体验ScanOnWeb的强大功能，开始您的扫描应用开发之旅\n          </p>\n          <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <NuxtLink to=\"/download\" class=\"btn-primary\">\n              免费下载试用\n            </NuxtLink>\n            <NuxtLink to=\"/contact\" class=\"btn-secondary\">\n              联系销售咨询\n            </NuxtLink>\n            <NuxtLink to=\"/demo\" class=\"btn-outline\"> 在线演示体验 </NuxtLink>\n          </div>\n        </div>\n      </div>\n    </main>\n    <Footer />\n  </div>\n</template>\n\n<script setup>\nimport Header from \"~/components/Header.vue\";\nimport Footer from \"~/components/Footer.vue\";\n\n// SEO设置\nuseHead({\n  title: \"ScanOnWeb 技术特性表格 - 全面的平台支持和功能规格\",\n  meta: [\n    {\n      name: \"description\",\n      content:\n        \"ScanOnWeb扫描控件完整技术特性表格，包含平台兼容性、扫描协议支持、国产化平台支持、多架构CPU支持等详细规格信息。\",\n    },\n    {\n      name: \"keywords\",\n      content:\n        \"ScanOnWeb,技术特性,平台支持,国产化,LoongArch,鲲鹏960,统信UOS,麒麟操作系统,TWAIN,SANE,扫描协议\",\n    },\n  ],\n});\n</script>\n\n<style scoped>\n.feature-tag {\n  @apply inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mr-1 mb-1;\n}\n\n.browser-tag {\n  @apply inline-block bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded mr-1 mb-1;\n}\n\n.framework-tag {\n  @apply inline-block bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded mr-1 mb-1;\n}\n\n.server-tag {\n  @apply inline-block bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded mr-1 mb-1;\n}\n\ntable {\n  @apply shadow-lg;\n}\n\nth {\n  @apply text-sm;\n}\n\ntd {\n  @apply text-sm;\n}\n\n.card-business {\n  @apply bg-white rounded-lg shadow-md;\n}\n\n.feature-section {\n  @apply mb-8;\n}\n\n.feature-section-title {\n  @apply text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-orange-500;\n}\n\n.feature-table {\n  @apply w-full border-collapse shadow-lg;\n}\n\n.feature-name {\n  @apply border border-gray-300 px-4 py-3 bg-gray-50 font-medium text-gray-900 w-1/4;\n}\n\n.feature-value {\n  @apply border border-gray-300 px-4 py-3 text-gray-700 w-1/2;\n}\n\n.feature-platform {\n  @apply border border-gray-300 px-4 py-3 text-center font-medium w-1/4;\n}\n\n.feature-platform:contains(\"Windows\") {\n  @apply bg-blue-50 text-blue-800;\n}\n\n.feature-platform:contains(\"macOS\") {\n  @apply bg-gray-50 text-gray-800;\n}\n\n.feature-platform:contains(\"Linux\") {\n  @apply bg-green-50 text-green-800;\n}\n\n.feature-platform:contains(\"全平台\") {\n  @apply bg-orange-50 text-orange-800;\n}\n\n.productivity-item {\n  @apply bg-gray-50 p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow;\n}\n\n.productivity-title {\n  @apply text-lg font-bold text-gray-800 mb-3;\n}\n\n.productivity-desc {\n  @apply text-gray-600 leading-relaxed;\n}\n\n.btn-outline {\n  @apply border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white font-medium py-2 px-4 rounded transition-colors;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAyuBQ,YAAA;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,MACF;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}