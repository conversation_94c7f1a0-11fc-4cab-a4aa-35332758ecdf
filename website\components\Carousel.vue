<template>
  <div class="relative h-[500px] overflow-hidden"> <!-- 调整高度 -->
    <div v-for="(slide, index) in slides" :key="index" 
         class="absolute top-0 left-0 w-full h-full transition-opacity duration-500 ease-in-out"
         :class="{ 'opacity-100': currentSlide === index, 'opacity-0': currentSlide !== index }">
      <img :src="slide.image" :alt="slide.alt" class="w-full h-full object-cover">
      <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-6"> <!-- 增加内边距 -->
        <h2 class="text-3xl font-bold mb-2">{{ slide.title }}</h2> <!-- 增加字体大小 -->
        <p class="text-lg">{{ slide.description }}</p> <!-- 增加字体大小 -->
      </div>
    </div>
    <button @click="prevSlide" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full">
      &#8249; <!-- 左箭头 -->
    </button>
    <button @click="nextSlide" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full">
      &#8250; <!-- 右箭头 -->
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const slides = [
  { image: '/images/slide1.png', alt: 'Slide 1', title: '跨平台扫描插件解决方案', description: '为您的业务提供最先进的技术' },
  { image: '/images/slide2.png', alt: 'Slide 2', title: '专业团队', description: '经验丰富的开发者为您服务' },
  { image: '/images/slide3.png', alt: 'Slide 3', title: '客户满意度', description: '我们以客户的成功为己任' },
];

const currentSlide = ref(0);
const timer = ref(null);

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.length;
};

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + slides.length) % slides.length;
};

onMounted(() => {
  timer.value = setInterval(nextSlide, 5000);
});

onUnmounted(() => {
  clearInterval(timer.value);
});
</script>