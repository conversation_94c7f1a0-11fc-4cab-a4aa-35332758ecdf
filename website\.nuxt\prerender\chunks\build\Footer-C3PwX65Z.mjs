import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { mergeProps, withCtx, createTextVNode, openBlock, createBlock, createVNode, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssrRenderAttrs, ssrRenderAttr, ssrRenderComponent } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { p as publicAssetsURL } from '../_/renderer.mjs';

const _imports_0 = publicAssetsURL("/images/logo.jpg");
const _sfc_main$1 = {
  __name: "Header",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<header${ssrRenderAttrs(mergeProps({ class: "bg-white border-b border-gray-200 shadow-sm" }, _attrs))}><div class="container mx-auto px-4 py-4"><div class="flex justify-between items-center"><div class="flex items-center"><img${ssrRenderAttr("src", _imports_0)} alt="brainysoft Logo" class="h-10 mr-3"><div><h1 class="text-2xl font-bold text-gray-900">brainysoft.cn</h1><p class="text-sm text-gray-500">\u4E13\u4E1AWeb\u63A7\u4EF6\u89E3\u51B3\u65B9\u6848</p></div></div><nav class="flex items-center space-x-8"><ul class="flex space-x-6"><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/products",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u4EA7\u54C1\u4ECB\u7ECD`);
          } else {
            return [
              createTextVNode("\u4EA7\u54C1\u4ECB\u7ECD")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/demo",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u5728\u7EBF\u6F14\u793A`);
          } else {
            return [
              createTextVNode("\u5728\u7EBF\u6F14\u793A")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u6587\u6863\u8D44\u6599`);
          } else {
            return [
              createTextVNode("\u6587\u6863\u8D44\u6599")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/purchase",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u6CE8\u518C\u8D2D\u4E70`);
          } else {
            return [
              createTextVNode("\u6CE8\u518C\u8D2D\u4E70")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li><li>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/contact",
        class: "text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200",
        "active-class": "text-orange-500 border-b-2 border-orange-500",
        "exact-active-class": "font-semibold"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u8054\u7CFB\u6211\u4EEC`);
          } else {
            return [
              createTextVNode("\u8054\u7CFB\u6211\u4EEC")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</li></ul>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/download",
        class: "btn-primary flex items-center space-x-2"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"${_scopeId}></path></svg><span${_scopeId}>\u514D\u8D39\u4E0B\u8F7D</span>`);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                })
              ])),
              createVNode("span", null, "\u514D\u8D39\u4E0B\u8F7D")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</nav></div></div></header>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Header.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "Footer",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<footer${ssrRenderAttrs(mergeProps({ class: "bg-gray-800 text-white py-8" }, _attrs))}><div class="container mx-auto"><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div><h3 class="text-xl font-bold mb-4">\u5173\u4E8E\u6211\u4EEC</h3><p>\u6211\u4EEC\u662F\u4E00\u5BB6\u81F4\u529B\u4E8E\u63D0\u4F9B\u56FE\u50CF\u626B\u63CF\u5904\u7406\u8F6F\u4EF6\u89E3\u51B3\u65B9\u6848\u7684\u516C\u53F8,\u4E3A\u7B80\u5316\u56FE\u50CF\u626B\u63CF\u7F16\u7A0B\u5E94\u7528\u800C\u52AA\u529B\u3002</p></div><div><h3 class="text-xl font-bold mb-4">\u8054\u7CFB\u65B9\u5F0F</h3><p>\u90AE\u7BB1: <EMAIL></p><p>\u7535\u8BDD: (+86) 155-1196-5595</p><p>\u5FAE\u4FE1: 20155031</p></div><div><h3 class="text-xl font-bold mb-4">\u5FEB\u901F\u94FE\u63A5</h3><ul><li><a href="http://www.regsky.com" target="_blank" class="hover:text-gray-300">\u5929\u7A7A\u4E91\u79D1\u6280</a></li><li><a href="#" class="hover:text-gray-300">\u8054\u7CFB\u6211\u4EEC</a></li></ul></div><div><h3 class="text-xl font-bold mb-4">\u5173\u6CE8\u6211\u4EEC</h3><div class="flex space-x-4"><a href="#" class="hover:text-gray-300">Facebook</a><a href="#" class="hover:text-gray-300">Twitter</a><a href="#" class="hover:text-gray-300">LinkedIn</a></div></div></div><div class="mt-8 text-center"><p>\xA9 2024 brainysoft. \u4FDD\u7559\u6240\u6709\u6743\u5229\u3002</p></div></div></footer>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Footer.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main$1 as _, _sfc_main as a };
//# sourceMappingURL=Footer-C3PwX65Z.mjs.map
