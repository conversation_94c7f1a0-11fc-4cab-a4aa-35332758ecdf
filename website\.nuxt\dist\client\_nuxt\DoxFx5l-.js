import{_ as i}from"./L-3nxwpR.js";import{u as o}from"./Cp9XtYH-.js";import{_ as r,a as v}from"./DYzyhJ5q.js";import{_ as g,c as p,b as d,a as e,w as l,t as m,j as n,o as u,d as t,p as b,e as h}from"./Dy7juzJL.js";const a=s=>(b("data-v-ec61ad0e"),s=s(),h(),s),x={class:"bg-gray-50 min-h-screen"},f={class:"bg-white border-b border-gray-200"},_={class:"container mx-auto px-4 py-4"},w={class:"flex items-center space-x-2 text-sm text-gray-500"},y=a(()=>e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),S=a(()=>e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)),I=a(()=>e("span",{class:"text-gray-900 font-medium"},"ScanOnWeb 入门指南",-1)),L=n('<div class="bg-white py-8" data-v-ec61ad0e><div class="container mx-auto px-4" data-v-ec61ad0e><div class="max-w-4xl" data-v-ec61ad0e><h1 class="heading-primary mb-4" data-v-ec61ad0e>ScanOnWeb 入门指南</h1><p class="text-lg text-gray-600 mb-6" data-v-ec61ad0e> 快速上手ScanOnWeb扫描控件，支持原生HTML和Vue3项目集成 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-ec61ad0e><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>5分钟快速上手</span></div><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>完整代码示例</span></div><div class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg><span data-v-ec61ad0e>多框架支持</span></div></div></div></div></div>',1),B={class:"container mx-auto py-12 px-4"},M=n('<div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>概述</h2><p class="text-business mb-4" data-v-ec61ad0e> ScanOnWeb是一款专业的Web扫描控件，通过WebSocket与本地托盘服务通信，实现在浏览器中直接控制扫描仪设备。 控件支持多种扫描模式、图像处理功能，并可无缝集成到各种Web应用中。 </p><div class="feature-highlight" data-v-ec61ad0e><h4 class="font-bold text-orange-700 mb-2" data-v-ec61ad0e>重要提醒</h4><p class="text-orange-600" data-v-ec61ad0e> ScanOnWeb控件需要用户在本地安装托盘服务程序才能正常工作。控件文件需要手动复制到项目中，未发布到npm仓库。 </p></div></div><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>系统要求</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-ec61ad0e><div data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>客户端要求</h3><ul class="space-y-2 text-business" data-v-ec61ad0e><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Windows 7/8/10/11 或 Linux </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> 支持WebSocket的现代浏览器 </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> 兼容TWAIN 1.9+或WIA协议的扫描仪 </li></ul></div><div data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>浏览器支持</h3><ul class="space-y-2 text-business" data-v-ec61ad0e><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Chrome 60+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Firefox 55+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Edge 79+ </li><li class="flex items-center" data-v-ec61ad0e><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-ec61ad0e><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-ec61ad0e></path></svg> Internet Explorer 11 </li></ul></div></div></div><div class="card-business p-8 mb-8" data-v-ec61ad0e><h2 class="heading-secondary mb-6" data-v-ec61ad0e>安装步骤</h2><div class="space-y-6" data-v-ec61ad0e><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>步骤1：下载托盘服务程序</h3><p class="text-business mb-4" data-v-ec61ad0e> 首先需要在客户端计算机上安装ScanOnWeb托盘服务程序，该程序负责与扫描仪硬件通信。 </p><div class="bg-gray-50 p-4 rounded-lg" data-v-ec61ad0e><p class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>下载链接：</p><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" class="text-orange-500 hover:text-orange-600 font-medium" target="_blank" data-v-ec61ad0e> ScanOnWebH5Install.exe </a></div></div><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>步骤2：获取控件文件</h3><p class="text-business mb-4" data-v-ec61ad0e> 从官方获取scanonweb.js控件文件，该文件包含了所有扫描功能的JavaScript API。 </p><div class="bg-gray-50 p-4 rounded-lg" data-v-ec61ad0e><p class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>文件说明：</p><ul class="text-sm space-y-1" data-v-ec61ad0e><li data-v-ec61ad0e> • <code class="bg-gray-200 px-2 py-1 rounded" data-v-ec61ad0e>scanonweb.js</code> - 核心控件文件 </li><li data-v-ec61ad0e>• 版本：1.0.1</li><li data-v-ec61ad0e>• 大小：约15KB</li></ul></div></div><div class="border-l-4 border-orange-500 pl-6" data-v-ec61ad0e><h3 class="heading-tertiary mb-3" data-v-ec61ad0e>步骤3：复制文件到项目</h3><p class="text-business mb-4" data-v-ec61ad0e> 将scanonweb.js文件复制到您的Web项目目录中。建议放在js或assets目录下。 </p><div class="bg-gray-100 border border-gray-300 p-4 rounded-lg" data-v-ec61ad0e><div class="text-sm text-gray-600 mb-2" data-v-ec61ad0e>项目结构示例：</div><div class="font-mono text-sm text-gray-800" data-v-ec61ad0e><div data-v-ec61ad0e>your-project/</div><div data-v-ec61ad0e>├── js/</div><div data-v-ec61ad0e> │   └── scanonweb.js  <span class="text-orange-600" data-v-ec61ad0e>← 控件文件</span></div><div data-v-ec61ad0e>├── index.html</div><div data-v-ec61ad0e>└── ...</div></div></div></div></div></div>',3),W=a(()=>e("h2",{class:"heading-secondary mb-6"},"原生HTML集成",-1)),k=a(()=>e("p",{class:"text-business"}," 在原生HTML项目中使用ScanOnWeb控件非常简单，只需要引入JavaScript文件并初始化即可。 ",-1)),C=a(()=>e("h3",{class:"heading-tertiary mb-3"},"完整HTML示例",-1)),E=a(()=>e("div",{class:"text-sm text-gray-600 mb-3"}," 复制以下代码到您的HTML文件中： ",-1)),D=n('<div class="bg-blue-50 border border-blue-200 rounded-lg p-6" data-v-ec61ad0e><h4 class="font-bold text-blue-800 mb-3" data-v-ec61ad0e>关键代码说明</h4><ul class="space-y-2 text-blue-700" data-v-ec61ad0e><li data-v-ec61ad0e><strong data-v-ec61ad0e>初始化：</strong><code data-v-ec61ad0e>let scanonweb = new ScanOnWeb();</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>获取设备：</strong><code data-v-ec61ad0e>scanonweb.loadDevices()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>开始扫描：</strong> <code data-v-ec61ad0e>scanonweb.startScan()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>获取图像：</strong><code data-v-ec61ad0e>scanonweb.getAllImage()</code></li><li data-v-ec61ad0e><strong data-v-ec61ad0e>事件回调：</strong> 通过 <code data-v-ec61ad0e>onScanFinishedEvent</code> 等事件处理扫描结果 </li></ul></div>',1),O={class:"card-business p-8"},z=a(()=>e("h2",{class:"heading-secondary mb-6"},"技术支持",-1)),A={class:"bg-orange-50 border border-orange-200 rounded-lg p-6"},H={class:"flex items-start"},T=a(()=>e("svg",{class:"w-6 h-6 text-orange-500 mr-3 mt-1 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})],-1)),j=a(()=>e("h4",{class:"font-bold text-orange-800 mb-2"},"需要帮助？",-1)),N=a(()=>e("p",{class:"text-orange-700 mb-4"}," 如果您在集成过程中遇到问题，或需要更多技术支持，请联系我们的技术团队。 ",-1)),V=a(()=>e("div",{class:"space-y-2 text-orange-700"},[e("p",null,[e("strong",null,"技术支持邮箱："),t(" <EMAIL>")]),e("p",null,[e("strong",null,"技术支持QQ："),t(" 123456789")]),e("p",null,[e("strong",null,"工作时间："),t(" 周一至周五 9:00-18:00")])],-1)),F={class:"mt-4"},G=`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScanOnWeb 扫描示例</title>
</head>
<body>
    <h1>ScanOnWeb 扫描控件演示</h1>

    <!-- 控制面板 -->
    <div id="controlPanel">
        <label>选择扫描设备：</label>
        <select id="deviceSelect"></select>

        <label>分辨率：</label>
        <input type="number" id="dpiX" value="300" style="width: 60px;"> x
        <input type="number" id="dpiY" value="300" style="width: 60px;">

        <label>色彩模式：</label>
        <select id="colorMode">
            <option value="RGB">彩色</option>
            <option value="GRAY">灰色</option>
            <option value="BW">黑白</option>
        </select>

        <br><br>

        <button onclick="loadDevices()">获取设备列表</button>
        <button onclick="startScan()">开始扫描</button>
        <button onclick="getAllImages()">获取所有图像</button>
        <button onclick="clearAll()">清空结果</button>
    </div>

    <!-- 图像显示区域 -->
    <div id="imageContainer">
        <h3>扫描结果：</h3>
        <div id="imageList"></div>
    </div>

    <!-- 引入ScanOnWeb控件 -->
    <script src="js/scanonweb.js"><\/script>

    <script>
        // 初始化扫描控件
        let scanonweb = new ScanOnWeb();
        let selectedDeviceIndex = 0;

        // 获取设备列表
        function loadDevices() {
            scanonweb.onGetDevicesListEvent = function(msg) {
                const deviceSelect = document.getElementById('deviceSelect');
                deviceSelect.innerHTML = '';

                msg.devices.forEach((device, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = device;
                    deviceSelect.appendChild(option);
                });

                selectedDeviceIndex = msg.currentIndex;
                deviceSelect.value = selectedDeviceIndex;
            };

            scanonweb.loadDevices();
        }

        // 开始扫描
        function startScan() {
            if (selectedDeviceIndex === -1) {
                alert('请先选择扫描设备！');
                return;
            }

            // 配置扫描参数
            scanonweb.scaner_work_config = {
                deviceIndex: selectedDeviceIndex,
                dpi_x: parseInt(document.getElementById('dpiX').value),
                dpi_y: parseInt(document.getElementById('dpiY').value),
                colorMode: document.getElementById('colorMode').value,
                showDialog: false,
                autoFeedEnable: false,
                autoFeed: false,
                dupxMode: false,
                autoDeskew: false,
                autoBorderDetection: false
            };

            scanonweb.startScan();
        }

        // 获取所有图像
        function getAllImages() {
            scanonweb.onGetAllImageEvent = function(msg) {
                const imageList = document.getElementById('imageList');
                imageList.innerHTML = '';

                msg.images.forEach((imageBase64, index) => {
                    const img = document.createElement('img');
                    img.src = 'data:image/jpg;base64,' + imageBase64;
                    img.style.width = '200px';
                    img.style.height = '200px';
                    img.style.margin = '10px';
                    img.style.border = '1px solid #ccc';
                    imageList.appendChild(img);
                });
            };

            scanonweb.getAllImage();
        }

        // 清空所有图像
        function clearAll() {
            scanonweb.clearAll();
            document.getElementById('imageList').innerHTML = '';
        }

        // 设备选择变化事件
        document.getElementById('deviceSelect').addEventListener('change', function(e) {
            selectedDeviceIndex = parseInt(e.target.value);
            scanonweb.selectScanDevice(selectedDeviceIndex);
        });

        // 扫描完成事件回调
        scanonweb.onScanFinishedEvent = function(msg) {
            console.log('扫描完成，图像数量：', msg.imageAfterCount);
            getAllImages(); // 自动获取扫描结果
        };

        // 页面加载完成后自动获取设备列表
        window.onload = function() {
            loadDevices();
        };
    <\/script>
</body>
</html>`,Y={__name:"scanonweb-getting-started",setup(s){return o({title:"ScanOnWeb 入门指南 - 快速上手扫描控件",meta:[{name:"description",content:"ScanOnWeb扫描控件入门指南，详细介绍如何在HTML和Vue3项目中集成使用扫描功能，包含完整代码示例和最佳实践。"},{name:"keywords",content:"ScanOnWeb,扫描控件,入门指南,HTML,Vue3,JavaScript,WebSocket,扫描仪,集成教程"}]}),(J,P)=>{const c=i;return u(),p("div",x,[d(r),e("main",null,[e("div",f,[e("div",_,[e("nav",w,[d(c,{to:"/",class:"hover:text-orange-500"},{default:l(()=>[t("首页")]),_:1}),y,d(c,{to:"/documents",class:"hover:text-orange-500"},{default:l(()=>[t("文档资料")]),_:1}),S,I])])]),L,e("div",B,[M,e("div",{class:"card-business p-8 mb-8"},[W,e("div",{class:"space-y-6"},[k,e("div",null,[C,e("div",{class:"bg-gray-50 border border-gray-300 p-4 rounded-lg"},[E,e("div",{class:"bg-white border border-gray-200 p-4 rounded-lg",style:{"max-height":"600px","overflow-y":"auto"}},[e("pre",{class:"font-mono text-sm text-gray-800 whitespace-pre-wrap m-0 p-0"},m(G))])])]),D])]),e("div",O,[z,e("div",A,[e("div",H,[T,e("div",null,[j,N,V,e("div",F,[d(c,{to:"/contact",class:"btn-primary"},{default:l(()=>[t(" 联系技术支持 ")]),_:1})])])])])])])]),d(v)])}}},K=g(Y,[["__scopeId","data-v-ec61ad0e"]]);export{K as default};
