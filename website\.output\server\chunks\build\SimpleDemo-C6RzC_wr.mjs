import { ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { _ as _export_sfc } from './server.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';

const _sfc_main = {
  __name: "SimpleDemo",
  __ssrInlineRender: true,
  setup(__props) {
    const colorMode = ref("RGB");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "simple-demo" }, _attrs))} data-v-b6686b1b><div class="bg-white shadow-lg rounded-lg overflow-hidden p-6" data-v-b6686b1b><h2 class="text-2xl font-bold mb-4 text-blue-600 border-b pb-2" data-v-b6686b1b> \u7B80\u6613\u626B\u63CF\u5DE5\u5177 </h2><div class="mb-8" data-v-b6686b1b><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6" data-v-b6686b1b><div class="form-control" data-v-b6686b1b><label class="font-medium mb-1 block" data-v-b6686b1b>\u626B\u63CF\u8BBE\u5907</label><select id="simple-devices" class="w-full p-2 border rounded" data-v-b6686b1b></select></div><div class="form-control" data-v-b6686b1b><label class="font-medium mb-1 block" data-v-b6686b1b>\u8272\u5F69\u6A21\u5F0F</label><select id="simple-colorMode" class="w-full p-2 border rounded" data-v-b6686b1b><option value="RGB" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "RGB") : ssrLooseEqual(colorMode.value, "RGB")) ? " selected" : ""}>\u5F69\u8272</option><option value="GRAY" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "GRAY") : ssrLooseEqual(colorMode.value, "GRAY")) ? " selected" : ""}>\u7070\u8272</option><option value="BW" data-v-b6686b1b${ssrIncludeBooleanAttr(Array.isArray(colorMode.value) ? ssrLooseContain(colorMode.value, "BW") : ssrLooseEqual(colorMode.value, "BW")) ? " selected" : ""}>\u9ED1\u767D</option></select></div></div><div class="flex flex-wrap gap-2" data-v-b6686b1b><button class="btn bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-v-b6686b1b> \u83B7\u53D6\u8BBE\u5907\u5217\u8868 </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-b6686b1b> \u5F00\u59CB\u626B\u63CF </button><button class="btn bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors" data-v-b6686b1b> \u6E05\u7A7A\u7ED3\u679C </button><button class="btn bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors" data-v-b6686b1b> \u4FDD\u5B58\u4E3APDF </button></div></div><div data-v-b6686b1b><h3 class="text-xl font-semibold mb-3 text-blue-600" data-v-b6686b1b>\u626B\u63CF\u7ED3\u679C</h3><div id="simple-imageList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" data-v-b6686b1b></div></div><div class="mt-6 p-4 bg-yellow-100 text-yellow-800 rounded" data-v-b6686b1b><p class="font-bold" data-v-b6686b1b> \u6CE8\u610F\uFF1A\u672C\u6F14\u793A\u9700\u8981\u4E0B\u8F7D\u5B89\u88C5\u6258\u76D8\u626B\u63CF\u670D\u52A1\u624D\u53EF\u6B63\u5E38\u5DE5\u4F5C\u3002 </p><a href="https://www.brainysoft.cn/download/ScanOnWebH5Install.exe" target="_blank" class="text-blue-600 hover:underline mt-2 inline-block" data-v-b6686b1b> \u4E0B\u8F7D\u626B\u63CF\u670D\u52A1 </a></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/demos/SimpleDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const SimpleDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-b6686b1b"]]);

export { SimpleDemo as default };
//# sourceMappingURL=SimpleDemo-C6RzC_wr.mjs.map
