{"version": 3, "file": "documents-D_JGK4xR.js", "sources": ["../../../../pages/documents.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bg-gray-50 min-h-screen\">\r\n    <Header />\r\n    <main>\r\n      <!-- 面包屑导航 -->\r\n      <div class=\"bg-white border-b border-gray-200\">\r\n        <div class=\"container mx-auto px-4 py-4\">\r\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\r\n            <svg\r\n              class=\"w-4 h-4\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 5l7 7-7 7\"\r\n              ></path>\r\n            </svg>\r\n            <span class=\"text-gray-900 font-medium\">文档资料</span>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 页面标题区域 -->\r\n      <div class=\"bg-white py-12\">\r\n        <div class=\"container mx-auto px-4\">\r\n          <div class=\"max-w-4xl\">\r\n            <h1 class=\"heading-primary mb-4\">技术文档中心</h1>\r\n            <p class=\"text-xl text-gray-600 mb-6\">\r\n              完整的产品文档、API参考、示例代码和视频教程，助您快速上手和深入使用我们的产品\r\n            </p>\r\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>完整API文档</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>实用代码示例</span>\r\n              </div>\r\n              <div class=\"flex items-center\">\r\n                <svg\r\n                  class=\"w-4 h-4 mr-2 text-green-500\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 20 20\"\r\n                >\r\n                  <path\r\n                    fill-rule=\"evenodd\"\r\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                    clip-rule=\"evenodd\"\r\n                  ></path>\r\n                </svg>\r\n                <span>视频教程指导</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 产品选择器 -->\r\n      <div class=\"bg-orange-50 py-8\">\r\n        <div class=\"container mx-auto px-4\">\r\n          <div class=\"flex flex-col md:flex-row items-center justify-between\">\r\n            <div class=\"mb-4 md:mb-0\">\r\n              <h2 class=\"text-lg font-semibold text-gray-900 mb-2\">\r\n                选择产品查看文档\r\n              </h2>\r\n              <p class=\"text-gray-600\">每个产品都有完整的技术文档和使用指南</p>\r\n            </div>\r\n            <div class=\"flex flex-wrap gap-3\">\r\n              <button\r\n                v-for=\"product in products\"\r\n                :key=\"product.id\"\r\n                @click=\"selectProduct(product.id)\"\r\n                :class=\"[\r\n                  'px-6 py-3 rounded-lg font-semibold transition-all duration-200',\r\n                  selectedProductId === product.id\r\n                    ? 'bg-orange-500 text-white shadow-lg'\r\n                    : 'bg-white text-gray-700 hover:bg-orange-100 border border-gray-200',\r\n                ]\"\r\n              >\r\n                {{ product.name }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"container mx-auto px-4 py-12\">\r\n        <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\r\n          <!-- 文档分类导航 -->\r\n          <div class=\"lg:col-span-1\">\r\n            <div class=\"card-business p-6 sticky top-8\">\r\n              <h3 class=\"heading-tertiary mb-4\">文档分类</h3>\r\n              <nav class=\"space-y-2\">\r\n                <button\r\n                  v-for=\"category in documentCategories\"\r\n                  :key=\"category.id\"\r\n                  @click=\"selectCategory(category.id)\"\r\n                  :class=\"[\r\n                    'w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center',\r\n                    selectedCategoryId === category.id\r\n                      ? 'bg-orange-100 text-orange-700 border-l-4 border-orange-500'\r\n                      : 'text-gray-700 hover:bg-gray-50',\r\n                  ]\"\r\n                >\r\n                  <component :is=\"category.icon\" class=\"w-5 h-5 mr-3\" />\r\n                  <span class=\"font-medium\">{{ category.name }}</span>\r\n                  <span class=\"ml-auto text-sm text-gray-500\"\r\n                    >({{ getDocumentCount(category.id) }})</span\r\n                  >\r\n                </button>\r\n              </nav>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 文档列表 -->\r\n          <div class=\"lg:col-span-3\">\r\n            <div class=\"mb-6\">\r\n              <h2 class=\"heading-secondary mb-2\">\r\n                {{ getCurrentCategoryName() }}\r\n              </h2>\r\n              <p class=\"text-gray-600\">{{ getCurrentCategoryDescription() }}</p>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div\r\n                v-for=\"document in filteredDocuments\"\r\n                :key=\"document.id\"\r\n                class=\"card-business p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer\"\r\n                @click=\"selectDocument(document)\"\r\n              >\r\n                <div class=\"flex items-start\">\r\n                  <div\r\n                    class=\"w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\"\r\n                    :class=\"getDocumentTypeClass(document.type)\"\r\n                  >\r\n                    <component\r\n                      :is=\"getDocumentIcon(document.type)\"\r\n                      class=\"w-6 h-6 text-white\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"flex-1\">\r\n                    <h3 class=\"font-bold text-gray-900 mb-2\">\r\n                      {{ document.title }}\r\n                    </h3>\r\n                    <p class=\"text-gray-600 text-sm mb-3\">\r\n                      {{ document.description }}\r\n                    </p>\r\n                    <div\r\n                      class=\"flex items-center justify-between text-xs text-gray-500\"\r\n                    >\r\n                      <span>{{ document.version }}</span>\r\n                      <span>{{ document.date }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div\r\n              v-if=\"filteredDocuments.length === 0\"\r\n              class=\"text-center py-12\"\r\n            >\r\n              <svg\r\n                class=\"w-16 h-16 text-gray-300 mx-auto mb-4\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n                ></path>\r\n              </svg>\r\n              <h3 class=\"text-lg font-semibold text-gray-900 mb-2\">\r\n                暂无相关文档\r\n              </h3>\r\n              <p class=\"text-gray-500\">该产品的此类文档正在准备中，敬请期待</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n    <Footer />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed, h } from \"vue\";\r\nimport Header from \"~/components/Header.vue\";\r\nimport Footer from \"~/components/Footer.vue\";\r\n\r\n// 产品列表\r\nconst products = ref([\r\n  {\r\n    id: 1,\r\n    name: \"ScanOnWeb\",\r\n    description: \"专业扫描仪控件解决方案\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"ImageCapOnWeb\",\r\n    description: \"摄像头图像采集控件\",\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"GaoPaiYi\",\r\n    description: \"高拍仪图像采集控件\",\r\n  },\r\n]);\r\n\r\n// 当前选中的产品和分类\r\nconst selectedProductId = ref(1);\r\nconst selectedCategoryId = ref(\"getting-started\");\r\n\r\n// 文档分类定义\r\nconst documentCategories = ref([\r\n  {\r\n    id: \"getting-started\",\r\n    name: \"入门指南\",\r\n    description: \"快速开始使用产品的基础教程和安装指南\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\",\r\n        }),\r\n      ]),\r\n  },\r\n  {\r\n    id: \"api-docs\",\r\n    name: \"API文档\",\r\n    description: \"完整的API参考文档和接口说明\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\",\r\n        }),\r\n      ]),\r\n  },\r\n  {\r\n    id: \"video-tutorials\",\r\n    name: \"视频教程\",\r\n    description: \"直观的视频教学内容，快速掌握使用技巧\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\",\r\n        }),\r\n      ]),\r\n  },\r\n  {\r\n    id: \"code-examples\",\r\n    name: \"示例代码\",\r\n    description: \"实用的代码示例和最佳实践\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\",\r\n        }),\r\n      ]),\r\n  },\r\n  {\r\n    id: \"faq\",\r\n    name: \"常见问题\",\r\n    description: \"常见问题解答和故障排除指南\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\",\r\n        }),\r\n      ]),\r\n  },\r\n  {\r\n    id: \"features\",\r\n    name: \"技术特性\",\r\n    description: \"详细的技术规格表格和平台兼容性信息\",\r\n    icon: () =>\r\n      h(\"svg\", { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" }, [\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\",\r\n        }),\r\n        h(\"path\", {\r\n          \"stroke-linecap\": \"round\",\r\n          \"stroke-linejoin\": \"round\",\r\n          \"stroke-width\": \"2\",\r\n          d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\",\r\n        }),\r\n      ]),\r\n  },\r\n]);\r\n\r\n// 所有文档数据（按产品和分类组织）\r\nconst allDocuments = ref([\r\n  // ScanOnWeb 文档\r\n  {\r\n    id: 1,\r\n    title: \"ScanOnWeb 入门指南\",\r\n    description: \"详细的集成教程，包含HTML和Vue3项目的完整示例代码\",\r\n    type: \"guide\",\r\n    category: \"getting-started\",\r\n    productId: 1,\r\n    date: \"2024-03-15\",\r\n    version: \"v3.5.0\",\r\n    url: \"/docs/scanonweb-getting-started\",\r\n  },\r\n  {\r\n    id: 16,\r\n    title: \"ScanOnWeb 快速入门指南\",\r\n    description: \"5分钟快速上手ScanOnWeb控件，包含安装、配置和基本使用\",\r\n    type: \"pdf\",\r\n    category: \"getting-started\",\r\n    productId: 1,\r\n    date: \"2024-02-15\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/docs/ScanOnWeb-QuickStart.pdf\",\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"ScanOnWeb API 参考手册\",\r\n    description: \"完整的API接口文档，包含所有方法、事件和参数说明\",\r\n    type: \"code\",\r\n    category: \"api-docs\",\r\n    productId: 1,\r\n    date: \"2024-02-20\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/docs/scanonweb-api\",\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"ScanOnWeb 视频教程\",\r\n    description: \"通过视频演示学习ScanOnWeb的安装配置和常用功能\",\r\n    type: \"video\",\r\n    category: \"video-tutorials\",\r\n    productId: 1,\r\n    date: \"2024-02-25\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/docs/ScanOnWeb-Video.mp4\",\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"Vue3 + Spring Boot 集成示例\",\r\n    description: \"Vue3前端 + Spring Boot后端的完整ScanOnWeb集成项目\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 1,\r\n    date: \"2024-03-01\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/downloads/examples/scanonweb-vue3-springboot.zip\",\r\n  },\r\n  {\r\n    id: 41,\r\n    title: \"Vue3 + ASP.NET Core 集成示例\",\r\n    description: \"Vue3前端 + ASP.NET Core后端的完整ScanOnWeb集成项目\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 1,\r\n    date: \"2024-03-05\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/downloads/examples/scanonweb-vue3-aspnetcore.zip\",\r\n  },\r\n  {\r\n    id: 42,\r\n    title: \"HTML + JS + Spring Boot 集成示例\",\r\n    description: \"原生HTML/JavaScript前端 + Spring Boot后端的ScanOnWeb集成项目\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 1,\r\n    date: \"2024-03-08\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/downloads/examples/scanonweb-html-springboot.zip\",\r\n  },\r\n  {\r\n    id: 43,\r\n    title: \"React + Spring Boot 集成示例\",\r\n    description: \"React前端 + Spring Boot后端的完整ScanOnWeb集成项目\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 1,\r\n    date: \"2024-03-10\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/downloads/examples/scanonweb-react-springboot.zip\",\r\n  },\r\n  {\r\n    id: 44,\r\n    title: \"Vue3 + Go 集成示例\",\r\n    description: \"Vue3前端 + Go后端的完整ScanOnWeb集成项目\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 1,\r\n    date: \"2024-03-12\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/downloads/examples/scanonweb-vue3-go.zip\",\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"ScanOnWeb 常见问题解答\",\r\n    description: \"使用过程中的常见问题和解决方案\",\r\n    type: \"text\",\r\n    category: \"faq\",\r\n    productId: 1,\r\n    date: \"2024-03-05\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/docs/scanonweb-faq\",\r\n  },\r\n  {\r\n    id: 6,\r\n    title: \"ScanOnWeb 技术特性表格\",\r\n    description: \"全面的技术规格、平台支持和功能特性对比表格\",\r\n    type: \"table\",\r\n    category: \"features\",\r\n    productId: 1,\r\n    date: \"2024-03-15\",\r\n    version: \"v3.5.0\",\r\n    downloadUrl: \"/docs/scanonweb-features\",\r\n  },\r\n\r\n  // ImageCapOnWeb 文档\r\n  {\r\n    id: 7,\r\n    title: \"ImageCapOnWeb 快速入门指南\",\r\n    description: \"摄像头控件的安装配置和基本使用教程\",\r\n    type: \"pdf\",\r\n    category: \"getting-started\",\r\n    productId: 2,\r\n    date: \"2024-01-20\",\r\n    version: \"v2.8.0\",\r\n    downloadUrl: \"/docs/ImageCapOnWeb-QuickStart.pdf\",\r\n  },\r\n  {\r\n    id: 7,\r\n    title: \"ImageCapOnWeb API 文档\",\r\n    description: \"摄像头控件的完整API接口说明\",\r\n    type: \"code\",\r\n    category: \"api-docs\",\r\n    productId: 2,\r\n    date: \"2024-01-25\",\r\n    version: \"v2.8.0\",\r\n    downloadUrl: \"/docs/ImageCapOnWeb-API.pdf\",\r\n  },\r\n  {\r\n    id: 8,\r\n    title: \"ImageCapOnWeb 视频教程\",\r\n    description: \"通过视频学习摄像头控件的使用方法\",\r\n    type: \"video\",\r\n    category: \"video-tutorials\",\r\n    productId: 2,\r\n    date: \"2024-02-05\",\r\n    version: \"v2.8.0\",\r\n    downloadUrl: \"/docs/ImageCapOnWeb-Video.mp4\",\r\n  },\r\n  {\r\n    id: 9,\r\n    title: \"ImageCapOnWeb React 集成示例\",\r\n    description: \"在React项目中集成ImageCapOnWeb的代码示例\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 2,\r\n    date: \"2024-02-10\",\r\n    version: \"v2.8.0\",\r\n    downloadUrl: \"/docs/ImageCapOnWeb-React-Example.zip\",\r\n  },\r\n  {\r\n    id: 10,\r\n    title: \"ImageCapOnWeb 常见问题\",\r\n    description: \"摄像头控件使用中的常见问题和解决方案\",\r\n    type: \"text\",\r\n    category: \"faq\",\r\n    productId: 2,\r\n    date: \"2024-02-15\",\r\n    version: \"v2.8.0\",\r\n    downloadUrl: \"/docs/ImageCapOnWeb-FAQ.pdf\",\r\n  },\r\n\r\n  // GaoPaiYi 文档\r\n  {\r\n    id: 11,\r\n    title: \"GaoPaiYi 快速入门指南\",\r\n    description: \"高拍仪控件的安装和基本使用方法\",\r\n    type: \"pdf\",\r\n    category: \"getting-started\",\r\n    productId: 3,\r\n    date: \"2024-03-05\",\r\n    version: \"v2.0.1\",\r\n    downloadUrl: \"/docs/GaoPaiYi-QuickStart.pdf\",\r\n  },\r\n  {\r\n    id: 12,\r\n    title: \"GaoPaiYi API 参考手册\",\r\n    description: \"高拍仪控件的API接口文档\",\r\n    type: \"code\",\r\n    category: \"api-docs\",\r\n    productId: 3,\r\n    date: \"2024-03-10\",\r\n    version: \"v2.0.1\",\r\n    downloadUrl: \"/docs/GaoPaiYi-API.pdf\",\r\n  },\r\n  {\r\n    id: 13,\r\n    title: \"GaoPaiYi 视频教程\",\r\n    description: \"通过视频学习高拍仪控件的使用\",\r\n    type: \"video\",\r\n    category: \"video-tutorials\",\r\n    productId: 3,\r\n    date: \"2024-03-20\",\r\n    version: \"v2.0.1\",\r\n    downloadUrl: \"/docs/GaoPaiYi-Video.mp4\",\r\n  },\r\n  {\r\n    id: 14,\r\n    title: \"GaoPaiYi Angular 集成示例\",\r\n    description: \"在Angular项目中集成GaoPaiYi的代码示例\",\r\n    type: \"code\",\r\n    category: \"code-examples\",\r\n    productId: 3,\r\n    date: \"2024-03-25\",\r\n    version: \"v2.0.1\",\r\n    downloadUrl: \"/docs/GaoPaiYi-Angular-Example.zip\",\r\n  },\r\n  {\r\n    id: 15,\r\n    title: \"GaoPaiYi 常见问题\",\r\n    description: \"高拍仪控件使用中的问题解答\",\r\n    type: \"text\",\r\n    category: \"faq\",\r\n    productId: 3,\r\n    date: \"2024-03-30\",\r\n    version: \"v2.0.1\",\r\n    downloadUrl: \"/docs/GaoPaiYi-FAQ.pdf\",\r\n  },\r\n]);\r\n\r\n// 计算属性和方法\r\nconst filteredDocuments = computed(() => {\r\n  return allDocuments.value.filter(\r\n    (doc) =>\r\n      doc.productId === selectedProductId.value &&\r\n      doc.category === selectedCategoryId.value\r\n  );\r\n});\r\n\r\nconst selectProduct = (productId) => {\r\n  selectedProductId.value = productId;\r\n};\r\n\r\nconst selectCategory = (categoryId) => {\r\n  selectedCategoryId.value = categoryId;\r\n};\r\n\r\nconst selectDocument = (document) => {\r\n  // 如果是在线指南，使用路由导航\r\n  if (document.url) {\r\n    navigateTo(document.url);\r\n  } else if (document.downloadUrl) {\r\n    // 如果是下载文档，在新窗口打开\r\n    window.open(document.downloadUrl, \"_blank\");\r\n  }\r\n};\r\n\r\nconst getCurrentCategoryName = () => {\r\n  const category = documentCategories.value.find(\r\n    (cat) => cat.id === selectedCategoryId.value\r\n  );\r\n  return category ? category.name : \"\";\r\n};\r\n\r\nconst getCurrentCategoryDescription = () => {\r\n  const category = documentCategories.value.find(\r\n    (cat) => cat.id === selectedCategoryId.value\r\n  );\r\n  return category ? category.description : \"\";\r\n};\r\n\r\nconst getDocumentCount = (categoryId) => {\r\n  return allDocuments.value.filter(\r\n    (doc) =>\r\n      doc.productId === selectedProductId.value && doc.category === categoryId\r\n  ).length;\r\n};\r\n\r\nconst getDocumentTypeClass = (type) => {\r\n  switch (type) {\r\n    case \"pdf\":\r\n      return \"bg-red-500\";\r\n    case \"video\":\r\n      return \"bg-blue-500\";\r\n    case \"code\":\r\n      return \"bg-green-500\";\r\n    case \"text\":\r\n      return \"bg-gray-500\";\r\n    case \"guide\":\r\n      return \"bg-orange-500\";\r\n    case \"table\":\r\n      return \"bg-purple-500\";\r\n    default:\r\n      return \"bg-gray-400\";\r\n  }\r\n};\r\n\r\nconst getDocumentIcon = (type) => {\r\n  switch (type) {\r\n    case \"pdf\":\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\",\r\n            }),\r\n          ]\r\n        );\r\n    case \"video\":\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\",\r\n            }),\r\n          ]\r\n        );\r\n    case \"code\":\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\",\r\n            }),\r\n          ]\r\n        );\r\n    case \"guide\":\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\",\r\n            }),\r\n          ]\r\n        );\r\n    case \"table\":\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1V10z\",\r\n            }),\r\n          ]\r\n        );\r\n    default:\r\n      return () =>\r\n        h(\r\n          \"svg\",\r\n          { fill: \"none\", stroke: \"currentColor\", viewBox: \"0 0 24 24\" },\r\n          [\r\n            h(\"path\", {\r\n              \"stroke-linecap\": \"round\",\r\n              \"stroke-linejoin\": \"round\",\r\n              \"stroke-width\": \"2\",\r\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\",\r\n            }),\r\n          ]\r\n        );\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.5s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA8NA,UAAM,WAAW,IAAI;AAAA,MACnB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IAAA,CACD;AAGK,UAAA,oBAAoB,IAAI,CAAC;AACzB,UAAA,qBAAqB,IAAI,iBAAiB;AAGhD,UAAM,qBAAqB,IAAI;AAAA,MAC7B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM,MACJ,EAAE,OAAO,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,eAAe;AAAA,UACvE,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,UACD,EAAE,QAAQ;AAAA,YACR,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UAAA,CACJ;AAAA,QAAA,CACF;AAAA,MACL;AAAA,IAAA,CACD;AAGD,UAAM,eAAe,IAAI;AAAA;AAAA,MAEvB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,MACP;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA;AAAA,MAGA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA;AAAA,MAGA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IAAA,CACD;AAGK,UAAA,oBAAoB,SAAS,MAAM;AACvC,aAAO,aAAa,MAAM;AAAA,QACxB,CAAC,QACC,IAAI,cAAc,kBAAkB,SACpC,IAAI,aAAa,mBAAmB;AAAA,MAAA;AAAA,IACxC,CACD;AAoBD,UAAM,yBAAyB,MAAM;AAC7B,YAAA,WAAW,mBAAmB,MAAM;AAAA,QACxC,CAAC,QAAQ,IAAI,OAAO,mBAAmB;AAAA,MAAA;AAElC,aAAA,WAAW,SAAS,OAAO;AAAA,IAAA;AAGpC,UAAM,gCAAgC,MAAM;AACpC,YAAA,WAAW,mBAAmB,MAAM;AAAA,QACxC,CAAC,QAAQ,IAAI,OAAO,mBAAmB;AAAA,MAAA;AAElC,aAAA,WAAW,SAAS,cAAc;AAAA,IAAA;AAGrC,UAAA,mBAAmB,CAAC,eAAe;AACvC,aAAO,aAAa,MAAM;AAAA,QACxB,CAAC,QACC,IAAI,cAAc,kBAAkB,SAAS,IAAI,aAAa;AAAA,MAChE,EAAA;AAAA,IAAA;AAGE,UAAA,uBAAuB,CAAC,SAAS;AACrC,cAAQ,MAAM;AAAA,QACZ,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IAAA;AAGI,UAAA,kBAAkB,CAAC,SAAS;AAChC,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,QAEN,KAAK;AACH,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,QAEN,KAAK;AACH,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,QAEN,KAAK;AACH,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,QAEN,KAAK;AACH,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,QAEN;AACE,iBAAO,MACL;AAAA,YACE;AAAA,YACA,EAAE,MAAM,QAAQ,QAAQ,gBAAgB,SAAS,YAAY;AAAA,YAC7D;AAAA,cACE,EAAE,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,mBAAmB;AAAA,gBACnB,gBAAgB;AAAA,gBAChB,GAAG;AAAA,cAAA,CACJ;AAAA,YACH;AAAA,UAAA;AAAA,MAER;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}