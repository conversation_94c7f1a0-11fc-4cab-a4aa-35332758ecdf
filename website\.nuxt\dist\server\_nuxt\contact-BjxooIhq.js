import { _ as __nuxt_component_0 } from "./nuxt-link-2X8I7ISh.js";
import { ref, mergeProps, withCtx, createTextVNode, openBlock, createBlock, createVNode, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrInterpolate } from "vue/server-renderer";
import { publicAssetsURL } from "#internal/nuxt/paths";
import { _ as _sfc_main$1, a as _sfc_main$2 } from "./Footer-C3PwX65Z.js";
import { _ as _export_sfc } from "../server.mjs";
import "ufo";
import "ofetch";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "@unhead/shared";
import "vue-router";
import "radix3";
import "defu";
import "devalue";
const _imports_0 = publicAssetsURL("/images/wechat-qrcode.jpg");
const _sfc_main = {
  __name: "contact",
  __ssrInlineRender: true,
  setup(__props) {
    const form = ref({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: ""
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-37ff425d>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-37ff425d><div class="bg-white border-b border-gray-200" data-v-37ff425d><div class="container mx-auto px-4 py-4" data-v-37ff425d><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-37ff425d>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`首页`);
          } else {
            return [
              createTextVNode("首页")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d></path></svg><span class="text-gray-900 font-medium" data-v-37ff425d>联系我们</span></nav></div></div><div class="bg-white py-12" data-v-37ff425d><div class="container mx-auto px-4" data-v-37ff425d><div class="max-w-3xl" data-v-37ff425d><h1 class="heading-primary mb-4" data-v-37ff425d>联系我们</h1><p class="text-xl text-gray-600 mb-6" data-v-37ff425d> 我们随时准备为您提供专业的技术支持和服务咨询 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-37ff425d><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>24小时内回复</span></div><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>专业技术团队</span></div><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>免费咨询服务</span></div></div></div></div></div><div class="bg-orange-50 py-8" data-v-37ff425d><div class="container mx-auto px-4" data-v-37ff425d><div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-v-37ff425d><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>电话咨询</h3><p class="text-orange-600 font-semibold text-lg" data-v-37ff425d>155-1196-5595</p><p class="text-sm text-gray-500" data-v-37ff425d>工作日 9:00-18:00</p></div><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>邮件联系</h3><p class="text-orange-600 font-semibold" data-v-37ff425d><EMAIL></p><p class="text-sm text-gray-500" data-v-37ff425d>24小时内回复</p></div><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>微信咨询</h3><p class="text-orange-600 font-semibold" data-v-37ff425d>20155031</p><p class="text-sm text-gray-500" data-v-37ff425d>扫码添加微信</p></div></div></div></div><div class="container mx-auto px-4 py-12" data-v-37ff425d><div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-v-37ff425d><div class="card-business p-8" data-v-37ff425d><div class="mb-6" data-v-37ff425d><h2 class="heading-secondary mb-2" data-v-37ff425d>发送消息</h2><p class="text-gray-600" data-v-37ff425d>请填写以下信息，我们会尽快与您联系</p></div><form class="space-y-6" data-v-37ff425d><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-37ff425d><div data-v-37ff425d><label for="name" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> 姓名 <span class="text-red-500" data-v-37ff425d>*</span></label><input type="text" id="name"${ssrRenderAttr("value", form.value.name)} required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="请输入您的姓名" data-v-37ff425d></div><div data-v-37ff425d><label for="phone" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> 电话 <span class="text-red-500" data-v-37ff425d>*</span></label><input type="tel" id="phone"${ssrRenderAttr("value", form.value.phone)} required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="请输入您的电话" data-v-37ff425d></div></div><div data-v-37ff425d><label for="email" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> 邮箱 </label><input type="email" id="email"${ssrRenderAttr("value", form.value.email)} class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="请输入您的邮箱" data-v-37ff425d></div><div data-v-37ff425d><label for="subject" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> 咨询类型 <span class="text-red-500" data-v-37ff425d>*</span></label><select id="subject" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" data-v-37ff425d><option value="" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "") : ssrLooseEqual(form.value.subject, "")) ? " selected" : ""}>请选择咨询类型</option><option value="产品咨询" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "产品咨询") : ssrLooseEqual(form.value.subject, "产品咨询")) ? " selected" : ""}>产品功能咨询</option><option value="技术支持" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "技术支持") : ssrLooseEqual(form.value.subject, "技术支持")) ? " selected" : ""}>技术支持</option><option value="商务合作" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "商务合作") : ssrLooseEqual(form.value.subject, "商务合作")) ? " selected" : ""}>商务合作</option><option value="售前咨询" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "售前咨询") : ssrLooseEqual(form.value.subject, "售前咨询")) ? " selected" : ""}>售前咨询</option><option value="其他问题" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "其他问题") : ssrLooseEqual(form.value.subject, "其他问题")) ? " selected" : ""}>其他问题</option></select></div><div data-v-37ff425d><label for="message" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> 详细描述 <span class="text-red-500" data-v-37ff425d>*</span></label><textarea id="message" required rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors resize-none" placeholder="请详细描述您的问题或需求，我们会根据您的描述提供更精准的服务" data-v-37ff425d>${ssrInterpolate(form.value.message)}</textarea></div><div class="pt-4" data-v-37ff425d><button type="submit" class="btn-primary w-full py-4 text-lg" data-v-37ff425d> 提交咨询 </button><p class="text-sm text-gray-500 mt-3 text-center" data-v-37ff425d> 提交后我们会在24小时内与您联系 </p></div></form></div><div class="space-y-6" data-v-37ff425d><div class="card-business p-6" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>联系方式详情</h3><div class="space-y-4" data-v-37ff425d><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>销售热线</h4><p class="text-orange-600 font-semibold" data-v-37ff425d>155-1196-5595</p><p class="text-sm text-gray-500" data-v-37ff425d>工作日 9:00-18:00</p></div></div><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>商务邮箱</h4><p class="text-orange-600 font-semibold" data-v-37ff425d><EMAIL></p><p class="text-sm text-gray-500" data-v-37ff425d>24小时内回复</p></div></div><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-37ff425d></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>公司地址</h4><p class="text-gray-700" data-v-37ff425d>广东省广州市天河区</p><p class="text-sm text-gray-500" data-v-37ff425d>可预约上门服务</p></div></div></div></div><div class="card-business p-6" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>服务时间</h3><div class="space-y-3" data-v-37ff425d><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>销售咨询</span><span class="font-semibold text-gray-900" data-v-37ff425d>周一至周五 9:00-18:00</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>技术支持</span><span class="font-semibold text-gray-900" data-v-37ff425d>7×24小时</span></div><div class="flex justify-between items-center py-2" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>邮件回复</span><span class="font-semibold text-gray-900" data-v-37ff425d>24小时内</span></div></div></div><div class="card-business p-6 text-center" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>微信咨询</h3><div class="inline-block bg-gray-50 p-4 rounded-lg" data-v-37ff425d><img${ssrRenderAttr("src", _imports_0)} alt="微信二维码" class="w-32 h-32 mx-auto" data-v-37ff425d><p class="text-sm text-gray-600 mt-2" data-v-37ff425d>扫码添加微信</p><p class="text-xs text-gray-500" data-v-37ff425d>微信号：20155031</p></div></div></div></div></div><div class="container mx-auto px-4 py-12" data-v-37ff425d><div class="card-business p-8" data-v-37ff425d><div class="text-center mb-12" data-v-37ff425d><h2 class="heading-secondary mb-4" data-v-37ff425d>常见问题解答</h2><p class="text-gray-600" data-v-37ff425d>快速找到您关心的问题答案</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8" data-v-37ff425d><div class="space-y-6" data-v-37ff425d><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> 产品功能咨询 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> 我们的产品支持多种扫描设备和图像处理功能，兼容Windows和Linux平台。如需了解具体功能特性，请查看产品详情页面或联系我们的技术顾问。 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/products",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 查看产品详情 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" 查看产品详情 "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> 技术支持服务 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> 我们提供7×24小时技术支持服务，包括远程协助、现场服务等。技术团队具备丰富的项目经验，能够快速解决各种技术问题。 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 查看技术文档 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" 查看技术文档 "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="space-y-6" data-v-37ff425d><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> 购买和授权 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> 我们提供灵活的授权方案和多种支付方式，支持企业采购流程。所有产品均提供正规发票和软件著作权授权文件。 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/purchase",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` 了解购买流程 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" 了解购买流程 "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> 合作与定制 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> 我们欢迎各类企业合作，提供产品定制、技术咨询、解决方案设计等服务。可根据您的具体需求提供专业的定制化方案。 </p><div class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center cursor-pointer" data-v-37ff425d> 联系商务合作 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d></path></svg></div></div></div></div><div class="mt-12 p-6 bg-orange-50 rounded-lg border border-orange-200" data-v-37ff425d><div class="text-center" data-v-37ff425d><h3 class="text-lg font-semibold text-gray-900 mb-2" data-v-37ff425d> 没有找到您要的答案？ </h3><p class="text-gray-600 mb-4" data-v-37ff425d>我们的专业团队随时为您提供帮助</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-37ff425d><a href="tel:155-1196-5595" class="btn-primary inline-flex items-center justify-center" data-v-37ff425d><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg> 立即致电 </a><button class="btn-secondary inline-flex items-center justify-center" data-v-37ff425d><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg> 在线咨询 </button></div></div></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/contact.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const contact = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-37ff425d"]]);
export {
  contact as default
};
//# sourceMappingURL=contact-BjxooIhq.js.map
