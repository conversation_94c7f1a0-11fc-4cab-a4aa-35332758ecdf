import { ref, computed, mergeProps, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssr<PERSON><PERSON><PERSON>tt<PERSON>, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderAttr } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import './nuxt-link-2X8I7ISh.mjs';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import './server.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import '../_/renderer.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const _sfc_main = {
  __name: "download",
  __ssrInlineRender: true,
  setup(__props) {
    const products = ref([
      {
        id: 1,
        name: "ScanOnWeb",
        shortDesc: "\u626B\u63CF\u4EEA\u63A7\u4EF6",
        version: "3.5.0",
        baseUrl: "/downloads/scanonweb"
      },
      {
        id: 2,
        name: "ImageCapOnWeb",
        shortDesc: "\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u63A7\u4EF6",
        version: "2.8.0",
        baseUrl: "/downloads/imagecaponweb"
      },
      {
        id: 3,
        name: "GaoPaiYi",
        shortDesc: "\u9AD8\u62CD\u4EEA\u63A7\u4EF6",
        version: "2.0.1",
        baseUrl: "/downloads/gaopaiyi"
      }
    ]);
    const operatingSystems = ref([
      { id: "windows", name: "Windows" },
      { id: "linux", name: "Linux" }
    ]);
    const cpuArchitectures = ref([
      { id: "x64", name: "x64", description: "\u901A\u7528x86-64\u67B6\u6784" },
      { id: "phytium", name: "\u98DE\u817E", description: "FT-2000/4\u7B49\u7CFB\u5217" },
      { id: "loongson", name: "\u9F99\u82AF", description: "\u9F99\u82AF3A4000\u7B49\u7CFB\u5217" },
      { id: "kunpeng", name: "\u9CB2\u9E4F", description: "\u534E\u4E3A\u9CB2\u9E4F920\u7B49\u7CFB\u5217" }
    ]);
    const selectedProduct = ref(null);
    const selectedOS = ref(null);
    const selectedArch = ref(null);
    const downloadReady = computed(() => {
      if (!selectedProduct.value)
        return false;
      if (!selectedOS.value)
        return false;
      if (selectedOS.value.id === "linux" && !selectedArch.value)
        return false;
      return true;
    });
    const historyVersions = ref([
      {
        productName: "ScanOnWeb",
        version: "3.4.0",
        platform: "Windows",
        date: "2023-12-15",
        downloadUrl: "/downloads/scanonweb/windows/ScanOnWeb_3.4.0_Setup.exe"
      },
      {
        productName: "ScanOnWeb",
        version: "3.3.5",
        platform: "Linux (x64)",
        date: "2023-10-20",
        downloadUrl: "/downloads/scanonweb/linux/x64/ScanOnWeb_3.3.5.tar.gz"
      },
      {
        productName: "ImageCapOnWeb",
        version: "2.7.0",
        platform: "Windows",
        date: "2023-11-05",
        downloadUrl: "/downloads/imagecaponweb/windows/ImageCapOnWeb_2.7.0_Setup.exe"
      },
      {
        productName: "GaoPaiYi",
        version: "1.9.2",
        platform: "Windows",
        date: "2023-09-30",
        downloadUrl: "/downloads/gaopaiyi/windows/GaoPaiYi_1.9.2_Setup.exe"
      }
    ]);
    const getVersionText = () => {
      if (!selectedProduct.value)
        return "";
      let versionText = selectedProduct.value.version;
      if (selectedOS.value) {
        versionText += ` for ${selectedOS.value.name}`;
        if (selectedOS.value.id === "linux" && selectedArch.value) {
          versionText += ` (${selectedArch.value.name})`;
        }
      }
      return versionText;
    };
    const getFileSize = () => {
      return "15.8 MB";
    };
    const getInstallInstructions = () => {
      if (!selectedProduct.value || !selectedOS.value)
        return "";
      if (selectedOS.value.id === "windows") {
        return "\u4E0B\u8F7D\u540E\u53CC\u51FB\u5B89\u88C5\u5305\u8FD0\u884C\uFF0C\u6309\u7167\u5B89\u88C5\u5411\u5BFC\u5B8C\u6210\u5B89\u88C5\u3002\u5B89\u88C5\u5B8C\u6210\u540E\uFF0C\u91CD\u542F\u6D4F\u89C8\u5668\u5373\u53EF\u4F7F\u7528\u3002";
      } else {
        return "\u4E0B\u8F7D\u540E\u89E3\u538B\u6587\u4EF6\uFF0C\u8FDB\u5165\u89E3\u538B\u76EE\u5F55\uFF0C\u8FD0\u884C ./install.sh \u811A\u672C\u8FDB\u884C\u5B89\u88C5\u3002\u5B89\u88C5\u5B8C\u6210\u540E\uFF0C\u91CD\u542F\u6D4F\u89C8\u5668\u5373\u53EF\u4F7F\u7528\u3002";
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))}>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main class="container mx-auto py-12 px-4"><div class="text-center mb-12"><h1 class="heading-primary mb-4">\u4EA7\u54C1\u4E0B\u8F7D\u4E2D\u5FC3</h1><p class="text-xl text-gray-600">\u9009\u62E9\u9002\u5408\u60A8\u7CFB\u7EDF\u7684\u7248\u672C\uFF0C\u5F00\u59CB\u514D\u8D39\u8BD5\u7528</p></div><div class="card-business p-8 mb-8"><div class="mb-8"><h2 class="heading-secondary mb-6">\u9009\u62E9\u4EA7\u54C1</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-6"><!--[-->`);
      ssrRenderList(products.value, (product) => {
        var _a;
        _push(`<button class="${ssrRenderClass([
          "p-6 rounded-lg border-2 transition-all duration-200 text-left",
          ((_a = selectedProduct.value) == null ? void 0 : _a.id) === product.id ? "border-orange-500 bg-orange-50 shadow-md" : "border-gray-200 hover:border-orange-300 hover:shadow-sm"
        ])}"><div class="flex items-center mb-3"><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3"><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="font-bold text-lg text-gray-900">${ssrInterpolate(product.name)}</h3></div><p class="text-gray-600 mb-3">${ssrInterpolate(product.shortDesc)}</p><div class="text-sm text-gray-500"><span class="inline-block bg-gray-100 px-2 py-1 rounded">${ssrInterpolate(product.version)}</span></div></button>`);
      });
      _push(`<!--]--></div></div>`);
      if (selectedProduct.value) {
        _push(`<div class="mb-8"><h2 class="heading-secondary mb-6">\u9009\u62E9\u64CD\u4F5C\u7CFB\u7EDF</h2><div class="grid grid-cols-1 sm:grid-cols-2 gap-6"><!--[-->`);
        ssrRenderList(operatingSystems.value, (os) => {
          var _a;
          _push(`<button class="${ssrRenderClass([
            "p-6 rounded-lg border-2 transition-all duration-200 text-left",
            ((_a = selectedOS.value) == null ? void 0 : _a.id) === os.id ? "border-orange-500 bg-orange-50 shadow-md" : "border-gray-200 hover:border-orange-300 hover:shadow-sm"
          ])}"><div class="flex items-center"><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">`);
          if (os.id === "windows") {
            _push(`<svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24"><path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"></path></svg>`);
          } else {
            _push(`<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path></svg>`);
          }
          _push(`</div><div><h3 class="font-bold text-lg text-gray-900">${ssrInterpolate(os.name)}</h3><p class="text-sm text-gray-500">${ssrInterpolate(os.id === "windows" ? "\u652F\u6301 Windows 7/8/10/11" : "\u652F\u6301\u4E3B\u6D41Linux\u53D1\u884C\u7248")}</p></div></div></button>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (selectedOS.value && selectedOS.value.id === "linux") {
        _push(`<div class="mb-8"><h2 class="heading-secondary mb-6">\u9009\u62E9CPU\u67B6\u6784</h2><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"><!--[-->`);
        ssrRenderList(cpuArchitectures.value, (arch) => {
          var _a;
          _push(`<button class="${ssrRenderClass([
            "p-4 rounded-lg border-2 transition-all duration-200 text-left",
            ((_a = selectedArch.value) == null ? void 0 : _a.id) === arch.id ? "border-orange-500 bg-orange-50 shadow-md" : "border-gray-200 hover:border-orange-300 hover:shadow-sm"
          ])}"><div class="text-center"><div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2"><svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path></svg></div><h3 class="font-bold text-gray-900 mb-1">${ssrInterpolate(arch.name)}</h3><p class="text-xs text-gray-500">${ssrInterpolate(arch.description)}</p></div></button>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (downloadReady.value) {
        _push(`<div class="mt-8"><div class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-8 text-center border border-orange-200"><h3 class="text-2xl font-bold text-gray-900 mb-4">\u51C6\u5907\u4E0B\u8F7D</h3><div class="mb-6"><button class="btn-primary text-xl px-10 py-4 inline-flex items-center shadow-lg"><svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> \u4E0B\u8F7D ${ssrInterpolate(selectedProduct.value.name)} ${ssrInterpolate(getVersionText())}</button></div><div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-6"><div class="flex items-center justify-center"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> \u7248\u672C: ${ssrInterpolate(getVersionText())}</div><div class="flex items-center justify-center"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg> \u6587\u4EF6\u5927\u5C0F: ${ssrInterpolate(getFileSize())}</div><div class="flex items-center justify-center"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg> \u5B89\u5168\u65E0\u6BD2 </div></div><div class="feature-highlight text-left"><h4 class="font-bold text-orange-700 mb-2">\u5B89\u88C5\u8BF4\u660E</h4><p class="text-orange-600">${ssrInterpolate(getInstallInstructions())}</p></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="card-business p-8"><h2 class="heading-secondary mb-6">\u5386\u53F2\u7248\u672C\u4E0B\u8F7D</h2><div class="overflow-x-auto"><table class="min-w-full border-collapse"><thead><tr class="bg-gray-50"><th class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"> \u4EA7\u54C1\u540D\u79F0 </th><th class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"> \u7248\u672C\u53F7 </th><th class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"> \u652F\u6301\u5E73\u53F0 </th><th class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"> \u53D1\u5E03\u65E5\u671F </th><th class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"> \u64CD\u4F5C </th></tr></thead><tbody><!--[-->`);
      ssrRenderList(historyVersions.value, (version, index) => {
        _push(`<tr class="${ssrRenderClass(index % 2 === 0 ? "bg-white" : "bg-gray-50")}"><td class="border-b border-gray-200 py-4 px-6 font-medium text-gray-900">${ssrInterpolate(version.productName)}</td><td class="border-b border-gray-200 py-4 px-6 text-gray-700"><span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${ssrInterpolate(version.version)}</span></td><td class="border-b border-gray-200 py-4 px-6 text-gray-700">${ssrInterpolate(version.platform)}</td><td class="border-b border-gray-200 py-4 px-6 text-gray-700">${ssrInterpolate(version.date)}</td><td class="border-b border-gray-200 py-4 px-6"><a${ssrRenderAttr("href", version.downloadUrl)} class="btn-outline text-sm px-4 py-2 inline-flex items-center"><svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> \u4E0B\u8F7D </a></td></tr>`);
      });
      _push(`<!--]--></tbody></table></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/download.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=download-wiBYTEPb.mjs.map
