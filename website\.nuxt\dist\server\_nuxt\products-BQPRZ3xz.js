import { mergeProps, useSSRContext, withCtx, openBlock, createBlock, createVNode, createTextVNode, ref } from "vue";
import { ssrRenderAttrs, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderComponent, ssrRenderAttr } from "vue/server-renderer";
import { _ as _sfc_main$3, a as _sfc_main$4 } from "./Footer-C3PwX65Z.js";
import { _ as __nuxt_component_0 } from "./nuxt-link-2X8I7ISh.js";
import { _ as _export_sfc } from "../server.mjs";
import "#internal/nuxt/paths";
import "ufo";
import "ofetch";
import "hookable";
import "unctx";
import "h3";
import "unhead";
import "@unhead/shared";
import "vue-router";
import "radix3";
import "defu";
import "devalue";
const _sfc_main$2 = {
  __name: "ProductSidebar",
  __ssrInlineRender: true,
  props: {
    products: Array,
    selectedProductId: Number
  },
  emits: ["select"],
  setup(__props) {
    const getShortDescription = (description) => {
      return description.length > 50 ? description.substring(0, 50) + "..." : description;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-white shadow-lg rounded-lg overflow-hidden" }, _attrs))}><div class="bg-blue-600 text-white p-4"><h2 class="text-2xl font-bold">产品列表</h2></div><ul><!--[-->`);
      ssrRenderList(__props.products, (product) => {
        _push(`<li class="border-b last:border-b-0"><button class="${ssrRenderClass([{
          "bg-blue-100": product.id === __props.selectedProductId,
          "hover:bg-gray-100": product.id !== __props.selectedProductId
        }, "w-full text-left p-4 flex items-center transition-colors"])}"><div class="${ssrRenderClass([{
          "bg-gradient-to-r from-blue-500 to-indigo-600": product.id === 1,
          "bg-gradient-to-r from-purple-500 to-pink-600": product.id === 2,
          "bg-gradient-to-r from-green-500 to-teal-600": product.id === 3
        }, "w-12 h-12 rounded-md mr-4 flex items-center justify-center text-white font-bold text-xl"])}">${ssrInterpolate(product.name.charAt(0))}</div><div><p class="font-semibold text-gray-800">${ssrInterpolate(product.name)}</p><p class="text-xs text-gray-500 mt-1">${ssrInterpolate(getShortDescription(product.description))}</p></div></button></li>`);
      });
      _push(`<!--]--></ul></div>`);
    };
  }
};
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ProductSidebar.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = {
  __name: "ProductDetails",
  __ssrInlineRender: true,
  props: {
    product: Object
  },
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      if (__props.product) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "card-business overflow-hidden" }, _attrs))}><div class="bg-business-gradient p-8"><div class="flex items-center justify-between"><div><h1 class="text-2xl md:text-3xl font-bold text-white mb-2">${ssrInterpolate(__props.product.name)}</h1><p class="text-blue-100">企业级Web控件解决方案</p></div><div class="text-right text-white"><div class="text-sm text-blue-100">软件著作权</div><div class="font-semibold">2013SR145420</div></div></div></div><div class="p-8"><div class="mb-8"><p class="text-business text-lg leading-relaxed">${ssrInterpolate(__props.product.description)}</p></div><div class="mb-8 p-6 bg-orange-50 rounded-lg border border-orange-200"><div class="text-center"><h3 class="text-xl font-bold text-gray-900 mb-2">立即开始免费试用</h3><p class="text-gray-600 mb-4">无需注册，支持多平台，专业技术支持</p>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/download",
          class: "btn-primary text-lg px-8 py-4 inline-flex items-center"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"${_scopeId}></path></svg> 免费下载试用版 `);
            } else {
              return [
                (openBlock(), createBlock("svg", {
                  class: "w-5 h-5 mr-2",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24"
                }, [
                  createVNode("path", {
                    "stroke-linecap": "round",
                    "stroke-linejoin": "round",
                    "stroke-width": "2",
                    d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  })
                ])),
                createTextVNode(" 免费下载试用版 ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div><div class="mb-8"><h3 class="heading-secondary mb-6">核心功能特性</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><!--[-->`);
        ssrRenderList(__props.product.features, (feature) => {
          _push(`<div class="flex items-start p-4 bg-gray-50 rounded-lg"><div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0"><svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><span class="text-gray-700 font-medium">${ssrInterpolate(feature)}</span></div>`);
        });
        _push(`<!--]--></div></div>`);
        if (__props.product.tableData) {
          _push(`<div class="mb-8"><h3 class="heading-secondary mb-6">技术规格详情</h3><div class="overflow-x-auto"><table class="min-w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm"><thead><tr class="bg-gray-50"><th class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"> 特性分类 </th><th class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"> 技术参数 </th></tr></thead><tbody><!--[-->`);
          ssrRenderList(__props.product.tableData, (item, index) => {
            _push(`<tr class="${ssrRenderClass(index % 2 === 0 ? "bg-white" : "bg-gray-50")}"><td class="border-b border-gray-200 px-6 py-4 font-medium text-gray-900 w-1/4">${ssrInterpolate(item.category)}</td><td class="border-b border-gray-200 px-6 py-4 text-gray-700 whitespace-pre-line">${ssrInterpolate(item.value)}</td></tr>`);
          });
          _push(`<!--]--></tbody></table></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"><div class="card-business p-6"><h3 class="heading-tertiary mb-4">基础技术规格</h3><div class="space-y-3"><!--[-->`);
        ssrRenderList(__props.product.specs, (value, key) => {
          _push(`<div class="flex justify-between py-2 border-b border-gray-100 last:border-b-0"><span class="font-medium text-gray-700">${ssrInterpolate(key)}</span><span class="text-gray-600 text-right">${ssrInterpolate(value)}</span></div>`);
        });
        _push(`<!--]--></div></div><div class="card-business p-6"><h3 class="heading-tertiary mb-4">典型应用场景</h3><div class="grid grid-cols-2 gap-3"><!--[-->`);
        ssrRenderList(__props.product.useCases, (useCase) => {
          _push(`<div class="flex items-center p-3 bg-orange-50 rounded-lg"><svg class="w-5 h-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg><span class="text-gray-700 font-medium">${ssrInterpolate(useCase)}</span></div>`);
        });
        _push(`<!--]--></div></div></div><div class="feature-highlight mb-8"><div class="flex items-center justify-between"><div><h3 class="heading-tertiary text-orange-700 mb-2"> 企业级定价方案 </h3><p class="text-orange-600">${ssrInterpolate(__props.product.pricing)}</p></div><div class="text-right"><div class="text-sm text-orange-600">联系销售获取</div><div class="font-bold text-orange-700">专属优惠价格</div></div></div></div><div class="flex flex-col sm:flex-row gap-4"><a${ssrRenderAttr("href", __props.product.videoUrl)} target="_blank" class="btn-secondary flex-1 text-center inline-flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> 观看产品演示 </a><a${ssrRenderAttr("href", __props.product.pdfUrl)} target="_blank" class="btn-secondary flex-1 text-center inline-flex items-center justify-center"><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> 下载产品资料 </a></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/ProductDetails.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const _sfc_main = {
  __name: "products",
  __ssrInlineRender: true,
  setup(__props) {
    const products2 = ref([
      {
        id: 1,
        name: "ScanOnWeb",
        description: "ScanOnWeb控件(软件著作权登记号 2013SR145420，证书号0651182)用于处理图像扫描编程，适合用于web环境下的扫描仪编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容目前主流的数款扫描设备，对于个别非按标准协议支持的扫描设备亦提供了集成支持，目前控件经过多年的发展已经很成熟稳定，被广泛的应用于办公OA、电子政务、纸质文档电子化等应用场景，客户单位遍布税务、公安、建筑、银行等多个行业，是目前国内唯一成熟稳定的扫描控件产品。",
        image: "/images/slide1.png",
        features: [
          "扫描设备选择",
          "自动进纸器连续多页扫描",
          "双面扫描模式",
          "自动纠偏模式",
          "多种图像扫描模式",
          "多种分辨率设置"
        ],
        specs: {
          浏览器兼容性: "所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等",
          图像扫描协议: "支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备，支持WIA协议的所有图像采集设备",
          图像编辑特性: "支持旋转、裁剪、填白、马赛克等多种处理",
          图像上传能力: "支持多种格式上传到指定URL"
        },
        useCases: [
          "办公OA",
          "电子政务",
          "纸质文档电子化",
          "税务系统",
          "公安系统",
          "银行业务"
        ],
        pricing: "请下载报价单了解详情",
        videoUrl: "https://example.com/scanonweb-demo.mp4",
        pdfUrl: "/ScanOnWeb-quotation.pdf",
        tableData: [
          {
            category: "浏览器兼容性",
            value: "所有支持websocket的现代浏览器,包括：chrome、edge、firefox、IE11等"
          },
          {
            category: "图像扫描协议",
            value: "1.支持兼容twain1.9及以上协议的所有扫描仪或其他图像采集设备2.支持WIA协议的所有图像采集设备"
          },
          {
            category: "图像扫描参数控制",
            value: "1. 扫描设备选择 2. 是否显示扫描仪驱动程序内置设置对话框 3. 是否使用自动进纸器进行连续多页扫描 4. 是否使用自动装填纸张模式 5. 是否使用双面扫描模式 6. 是否使用自动纠偏模式 7. 是否使用自动边框检测模式 8. 图像扫描模式：黑白模式、灰度模式、彩色模式 9. dpi分辨率设置 10. 扫描结果传输模式：内存、文件、原生"
          },
          {
            category: "图像编辑特性",
            value: "1. 支持图像向左、向右旋转 90 度 2. 支持图像自定义角度旋转 3. 支持魔术棒选择模式图像选择 4. 支持矩形图像选择 5. 支持选中区域填白处理 6. 支持选中区域反选填白处理 7. 支持选中区域马赛克处理 8. 支持裁剪选区以外所有图像处理 9. 支持选中区域归红、归绿处理 10. 支持去除图像黑边处理 11. 支持去除图像底色处理 12. UNDO 撤销操作 13. 客户端单页本地图像保存 14. 客户端单页本地图像打印及打印预览 15. 批量图像删除 16. 多页扫描结果排序（直接拖拽顺序） 17. 扫描图像删除单页处理 18. 客户端多页图像保存 19. 客户端多页图像打印机打印预览 20. 鼠标滚轮缩放图像"
          },
          {
            category: "图像上传能力",
            value: "1. 图像转jpg base64编码，供前端显示或上传 2. 图像按照 tiff 格式上传到指定 url 3. 图像按照 pdf 格式上传到指定 url 4. 图像按照多页 jpg 方式上传到指定 url 5. 单独将某一页图像以 jpg 方式上传到指定 url 6. 上传到指定的 sftp 地址（需定制）"
          }
        ]
      },
      {
        id: 2,
        name: "ImageCapOnWeb",
        description: "ImageCapOnWeb控件用于处理摄像头图像采集编程，适合用于web环境下的图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的大部分摄像头数码设备。目前已经在多所学校、银行、政府机构中进行集成应用。",
        image: "/images/slide2.png",
        features: [
          "摄像头图像采集",
          "Web环境下的图像采集编程",
          "兼容Windows平台下的大部分摄像头设备",
          "无缝集成到多种编程技术"
        ],
        specs: {
          兼容平台: "Windows平台",
          支持设备: "大部分摄像头数码设备",
          集成技术: "jsp、php、asp.net等"
        },
        useCases: ["学校信息系统", "银行业务系统", "政府机构应用"],
        pricing: "请下载报价单了解详情",
        videoUrl: "https://example.com/imagecaponweb-demo.mp4",
        pdfUrl: "/ImageCapOnWeb-quotation.pdf"
      },
      {
        id: 3,
        name: "GaoPaiYi",
        description: "GaoPaiYi控件用于处理高拍仪图像采集编程，适合用于web环境下的高拍仪图像采集编程应用，可无缝集成到jsp、php、asp.net等编程技术当中。控件兼容Windows平台下的以摄像头为核心设备的高拍仪产品。",
        image: "/images/slide3.png",
        features: [
          "高拍仪图像采集",
          "Web环境下的编程应用",
          "兼容Windows平台下的高拍仪产品",
          "无缝集成到各种编程技术"
        ],
        specs: {
          兼容平台: "Windows平台",
          支持设备: "以摄像头为核心设备的高拍仪产品",
          集成技术: "jsp、php、asp.net等"
        },
        useCases: ["文档扫描", "证件采集", "教育培训"],
        pricing: "请下载报价单了解详情",
        videoUrl: "https://example.com/gaopaiyi-demo.mp4",
        pdfUrl: "/GaoPaiYi-quotation.pdf"
      }
    ]);
    const selectedProduct = ref(products2.value[0] || null);
    const selectProduct = (product) => {
      selectedProduct.value = product;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-100 min-h-screen" }, _attrs))} data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$3, null, null, _parent));
      _push(`<main class="container mx-auto py-12 px-4" data-v-9e2630bc><div class="flex flex-col lg:flex-row gap-8" data-v-9e2630bc><div class="lg:w-1/4" data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$2, {
        products: products2.value,
        selectedProductId: (_a = selectedProduct.value) == null ? void 0 : _a.id,
        onSelect: selectProduct
      }, null, _parent));
      _push(`</div><div class="lg:w-3/4" data-v-9e2630bc>`);
      _push(ssrRenderComponent(_sfc_main$1, {
        key: (_b = selectedProduct.value) == null ? void 0 : _b.id,
        product: selectedProduct.value
      }, null, _parent));
      _push(`</div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$4, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/products.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const products = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-9e2630bc"]]);
export {
  products as default
};
//# sourceMappingURL=products-BQPRZ3xz.js.map
