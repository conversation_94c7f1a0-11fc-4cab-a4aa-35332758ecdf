<template>
  <div class="bg-gray-50 min-h-screen">
    <Header />
    <main class="container mx-auto py-12 px-4">
      <div class="text-center mb-12">
        <h1 class="heading-primary mb-4">产品下载中心</h1>
        <p class="text-xl text-gray-600">选择适合您系统的版本，开始免费试用</p>
      </div>

      <div class="card-business p-8 mb-8">
        <div class="mb-8">
          <h2 class="heading-secondary mb-6">选择产品</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button
              v-for="product in products"
              :key="product.id"
              @click="selectProduct(product)"
              :class="[
                'p-6 rounded-lg border-2 transition-all duration-200 text-left',
                selectedProduct?.id === product.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',
              ]"
            >
              <div class="flex items-center mb-3">
                <div
                  class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3"
                >
                  <svg
                    class="w-5 h-5 text-orange-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                  </svg>
                </div>
                <h3 class="font-bold text-lg text-gray-900">
                  {{ product.name }}
                </h3>
              </div>
              <p class="text-gray-600 mb-3">{{ product.shortDesc }}</p>
              <div class="text-sm text-gray-500">
                <span class="inline-block bg-gray-100 px-2 py-1 rounded">{{
                  product.version
                }}</span>
              </div>
            </button>
          </div>
        </div>

        <div v-if="selectedProduct" class="mb-8">
          <h2 class="heading-secondary mb-6">选择操作系统</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <button
              v-for="os in operatingSystems"
              :key="os.id"
              @click="selectOS(os)"
              :class="[
                'p-6 rounded-lg border-2 transition-all duration-200 text-left',
                selectedOS?.id === os.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',
              ]"
            >
              <div class="flex items-center">
                <div
                  class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4"
                >
                  <svg
                    v-if="os.id === 'windows'"
                    class="w-6 h-6 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-13.051-1.351"
                    />
                  </svg>
                  <svg
                    v-else
                    class="w-6 h-6 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="font-bold text-lg text-gray-900">{{ os.name }}</h3>
                  <p class="text-sm text-gray-500">
                    {{
                      os.id === "windows"
                        ? "支持 Windows 7/8/10/11"
                        : "支持主流Linux发行版"
                    }}
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <div v-if="selectedOS && selectedOS.id === 'linux'" class="mb-8">
          <h2 class="heading-secondary mb-6">选择CPU架构</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              v-for="arch in cpuArchitectures"
              :key="arch.id"
              @click="selectArch(arch)"
              :class="[
                'p-4 rounded-lg border-2 transition-all duration-200 text-left',
                selectedArch?.id === arch.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-orange-300 hover:shadow-sm',
              ]"
            >
              <div class="text-center">
                <div
                  class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2"
                >
                  <svg
                    class="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
                    ></path>
                  </svg>
                </div>
                <h3 class="font-bold text-gray-900 mb-1">{{ arch.name }}</h3>
                <p class="text-xs text-gray-500">{{ arch.description }}</p>
              </div>
            </button>
          </div>
        </div>

        <div v-if="downloadReady" class="mt-8">
          <div
            class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-8 text-center border border-orange-200"
          >
            <h3 class="text-2xl font-bold text-gray-900 mb-4">准备下载</h3>
            <div class="mb-6">
              <button
                @click="handleDownloadClick"
                class="btn-primary text-xl px-10 py-4 inline-flex items-center shadow-lg"
              >
                <svg
                  class="w-6 h-6 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  ></path>
                </svg>
                下载 {{ selectedProduct.name }} {{ getVersionText() }}
              </button>
            </div>

            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-6"
            >
              <div class="flex items-center justify-center">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                版本: {{ getVersionText() }}
              </div>
              <div class="flex items-center justify-center">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  ></path>
                </svg>
                文件大小: {{ getFileSize() }}
              </div>
              <div class="flex items-center justify-center">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  ></path>
                </svg>
                安全无毒
              </div>
            </div>

            <div class="feature-highlight text-left">
              <h4 class="font-bold text-orange-700 mb-2">安装说明</h4>
              <p class="text-orange-600">{{ getInstallInstructions() }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card-business p-8">
        <h2 class="heading-secondary mb-6">历史版本下载</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full border-collapse">
            <thead>
              <tr class="bg-gray-50">
                <th
                  class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"
                >
                  产品名称
                </th>
                <th
                  class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"
                >
                  版本号
                </th>
                <th
                  class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"
                >
                  支持平台
                </th>
                <th
                  class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"
                >
                  发布日期
                </th>
                <th
                  class="border-b border-gray-200 py-4 px-6 text-left font-semibold text-gray-900"
                >
                  操作
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(version, index) in historyVersions"
                :key="index"
                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
              >
                <td
                  class="border-b border-gray-200 py-4 px-6 font-medium text-gray-900"
                >
                  {{ version.productName }}
                </td>
                <td class="border-b border-gray-200 py-4 px-6 text-gray-700">
                  <span
                    class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                    >{{ version.version }}</span
                  >
                </td>
                <td class="border-b border-gray-200 py-4 px-6 text-gray-700">
                  {{ version.platform }}
                </td>
                <td class="border-b border-gray-200 py-4 px-6 text-gray-700">
                  {{ version.date }}
                </td>
                <td class="border-b border-gray-200 py-4 px-6">
                  <a
                    :href="version.downloadUrl"
                    class="btn-outline text-sm px-4 py-2 inline-flex items-center"
                  >
                    <svg
                      class="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                    下载
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import Header from "../components/Header.vue";
import Footer from "../components/Footer.vue";

const products = ref([
  {
    id: 1,
    name: "ScanOnWeb",
    shortDesc: "扫描仪控件",
    version: "3.5.0",
    baseUrl: "/downloads/scanonweb",
  },
  {
    id: 2,
    name: "ImageCapOnWeb",
    shortDesc: "摄像头图像采集控件",
    version: "2.8.0",
    baseUrl: "/downloads/imagecaponweb",
  },
  {
    id: 3,
    name: "GaoPaiYi",
    shortDesc: "高拍仪控件",
    version: "2.0.1",
    baseUrl: "/downloads/gaopaiyi",
  },
]);

const operatingSystems = ref([
  { id: "windows", name: "Windows" },
  { id: "linux", name: "Linux" },
]);

const cpuArchitectures = ref([
  { id: "x64", name: "x64", description: "通用x86-64架构" },
  { id: "phytium", name: "飞腾", description: "FT-2000/4等系列" },
  { id: "loongson", name: "龙芯", description: "龙芯3A4000等系列" },
  { id: "kunpeng", name: "鲲鹏", description: "华为鲲鹏920等系列" },
]);

const selectedProduct = ref(null);
const selectedOS = ref(null);
const selectedArch = ref(null);

const downloadReady = computed(() => {
  if (!selectedProduct.value) return false;
  if (!selectedOS.value) return false;
  if (selectedOS.value.id === "linux" && !selectedArch.value) return false;
  return true;
});

const historyVersions = ref([
  {
    productName: "ScanOnWeb",
    version: "3.4.0",
    platform: "Windows",
    date: "2023-12-15",
    downloadUrl: "/downloads/scanonweb/windows/ScanOnWeb_3.4.0_Setup.exe",
  },
  {
    productName: "ScanOnWeb",
    version: "3.3.5",
    platform: "Linux (x64)",
    date: "2023-10-20",
    downloadUrl: "/downloads/scanonweb/linux/x64/ScanOnWeb_3.3.5.tar.gz",
  },
  {
    productName: "ImageCapOnWeb",
    version: "2.7.0",
    platform: "Windows",
    date: "2023-11-05",
    downloadUrl:
      "/downloads/imagecaponweb/windows/ImageCapOnWeb_2.7.0_Setup.exe",
  },
  {
    productName: "GaoPaiYi",
    version: "1.9.2",
    platform: "Windows",
    date: "2023-09-30",
    downloadUrl: "/downloads/gaopaiyi/windows/GaoPaiYi_1.9.2_Setup.exe",
  },
]);

const selectProduct = (product) => {
  selectedProduct.value = product;
  selectedOS.value = null;
  selectedArch.value = null;
};

const selectOS = (os) => {
  selectedOS.value = os;
  if (os.id !== "linux") {
    selectedArch.value = null;
  }
};

const selectArch = (arch) => {
  selectedArch.value = arch;
};

const getDownloadLink = () => {
  if (!downloadReady.value) return "#";

  const { baseUrl } = selectedProduct.value;
  const osPath = selectedOS.value.id;

  if (selectedOS.value.id === "linux") {
    return `${baseUrl}/${osPath}/${selectedArch.value.id}/${selectedProduct.value.name}_${selectedProduct.value.version}.tar.gz`;
  } else {
    return `${baseUrl}/${osPath}/${selectedProduct.value.name}_${selectedProduct.value.version}_Setup.exe`;
  }
};

// 处理下载点击事件
const handleDownloadClick = async () => {
  if (!downloadReady.value) return;

  // 追踪下载事件
  try {
    const { analytics } = await import("~/utils/analytics");
    analytics.trackDownload(
      selectedProduct.value?.name || "Unknown",
      getVersionText()
    );
  } catch (error) {
    console.log("Analytics tracking failed:", error);
  }

  // 打开下载链接
  const downloadUrl = getDownloadLink();
  window.open(downloadUrl, "_blank");
};

const getVersionText = () => {
  if (!selectedProduct.value) return "";

  let versionText = selectedProduct.value.version;

  if (selectedOS.value) {
    versionText += ` for ${selectedOS.value.name}`;

    if (selectedOS.value.id === "linux" && selectedArch.value) {
      versionText += ` (${selectedArch.value.name})`;
    }
  }

  return versionText;
};

const getFileSize = () => {
  // 这里可以根据不同产品和平台返回实际文件大小
  return "15.8 MB";
};

const getInstallInstructions = () => {
  if (!selectedProduct.value || !selectedOS.value) return "";

  if (selectedOS.value.id === "windows") {
    return "下载后双击安装包运行，按照安装向导完成安装。安装完成后，重启浏览器即可使用。";
  } else {
    return "下载后解压文件，进入解压目录，运行 ./install.sh 脚本进行安装。安装完成后，重启浏览器即可使用。";
  }
};
</script>
