import { _ as __nuxt_component_0 } from './nuxt-link-2X8I7ISh.mjs';
import { ref, mergeProps, withCtx, createTextVNode, openBlock, createBlock, createVNode, useSSRContext } from 'file://D:/peihexian/website/website/node_modules/vue/index.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrInterpolate } from 'file://D:/peihexian/website/website/node_modules/vue/server-renderer/index.mjs';
import { p as publicAssetsURL } from '../_/renderer.mjs';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import 'file://D:/peihexian/website/website/node_modules/ufo/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/h3/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/devalue/index.js';
import 'file://D:/peihexian/website/website/node_modules/@unhead/ssr/dist/index.mjs';
import '../runtime.mjs';
import 'file://D:/peihexian/website/website/node_modules/ofetch/dist/node.mjs';
import 'file://D:/peihexian/website/website/node_modules/destr/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unenv/runtime/fetch/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/hookable/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/klona/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/scule/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/defu/dist/defu.mjs';
import 'file://D:/peihexian/website/website/node_modules/ohash/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs.mjs';
import 'file:///D:/peihexian/website/website/node_modules/nuxt/dist/core/runtime/nitro/cache-driver.js';
import 'file://D:/peihexian/website/website/node_modules/unstorage/drivers/fs-lite.mjs';
import 'file://D:/peihexian/website/website/node_modules/radix3/dist/index.mjs';
import 'node:fs';
import 'node:url';
import 'file://D:/peihexian/website/website/node_modules/pathe/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unhead/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/@unhead/shared/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/unctx/dist/index.mjs';
import 'file://D:/peihexian/website/website/node_modules/vue-router/dist/vue-router.node.mjs';

const _imports_0 = publicAssetsURL("/images/wechat-qrcode.jpg");
const _sfc_main = {
  __name: "contact",
  __ssrInlineRender: true,
  setup(__props) {
    const form = ref({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: ""
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-50 min-h-screen" }, _attrs))} data-v-37ff425d>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main data-v-37ff425d><div class="bg-white border-b border-gray-200" data-v-37ff425d><div class="container mx-auto px-4 py-4" data-v-37ff425d><nav class="flex items-center space-x-2 text-sm text-gray-500" data-v-37ff425d>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/",
        class: "hover:text-orange-500"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`\u9996\u9875`);
          } else {
            return [
              createTextVNode("\u9996\u9875")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d></path></svg><span class="text-gray-900 font-medium" data-v-37ff425d>\u8054\u7CFB\u6211\u4EEC</span></nav></div></div><div class="bg-white py-12" data-v-37ff425d><div class="container mx-auto px-4" data-v-37ff425d><div class="max-w-3xl" data-v-37ff425d><h1 class="heading-primary mb-4" data-v-37ff425d>\u8054\u7CFB\u6211\u4EEC</h1><p class="text-xl text-gray-600 mb-6" data-v-37ff425d> \u6211\u4EEC\u968F\u65F6\u51C6\u5907\u4E3A\u60A8\u63D0\u4F9B\u4E13\u4E1A\u7684\u6280\u672F\u652F\u6301\u548C\u670D\u52A1\u54A8\u8BE2 </p><div class="flex flex-wrap gap-6 text-sm text-gray-500" data-v-37ff425d><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>24\u5C0F\u65F6\u5185\u56DE\u590D</span></div><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>\u4E13\u4E1A\u6280\u672F\u56E2\u961F</span></div><div class="flex items-center" data-v-37ff425d><svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20" data-v-37ff425d><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" data-v-37ff425d></path></svg><span data-v-37ff425d>\u514D\u8D39\u54A8\u8BE2\u670D\u52A1</span></div></div></div></div></div><div class="bg-orange-50 py-8" data-v-37ff425d><div class="container mx-auto px-4" data-v-37ff425d><div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-v-37ff425d><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>\u7535\u8BDD\u54A8\u8BE2</h3><p class="text-orange-600 font-semibold text-lg" data-v-37ff425d>155-1196-5595</p><p class="text-sm text-gray-500" data-v-37ff425d>\u5DE5\u4F5C\u65E5 9:00-18:00</p></div><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>\u90AE\u4EF6\u8054\u7CFB</h3><p class="text-orange-600 font-semibold" data-v-37ff425d><EMAIL></p><p class="text-sm text-gray-500" data-v-37ff425d>24\u5C0F\u65F6\u5185\u56DE\u590D</p></div><div class="text-center" data-v-37ff425d><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-37ff425d><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" data-v-37ff425d></path></svg></div><h3 class="font-bold text-gray-900 mb-2" data-v-37ff425d>\u5FAE\u4FE1\u54A8\u8BE2</h3><p class="text-orange-600 font-semibold" data-v-37ff425d>20155031</p><p class="text-sm text-gray-500" data-v-37ff425d>\u626B\u7801\u6DFB\u52A0\u5FAE\u4FE1</p></div></div></div></div><div class="container mx-auto px-4 py-12" data-v-37ff425d><div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-v-37ff425d><div class="card-business p-8" data-v-37ff425d><div class="mb-6" data-v-37ff425d><h2 class="heading-secondary mb-2" data-v-37ff425d>\u53D1\u9001\u6D88\u606F</h2><p class="text-gray-600" data-v-37ff425d>\u8BF7\u586B\u5199\u4EE5\u4E0B\u4FE1\u606F\uFF0C\u6211\u4EEC\u4F1A\u5C3D\u5FEB\u4E0E\u60A8\u8054\u7CFB</p></div><form class="space-y-6" data-v-37ff425d><div class="grid grid-cols-1 md:grid-cols-2 gap-6" data-v-37ff425d><div data-v-37ff425d><label for="name" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> \u59D3\u540D <span class="text-red-500" data-v-37ff425d>*</span></label><input type="text" id="name"${ssrRenderAttr("value", form.value.name)} required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="\u8BF7\u8F93\u5165\u60A8\u7684\u59D3\u540D" data-v-37ff425d></div><div data-v-37ff425d><label for="phone" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> \u7535\u8BDD <span class="text-red-500" data-v-37ff425d>*</span></label><input type="tel" id="phone"${ssrRenderAttr("value", form.value.phone)} required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="\u8BF7\u8F93\u5165\u60A8\u7684\u7535\u8BDD" data-v-37ff425d></div></div><div data-v-37ff425d><label for="email" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> \u90AE\u7BB1 </label><input type="email" id="email"${ssrRenderAttr("value", form.value.email)} class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" placeholder="\u8BF7\u8F93\u5165\u60A8\u7684\u90AE\u7BB1" data-v-37ff425d></div><div data-v-37ff425d><label for="subject" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> \u54A8\u8BE2\u7C7B\u578B <span class="text-red-500" data-v-37ff425d>*</span></label><select id="subject" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors" data-v-37ff425d><option value="" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "") : ssrLooseEqual(form.value.subject, "")) ? " selected" : ""}>\u8BF7\u9009\u62E9\u54A8\u8BE2\u7C7B\u578B</option><option value="\u4EA7\u54C1\u54A8\u8BE2" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "\u4EA7\u54C1\u54A8\u8BE2") : ssrLooseEqual(form.value.subject, "\u4EA7\u54C1\u54A8\u8BE2")) ? " selected" : ""}>\u4EA7\u54C1\u529F\u80FD\u54A8\u8BE2</option><option value="\u6280\u672F\u652F\u6301" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "\u6280\u672F\u652F\u6301") : ssrLooseEqual(form.value.subject, "\u6280\u672F\u652F\u6301")) ? " selected" : ""}>\u6280\u672F\u652F\u6301</option><option value="\u5546\u52A1\u5408\u4F5C" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "\u5546\u52A1\u5408\u4F5C") : ssrLooseEqual(form.value.subject, "\u5546\u52A1\u5408\u4F5C")) ? " selected" : ""}>\u5546\u52A1\u5408\u4F5C</option><option value="\u552E\u524D\u54A8\u8BE2" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "\u552E\u524D\u54A8\u8BE2") : ssrLooseEqual(form.value.subject, "\u552E\u524D\u54A8\u8BE2")) ? " selected" : ""}>\u552E\u524D\u54A8\u8BE2</option><option value="\u5176\u4ED6\u95EE\u9898" data-v-37ff425d${ssrIncludeBooleanAttr(Array.isArray(form.value.subject) ? ssrLooseContain(form.value.subject, "\u5176\u4ED6\u95EE\u9898") : ssrLooseEqual(form.value.subject, "\u5176\u4ED6\u95EE\u9898")) ? " selected" : ""}>\u5176\u4ED6\u95EE\u9898</option></select></div><div data-v-37ff425d><label for="message" class="block text-sm font-medium text-gray-700 mb-2" data-v-37ff425d> \u8BE6\u7EC6\u63CF\u8FF0 <span class="text-red-500" data-v-37ff425d>*</span></label><textarea id="message" required rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors resize-none" placeholder="\u8BF7\u8BE6\u7EC6\u63CF\u8FF0\u60A8\u7684\u95EE\u9898\u6216\u9700\u6C42\uFF0C\u6211\u4EEC\u4F1A\u6839\u636E\u60A8\u7684\u63CF\u8FF0\u63D0\u4F9B\u66F4\u7CBE\u51C6\u7684\u670D\u52A1" data-v-37ff425d>${ssrInterpolate(form.value.message)}</textarea></div><div class="pt-4" data-v-37ff425d><button type="submit" class="btn-primary w-full py-4 text-lg" data-v-37ff425d> \u63D0\u4EA4\u54A8\u8BE2 </button><p class="text-sm text-gray-500 mt-3 text-center" data-v-37ff425d> \u63D0\u4EA4\u540E\u6211\u4EEC\u4F1A\u572824\u5C0F\u65F6\u5185\u4E0E\u60A8\u8054\u7CFB </p></div></form></div><div class="space-y-6" data-v-37ff425d><div class="card-business p-6" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>\u8054\u7CFB\u65B9\u5F0F\u8BE6\u60C5</h3><div class="space-y-4" data-v-37ff425d><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>\u9500\u552E\u70ED\u7EBF</h4><p class="text-orange-600 font-semibold" data-v-37ff425d>155-1196-5595</p><p class="text-sm text-gray-500" data-v-37ff425d>\u5DE5\u4F5C\u65E5 9:00-18:00</p></div></div><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>\u5546\u52A1\u90AE\u7BB1</h4><p class="text-orange-600 font-semibold" data-v-37ff425d><EMAIL></p><p class="text-sm text-gray-500" data-v-37ff425d>24\u5C0F\u65F6\u5185\u56DE\u590D</p></div></div><div class="flex items-start" data-v-37ff425d><div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4 mt-1" data-v-37ff425d><svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-37ff425d></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-37ff425d></path></svg></div><div data-v-37ff425d><h4 class="font-semibold text-gray-900" data-v-37ff425d>\u516C\u53F8\u5730\u5740</h4><p class="text-gray-700" data-v-37ff425d>\u5E7F\u4E1C\u7701\u5E7F\u5DDE\u5E02\u5929\u6CB3\u533A</p><p class="text-sm text-gray-500" data-v-37ff425d>\u53EF\u9884\u7EA6\u4E0A\u95E8\u670D\u52A1</p></div></div></div></div><div class="card-business p-6" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>\u670D\u52A1\u65F6\u95F4</h3><div class="space-y-3" data-v-37ff425d><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>\u9500\u552E\u54A8\u8BE2</span><span class="font-semibold text-gray-900" data-v-37ff425d>\u5468\u4E00\u81F3\u5468\u4E94 9:00-18:00</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>\u6280\u672F\u652F\u6301</span><span class="font-semibold text-gray-900" data-v-37ff425d>7\xD724\u5C0F\u65F6</span></div><div class="flex justify-between items-center py-2" data-v-37ff425d><span class="text-gray-700" data-v-37ff425d>\u90AE\u4EF6\u56DE\u590D</span><span class="font-semibold text-gray-900" data-v-37ff425d>24\u5C0F\u65F6\u5185</span></div></div></div><div class="card-business p-6 text-center" data-v-37ff425d><h3 class="heading-tertiary mb-4" data-v-37ff425d>\u5FAE\u4FE1\u54A8\u8BE2</h3><div class="inline-block bg-gray-50 p-4 rounded-lg" data-v-37ff425d><img${ssrRenderAttr("src", _imports_0)} alt="\u5FAE\u4FE1\u4E8C\u7EF4\u7801" class="w-32 h-32 mx-auto" data-v-37ff425d><p class="text-sm text-gray-600 mt-2" data-v-37ff425d>\u626B\u7801\u6DFB\u52A0\u5FAE\u4FE1</p><p class="text-xs text-gray-500" data-v-37ff425d>\u5FAE\u4FE1\u53F7\uFF1A20155031</p></div></div></div></div></div><div class="container mx-auto px-4 py-12" data-v-37ff425d><div class="card-business p-8" data-v-37ff425d><div class="text-center mb-12" data-v-37ff425d><h2 class="heading-secondary mb-4" data-v-37ff425d>\u5E38\u89C1\u95EE\u9898\u89E3\u7B54</h2><p class="text-gray-600" data-v-37ff425d>\u5FEB\u901F\u627E\u5230\u60A8\u5173\u5FC3\u7684\u95EE\u9898\u7B54\u6848</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8" data-v-37ff425d><div class="space-y-6" data-v-37ff425d><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> \u4EA7\u54C1\u529F\u80FD\u54A8\u8BE2 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> \u6211\u4EEC\u7684\u4EA7\u54C1\u652F\u6301\u591A\u79CD\u626B\u63CF\u8BBE\u5907\u548C\u56FE\u50CF\u5904\u7406\u529F\u80FD\uFF0C\u517C\u5BB9Windows\u548CLinux\u5E73\u53F0\u3002\u5982\u9700\u4E86\u89E3\u5177\u4F53\u529F\u80FD\u7279\u6027\uFF0C\u8BF7\u67E5\u770B\u4EA7\u54C1\u8BE6\u60C5\u9875\u9762\u6216\u8054\u7CFB\u6211\u4EEC\u7684\u6280\u672F\u987E\u95EE\u3002 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/products",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u67E5\u770B\u4EA7\u54C1\u8BE6\u60C5 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" \u67E5\u770B\u4EA7\u54C1\u8BE6\u60C5 "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> \u6280\u672F\u652F\u6301\u670D\u52A1 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> \u6211\u4EEC\u63D0\u4F9B7\xD724\u5C0F\u65F6\u6280\u672F\u652F\u6301\u670D\u52A1\uFF0C\u5305\u62EC\u8FDC\u7A0B\u534F\u52A9\u3001\u73B0\u573A\u670D\u52A1\u7B49\u3002\u6280\u672F\u56E2\u961F\u5177\u5907\u4E30\u5BCC\u7684\u9879\u76EE\u7ECF\u9A8C\uFF0C\u80FD\u591F\u5FEB\u901F\u89E3\u51B3\u5404\u79CD\u6280\u672F\u95EE\u9898\u3002 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/documents",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u67E5\u770B\u6280\u672F\u6587\u6863 <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" \u67E5\u770B\u6280\u672F\u6587\u6863 "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="space-y-6" data-v-37ff425d><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> \u8D2D\u4E70\u548C\u6388\u6743 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> \u6211\u4EEC\u63D0\u4F9B\u7075\u6D3B\u7684\u6388\u6743\u65B9\u6848\u548C\u591A\u79CD\u652F\u4ED8\u65B9\u5F0F\uFF0C\u652F\u6301\u4F01\u4E1A\u91C7\u8D2D\u6D41\u7A0B\u3002\u6240\u6709\u4EA7\u54C1\u5747\u63D0\u4F9B\u6B63\u89C4\u53D1\u7968\u548C\u8F6F\u4EF6\u8457\u4F5C\u6743\u6388\u6743\u6587\u4EF6\u3002 </p>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/purchase",
        class: "text-orange-500 hover:text-orange-600 font-medium inline-flex items-center"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u4E86\u89E3\u8D2D\u4E70\u6D41\u7A0B <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d${_scopeId}><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d${_scopeId}></path></svg>`);
          } else {
            return [
              createTextVNode(" \u4E86\u89E3\u8D2D\u4E70\u6D41\u7A0B "),
              (openBlock(), createBlock("svg", {
                class: "w-4 h-4 ml-1",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24"
              }, [
                createVNode("path", {
                  "stroke-linecap": "round",
                  "stroke-linejoin": "round",
                  "stroke-width": "2",
                  d: "M9 5l7 7-7 7"
                })
              ]))
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div><div class="border-l-4 border-orange-500 pl-6" data-v-37ff425d><h3 class="text-lg font-semibold mb-3 text-gray-900" data-v-37ff425d> \u5408\u4F5C\u4E0E\u5B9A\u5236 </h3><p class="text-gray-600 mb-4" data-v-37ff425d> \u6211\u4EEC\u6B22\u8FCE\u5404\u7C7B\u4F01\u4E1A\u5408\u4F5C\uFF0C\u63D0\u4F9B\u4EA7\u54C1\u5B9A\u5236\u3001\u6280\u672F\u54A8\u8BE2\u3001\u89E3\u51B3\u65B9\u6848\u8BBE\u8BA1\u7B49\u670D\u52A1\u3002\u53EF\u6839\u636E\u60A8\u7684\u5177\u4F53\u9700\u6C42\u63D0\u4F9B\u4E13\u4E1A\u7684\u5B9A\u5236\u5316\u65B9\u6848\u3002 </p><div class="text-orange-500 hover:text-orange-600 font-medium inline-flex items-center cursor-pointer" data-v-37ff425d> \u8054\u7CFB\u5546\u52A1\u5408\u4F5C <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-37ff425d></path></svg></div></div></div></div><div class="mt-12 p-6 bg-orange-50 rounded-lg border border-orange-200" data-v-37ff425d><div class="text-center" data-v-37ff425d><h3 class="text-lg font-semibold text-gray-900 mb-2" data-v-37ff425d> \u6CA1\u6709\u627E\u5230\u60A8\u8981\u7684\u7B54\u6848\uFF1F </h3><p class="text-gray-600 mb-4" data-v-37ff425d>\u6211\u4EEC\u7684\u4E13\u4E1A\u56E2\u961F\u968F\u65F6\u4E3A\u60A8\u63D0\u4F9B\u5E2E\u52A9</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-37ff425d><a href="tel:155-1196-5595" class="btn-primary inline-flex items-center justify-center" data-v-37ff425d><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" data-v-37ff425d></path></svg> \u7ACB\u5373\u81F4\u7535 </a><button class="btn-secondary inline-flex items-center justify-center" data-v-37ff425d><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-37ff425d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-v-37ff425d></path></svg> \u5728\u7EBF\u54A8\u8BE2 </button></div></div></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/contact.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const contact = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-37ff425d"]]);

export { contact as default };
//# sourceMappingURL=contact-BjxooIhq.mjs.map
