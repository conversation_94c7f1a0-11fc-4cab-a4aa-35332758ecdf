{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "vue.inlayHints.missingProps": true, "vue.inlayHints.inlineHandlerLeading": true, "vue.inlayHints.optionsWrapper": true, "vue.complete.casing.tags": "kebab", "vue.complete.casing.props": "camel", "emmet.includeLanguages": {"vue": "html", "vue-html": "html"}, "files.associations": {"*.vue": "vue"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "tailwindCSS.includeLanguages": {"vue": "html", "vue-html": "html"}, "tailwindCSS.experimental.classRegex": [["class:\\s*?[\"'`]([^\"'`]*).*?[\"'`]", "[\"'`]([^\"'`]*)[\"'`]"]], "css.validate": false, "scss.validate": false, "less.validate": false, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.nuxt": true, "**/.output": true}, "files.exclude": {"**/.nuxt": true, "**/node_modules": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.vue": "${capture}.vue.ts,${capture}.vue.js,${capture}.stories.vue,${capture}.test.vue,${capture}.spec.vue", "nuxt.config.*": ".nuxtignore,.nuxtrc,nuxt.schema.*", "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml,.npmrc,.yarnrc"}}