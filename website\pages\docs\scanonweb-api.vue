<template>
  <div class="bg-gray-50 min-h-screen">
    <Header />
    <main>
      <!-- 面包屑导航 -->
      <div class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4 py-4">
          <nav class="flex items-center space-x-2 text-sm text-gray-500">
            <NuxtLink to="/" class="hover:text-orange-500">首页</NuxtLink>
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
            <NuxtLink to="/documents" class="hover:text-orange-500"
              >文档资料</NuxtLink
            >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
            <span class="text-gray-900 font-medium">ScanOnWeb API文档</span>
          </nav>
        </div>
      </div>

      <!-- 页面标题区域 -->
      <div class="bg-white py-8">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl">
            <h1 class="heading-primary mb-4">ScanOnWeb API文档</h1>
            <p class="text-lg text-gray-600 mb-6">
              完整的ScanOnWeb扫描控件JavaScript
              API参考文档，包含所有方法、属性和事件回调
            </p>
            <div class="flex flex-wrap gap-6 text-sm text-gray-500">
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>完整API参考</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>详细参数说明</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>代码示例</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="container mx-auto py-12 px-4">
        <!-- 目录导航 -->
        <div class="card-business p-6 mb-8">
          <h2 class="heading-secondary mb-4">文档目录</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <a
              href="#constructor"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">构造函数</div>
              <div class="text-sm text-gray-600">ScanOnWeb()</div>
            </a>
            <a
              href="#config"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">配置属性</div>
              <div class="text-sm text-gray-600">scaner_work_config</div>
            </a>
            <a
              href="#device-methods"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">设备管理</div>
              <div class="text-sm text-gray-600">设备相关方法</div>
            </a>
            <a
              href="#scan-methods"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">扫描操作</div>
              <div class="text-sm text-gray-600">扫描相关方法</div>
            </a>
            <a
              href="#image-methods"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">图像处理</div>
              <div class="text-sm text-gray-600">图像相关方法</div>
            </a>
            <a
              href="#upload-methods"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">上传保存</div>
              <div class="text-sm text-gray-600">文件操作方法</div>
            </a>
            <a
              href="#events"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">事件回调</div>
              <div class="text-sm text-gray-600">所有事件回调</div>
            </a>
            <a
              href="#examples"
              class="block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors"
            >
              <div class="font-medium text-gray-900">使用示例</div>
              <div class="text-sm text-gray-600">完整代码示例</div>
            </a>
          </div>
        </div>

        <!-- 构造函数 -->
        <div id="constructor" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">构造函数</h2>

          <div class="api-method">
            <h3 class="api-method-name">new ScanOnWeb()</h3>
            <p class="api-description">
              创建ScanOnWeb扫描控件实例，自动初始化WebSocket连接并尝试连接本地托盘服务。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 创建扫描控件实例
let scanonweb = new ScanOnWeb();

// 设置事件回调
scanonweb.onScanFinishedEvent = function(msg) {
    console.log('扫描完成，图像数量：', msg.imageAfterCount);
};</pre
                >
              </div>
            </div>

            <div class="api-notes">
              <h4 class="api-notes-title">注意事项</h4>
              <ul class="api-notes-list">
                <li>
                  构造函数会自动尝试连接本地WebSocket服务（端口1001-5001）
                </li>
                <li>需要确保本地已安装并运行ScanOnWeb托盘服务程序</li>
                <li>如果连接失败，相关操作方法将无法正常工作</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 配置属性 -->
        <div id="config" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">配置属性</h2>

          <div class="api-method">
            <h3 class="api-method-name">scaner_work_config</h3>
            <p class="api-description">
              扫描工作配置对象，包含所有扫描参数设置。在调用startScan()方法前需要正确配置这些参数。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">配置参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>默认值</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>showUI</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否显示扫描控件工作界面</td>
                    </tr>
                    <tr>
                      <td><code>dpi_x</code></td>
                      <td>Number</td>
                      <td>300</td>
                      <td>水平分辨率（DPI）</td>
                    </tr>
                    <tr>
                      <td><code>dpi_y</code></td>
                      <td>Number</td>
                      <td>300</td>
                      <td>垂直分辨率（DPI）</td>
                    </tr>
                    <tr>
                      <td><code>deviceIndex</code></td>
                      <td>Number</td>
                      <td>0</td>
                      <td>选中的扫描设备索引</td>
                    </tr>
                    <tr>
                      <td><code>showDialog</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否显示设备内置对话框</td>
                    </tr>
                    <tr>
                      <td><code>autoFeedEnable</code></td>
                      <td>Boolean</td>
                      <td>true</td>
                      <td>是否启用自动进纸器</td>
                    </tr>
                    <tr>
                      <td><code>autoFeed</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否自动装填纸张</td>
                    </tr>
                    <tr>
                      <td><code>dupxMode</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否启用双面扫描模式</td>
                    </tr>
                    <tr>
                      <td><code>autoDeskew</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否启用自动纠偏</td>
                    </tr>
                    <tr>
                      <td><code>autoBorderDetection</code></td>
                      <td>Boolean</td>
                      <td>false</td>
                      <td>是否启用自动边框检测</td>
                    </tr>
                    <tr>
                      <td><code>colorMode</code></td>
                      <td>String</td>
                      <td>"RGB"</td>
                      <td>色彩模式：RGB(彩色)、GRAY(灰色)、BW(黑白)</td>
                    </tr>
                    <tr>
                      <td><code>transMode</code></td>
                      <td>String</td>
                      <td>"memory"</td>
                      <td>数据传输模式：memory、file、native</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">配置示例</h4>
              <div class="code-block">
                <pre>
// 配置扫描参数
scanonweb.scaner_work_config = {
    showUI: false,
    dpi_x: 600,
    dpi_y: 600,
    deviceIndex: 0,
    showDialog: false,
    autoFeedEnable: true,
    autoFeed: false,
    dupxMode: true,  // 启用双面扫描
    autoDeskew: true,  // 启用自动纠偏
    autoBorderDetection: true,  // 启用边框检测
    colorMode: "RGB",
    transMode: "memory"
};</pre
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 设备管理方法 -->
        <div id="device-methods" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">设备管理方法</h2>

          <!-- loadDevices -->
          <div class="api-method">
            <h3 class="api-method-name">loadDevices()</h3>
            <p class="api-description">
              获取系统中所有可用的扫描设备列表。调用后会触发onGetDevicesListEvent事件回调。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置设备列表回调
scanonweb.onGetDevicesListEvent = function(msg) {
    console.log('设备列表:', msg.devices);
    console.log('当前选中设备索引:', msg.currentIndex);

    // 填充设备选择下拉框
    const deviceSelect = document.getElementById('deviceSelect');
    deviceSelect.innerHTML = '';
    msg.devices.forEach((device, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = device;
        deviceSelect.appendChild(option);
    });
};

// 获取设备列表
scanonweb.loadDevices();</pre
                >
              </div>
            </div>
          </div>

          <!-- selectScanDevice -->
          <div class="api-method">
            <h3 class="api-method-name">selectScanDevice(deviceIndex)</h3>
            <p class="api-description">选择指定的扫描设备作为当前工作设备。</p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>deviceIndex</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>设备索引，从0开始</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 选择第一个设备
scanonweb.selectScanDevice(0);

// 选择第二个设备
scanonweb.selectScanDevice(1);</pre
                >
              </div>
            </div>
          </div>

          <!-- setLicenseKey -->
          <div class="api-method">
            <h3 class="api-method-name">
              setLicenseKey(licenseMode, key1, key2, licenseServerUrl)
            </h3>
            <p class="api-description">
              设置软件授权信息。在使用扫描功能前需要设置有效的授权密钥。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>licenseMode</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>授权模式</td>
                    </tr>
                    <tr>
                      <td><code>key1</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>授权密钥1</td>
                    </tr>
                    <tr>
                      <td><code>key2</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>授权密钥2</td>
                    </tr>
                    <tr>
                      <td><code>licenseServerUrl</code></td>
                      <td>String</td>
                      <td>否</td>
                      <td>授权服务器URL</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置授权信息
scanonweb.setLicenseKey(
    "online",
    "your-license-key-1",
    "your-license-key-2",
    "https://license.brainysoft.cn"
);</pre
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 扫描操作方法 -->
        <div id="scan-methods" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">扫描操作方法</h2>

          <!-- startScan -->
          <div class="api-method">
            <h3 class="api-method-name">startScan()</h3>
            <p class="api-description">
              开始扫描操作。使用当前的scaner_work_config配置进行扫描。扫描完成后会触发onScanFinishedEvent事件。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 配置扫描参数
scanonweb.scaner_work_config.dpi_x = 600;
scanonweb.scaner_work_config.dpi_y = 600;
scanonweb.scaner_work_config.colorMode = "RGB";
scanonweb.scaner_work_config.deviceIndex = 0;

// 设置扫描完成回调
scanonweb.onScanFinishedEvent = function(msg) {
    console.log('扫描前图像数量:', msg.imageBeforeCount);
    console.log('扫描后图像数量:', msg.imageAfterCount);

    // 自动获取扫描结果
    scanonweb.getAllImage();
};

// 开始扫描
scanonweb.startScan();</pre
                >
              </div>
            </div>

            <div class="api-notes">
              <h4 class="api-notes-title">注意事项</h4>
              <ul class="api-notes-list">
                <li>扫描前确保已选择正确的设备（deviceIndex）</li>
                <li>确保设备已连接并处于就绪状态</li>
                <li>扫描参数配置会影响扫描质量和速度</li>
              </ul>
            </div>
          </div>

          <!-- clearAll -->
          <div class="api-method">
            <h3 class="api-method-name">clearAll()</h3>
            <p class="api-description">
              清除所有已扫描的图像数据，释放内存空间。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 清除所有图像
scanonweb.clearAll();

// 同时清除页面显示
document.getElementById('imageList').innerHTML = '';</pre
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 图像处理方法 -->
        <div id="image-methods" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">图像处理方法</h2>

          <!-- getAllImage -->
          <div class="api-method">
            <h3 class="api-method-name">getAllImage()</h3>
            <p class="api-description">
              获取所有已扫描的图像数据。调用后会触发onGetAllImageEvent事件回调，返回Base64编码的图像数据。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置获取图像回调
scanonweb.onGetAllImageEvent = function(msg) {
    console.log('图像数量:', msg.imageCount);
    console.log('当前选中:', msg.currentSelected);

    // 显示图像
    const imageList = document.getElementById('imageList');
    imageList.innerHTML = '';

    msg.images.forEach((imageBase64, index) => {
        const img = document.createElement('img');
        img.src = 'data:image/jpg;base64,' + imageBase64;
        img.style.width = '200px';
        img.style.height = '200px';
        img.style.margin = '10px';
        img.style.border = '1px solid #ccc';
        imageList.appendChild(img);
    });
};

// 获取所有图像
scanonweb.getAllImage();</pre
                >
              </div>
            </div>
          </div>

          <!-- getImageById -->
          <div class="api-method">
            <h3 class="api-method-name">getImageById(index)</h3>
            <p class="api-description">
              获取指定索引的单张图像数据。调用后会触发onGetImageByIdEvent事件回调。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>index</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>图像索引，从0开始</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置单张图像回调
scanonweb.onGetImageByIdEvent = function(msg) {
    console.log('图像索引:', msg.imageIndex);
    console.log('图像数据:', msg.imageBase64);

    // 显示图像
    const img = document.createElement('img');
    img.src = 'data:image/jpg;base64,' + msg.imageBase64;
    document.body.appendChild(img);
};

// 获取第一张图像
scanonweb.getImageById(0);</pre
                >
              </div>
            </div>
          </div>

          <!-- getImageCount -->
          <div class="api-method">
            <h3 class="api-method-name">getImageCount()</h3>
            <p class="api-description">
              获取当前已扫描的图像总数。调用后会触发onGetImageCountEvent事件回调。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置图像计数回调
scanonweb.onGetImageCountEvent = function(msg) {
    console.log('图像总数:', msg.imageCount);
    console.log('当前选中图像索引:', msg.currentSelected);
};

// 获取图像数量
scanonweb.getImageCount();</pre
                >
              </div>
            </div>
          </div>

          <!-- rotateImage -->
          <div class="api-method">
            <h3 class="api-method-name">rotateImage(index, angle)</h3>
            <p class="api-description">
              旋转指定索引的图像。支持90度的倍数旋转。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>index</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>图像索引，从0开始</td>
                    </tr>
                    <tr>
                      <td><code>angle</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>旋转角度：90、180、270</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 将第一张图像顺时针旋转90度
scanonweb.rotateImage(0, 90);

// 将第二张图像旋转180度
scanonweb.rotateImage(1, 180);</pre
                >
              </div>
            </div>
          </div>

          <!-- getImageSize -->
          <div class="api-method">
            <h3 class="api-method-name">getImageSize(index)</h3>
            <p class="api-description">
              获取指定索引图像的尺寸信息。调用后会触发onGetImageSizeEvent事件回调。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>index</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>图像索引，从0开始</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置图像尺寸回调
scanonweb.onGetImageSizeEvent = function(msg) {
    console.log('图像宽度:', msg.width);
    console.log('图像高度:', msg.height);
    console.log('图像索引:', msg.imageIndex);
};

// 获取第一张图像尺寸
scanonweb.getImageSize(0);</pre
                >
              </div>
            </div>
          </div>

          <!-- loadImageFromUrl -->
          <div class="api-method">
            <h3 class="api-method-name">loadImageFromUrl(url)</h3>
            <p class="api-description">
              从远程URL加载图像到扫描控件中。支持多页图像文件。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>url</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>图像文件的URL地址</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 从服务器加载图像
scanonweb.loadImageFromUrl('https://example.com/document.pdf');

// 加载本地服务器图像
scanonweb.loadImageFromUrl('/uploads/scan_result.tiff');</pre
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 上传保存方法 -->
        <div id="upload-methods" class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">上传保存方法</h2>

          <!-- uploadAllImageAsPdfToUrl -->
          <div class="api-method">
            <h3 class="api-method-name">
              uploadAllImageAsPdfToUrl(url, id, desc)
            </h3>
            <p class="api-description">
              将所有图像合并为PDF格式并上传到指定URL。调用后会触发onUploadAllImageAsPdfToUrlEvent事件回调。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>url</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>上传目标URL地址</td>
                    </tr>
                    <tr>
                      <td><code>id</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>文档标识ID</td>
                    </tr>
                    <tr>
                      <td><code>desc</code></td>
                      <td>String</td>
                      <td>否</td>
                      <td>文档描述信息</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 设置上传回调
scanonweb.onUploadAllImageAsPdfToUrlEvent = function(msg) {
    const result = JSON.parse(msg.uploadResult);
    if (result.network) {
        console.log('上传成功:', result.msg);
    } else {
        console.error('上传失败:', result.msg);
    }
};

// 上传PDF到服务器
scanonweb.uploadAllImageAsPdfToUrl(
    'https://api.example.com/upload',
    'DOC_001',
    '扫描文档'
);</pre
                >
              </div>
            </div>
          </div>

          <!-- saveAllImageToLocal -->
          <div class="api-method">
            <h3 class="api-method-name">saveAllImageToLocal(filename)</h3>
            <p class="api-description">
              将所有图像保存到客户端本地文件。支持多种格式：PDF、TIFF、JPG等。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>filename</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>保存的文件路径和名称</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 保存为PDF文件
scanonweb.saveAllImageToLocal('D:/Documents/scan_result.pdf');

// 保存为TIFF文件
scanonweb.saveAllImageToLocal('D:/Documents/scan_result.tiff');

// 保存为JPG文件（仅第一张图像）
scanonweb.saveAllImageToLocal('D:/Documents/scan_result.jpg');</pre
                >
              </div>
            </div>
          </div>

          <!-- uploadJpgImageByIndex -->
          <div class="api-method">
            <h3 class="api-method-name">
              uploadJpgImageByIndex(url, id, desc, index)
            </h3>
            <p class="api-description">
              上传指定索引的单张图像（JPG格式）到服务器。
            </p>

            <div class="api-params">
              <h4 class="api-params-title">参数</h4>
              <div class="overflow-x-auto">
                <table class="api-params-table">
                  <thead>
                    <tr>
                      <th>参数名</th>
                      <th>类型</th>
                      <th>必填</th>
                      <th>说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>url</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>上传目标URL地址</td>
                    </tr>
                    <tr>
                      <td><code>id</code></td>
                      <td>String</td>
                      <td>是</td>
                      <td>图像标识ID</td>
                    </tr>
                    <tr>
                      <td><code>desc</code></td>
                      <td>String</td>
                      <td>否</td>
                      <td>图像描述信息</td>
                    </tr>
                    <tr>
                      <td><code>index</code></td>
                      <td>Number</td>
                      <td>是</td>
                      <td>图像索引，从0开始</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 上传第一张图像
scanonweb.uploadJpgImageByIndex(
    'https://api.example.com/upload-image',
    'IMG_001',
    '身份证正面',
    0
);</pre
                >
              </div>
            </div>
          </div>

          <!-- openClientLocalfile -->
          <div class="api-method">
            <h3 class="api-method-name">openClientLocalfile()</h3>
            <p class="api-description">
              打开客户端文件选择对话框，允许用户选择本地图像文件加载到控件中。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 打开文件选择对话框
scanonweb.openClientLocalfile();

// 用户选择文件后，会自动加载到控件中
// 可以通过getAllImage()获取加载的图像</pre
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 界面控制方法 -->
        <div class="card-business p-8 mb-8">
          <h2 class="heading-secondary mb-6">界面控制方法</h2>

          <!-- setFocus -->
          <div class="api-method">
            <h3 class="api-method-name">setFocus()</h3>
            <p class="api-description">
              设置扫描控件界面获得焦点，将控件窗口置于前台。
            </p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 将扫描控件窗口置于前台
scanonweb.setFocus();</pre
                >
              </div>
            </div>
          </div>

          <!-- hidden -->
          <div class="api-method">
            <h3 class="api-method-name">hidden()</h3>
            <p class="api-description">隐藏扫描控件界面窗口。</p>

            <div class="api-example">
              <h4 class="api-example-title">示例代码</h4>
              <div class="code-block">
                <pre>
// 隐藏扫描控件界面
scanonweb.hidden();</pre
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import Header from "~/components/Header.vue";
import Footer from "~/components/Footer.vue";

// SEO设置
useHead({
  title: "ScanOnWeb API文档 - 完整JavaScript API参考",
  meta: [
    {
      name: "description",
      content:
        "ScanOnWeb扫描控件完整API文档，包含所有JavaScript方法、属性、事件回调的详细说明和代码示例。",
    },
    {
      name: "keywords",
      content:
        "ScanOnWeb,API文档,JavaScript,扫描控件,方法参考,事件回调,参数说明",
    },
  ],
});
</script>

<style scoped>
.api-method {
  @apply mb-8;
}

.api-method-name {
  @apply text-xl font-bold text-gray-900 mb-3 font-mono bg-gray-100 px-3 py-2 rounded;
}

.api-description {
  @apply text-gray-700 mb-4;
}

.api-params-title,
.api-example-title,
.api-notes-title {
  @apply text-lg font-semibold text-gray-800 mb-3;
}

.api-params-table {
  @apply w-full border-collapse border border-gray-300;
}

.api-params-table th,
.api-params-table td {
  @apply border border-gray-300 px-4 py-2 text-left;
}

.api-params-table th {
  @apply bg-gray-100 font-semibold;
}

.api-params-table code {
  @apply bg-gray-200 px-2 py-1 rounded text-sm font-mono;
}

.code-block {
  @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;
}

.code-block pre {
  @apply font-mono text-sm m-0;
}

.api-notes-list {
  @apply list-disc list-inside space-y-1 text-gray-700;
}
</style>
