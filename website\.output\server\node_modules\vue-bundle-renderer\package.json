{"name": "vue-bundle-renderer", "version": "2.1.0", "description": "Bundle Renderer for Vue 3.0", "repository": "nuxt-contrib/vue-bundle-renderer", "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./runtime": {"types": "./dist/runtime.d.ts", "import": "./dist/runtime.mjs", "require": "./dist/runtime.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "runtime.d.ts"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint src", "prepack": "unbuild", "release": "pnpm test && pnpm build && changelogen --release --push && pnpm publish", "test": "pnpm lint && pnpm vitest run --coverage && tsc --noEmit"}, "dependencies": {"ufo": "^1.5.3"}, "devDependencies": {"@types/node": "^20.12.11", "@vitest/coverage-v8": "^1.6.0", "changelogen": "^0.5.5", "eslint": "^9.2.0", "typescript": "^5.4.5", "unbuild": "^2.0.0", "vite": "^5.2.11", "vitest": "1.6.0", "vue": "3.4.27", "@nuxt/eslint-config": "^0.3.10"}, "packageManager": "pnpm@9.1.0"}