{"version": 3, "file": "scanonweb-api-C_sYR7lZ.js", "sources": ["../../../../pages/docs/scanonweb-api.vue"], "sourcesContent": ["<template>\n  <div class=\"bg-gray-50 min-h-screen\">\n    <Header />\n    <main>\n      <!-- 面包屑导航 -->\n      <div class=\"bg-white border-b border-gray-200\">\n        <div class=\"container mx-auto px-4 py-4\">\n          <nav class=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <NuxtLink to=\"/\" class=\"hover:text-orange-500\">首页</NuxtLink>\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <NuxtLink to=\"/documents\" class=\"hover:text-orange-500\"\n              >文档资料</NuxtLink\n            >\n            <svg\n              class=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5l7 7-7 7\"\n              ></path>\n            </svg>\n            <span class=\"text-gray-900 font-medium\">ScanOnWeb API文档</span>\n          </nav>\n        </div>\n      </div>\n\n      <!-- 页面标题区域 -->\n      <div class=\"bg-white py-8\">\n        <div class=\"container mx-auto px-4\">\n          <div class=\"max-w-4xl\">\n            <h1 class=\"heading-primary mb-4\">ScanOnWeb API文档</h1>\n            <p class=\"text-lg text-gray-600 mb-6\">\n              完整的ScanOnWeb扫描控件JavaScript\n              API参考文档，包含所有方法、属性和事件回调\n            </p>\n            <div class=\"flex flex-wrap gap-6 text-sm text-gray-500\">\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>完整API参考</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>详细参数说明</span>\n              </div>\n              <div class=\"flex items-center\">\n                <svg\n                  class=\"w-4 h-4 mr-2 text-green-500\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fill-rule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clip-rule=\"evenodd\"\n                  ></path>\n                </svg>\n                <span>代码示例</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 主要内容区域 -->\n      <div class=\"container mx-auto py-12 px-4\">\n        <!-- 目录导航 -->\n        <div class=\"card-business p-6 mb-8\">\n          <h2 class=\"heading-secondary mb-4\">文档目录</h2>\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            <a\n              href=\"#constructor\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">构造函数</div>\n              <div class=\"text-sm text-gray-600\">ScanOnWeb()</div>\n            </a>\n            <a\n              href=\"#config\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">配置属性</div>\n              <div class=\"text-sm text-gray-600\">scaner_work_config</div>\n            </a>\n            <a\n              href=\"#device-methods\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">设备管理</div>\n              <div class=\"text-sm text-gray-600\">设备相关方法</div>\n            </a>\n            <a\n              href=\"#scan-methods\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">扫描操作</div>\n              <div class=\"text-sm text-gray-600\">扫描相关方法</div>\n            </a>\n            <a\n              href=\"#image-methods\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">图像处理</div>\n              <div class=\"text-sm text-gray-600\">图像相关方法</div>\n            </a>\n            <a\n              href=\"#upload-methods\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">上传保存</div>\n              <div class=\"text-sm text-gray-600\">文件操作方法</div>\n            </a>\n            <a\n              href=\"#events\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">事件回调</div>\n              <div class=\"text-sm text-gray-600\">所有事件回调</div>\n            </a>\n            <a\n              href=\"#examples\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">使用示例</div>\n              <div class=\"text-sm text-gray-600\">完整代码示例</div>\n            </a>\n            <a\n              href=\"#server-examples\"\n              class=\"block p-3 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors\"\n            >\n              <div class=\"font-medium text-gray-900\">服务器端代码</div>\n              <div class=\"text-sm text-gray-600\">后端接收示例</div>\n            </a>\n          </div>\n        </div>\n\n        <!-- 构造函数 -->\n        <div id=\"constructor\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">构造函数</h2>\n\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">new ScanOnWeb()</h3>\n            <p class=\"api-description\">\n              创建ScanOnWeb扫描控件实例，自动初始化WebSocket连接并尝试连接本地托盘服务。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 创建扫描控件实例\nlet scanonweb = new ScanOnWeb();\n\n// 设置事件回调\nscanonweb.onScanFinishedEvent = function(msg) {\n    console.log('扫描完成，图像数量：', msg.imageAfterCount);\n};</pre\n                >\n              </div>\n            </div>\n\n            <div class=\"api-notes\">\n              <h4 class=\"api-notes-title\">注意事项</h4>\n              <ul class=\"api-notes-list\">\n                <li>\n                  构造函数会自动尝试连接本地WebSocket服务（端口1001-5001）\n                </li>\n                <li>需要确保本地已安装并运行ScanOnWeb托盘服务程序</li>\n                <li>如果连接失败，相关操作方法将无法正常工作</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <!-- 配置属性 -->\n        <div id=\"config\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">配置属性</h2>\n\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">scaner_work_config</h3>\n            <p class=\"api-description\">\n              扫描工作配置对象，包含所有扫描参数设置。在调用startScan()方法前需要正确配置这些参数。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">配置参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>默认值</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>showUI</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否显示扫描控件工作界面</td>\n                    </tr>\n                    <tr>\n                      <td><code>dpi_x</code></td>\n                      <td>Number</td>\n                      <td>300</td>\n                      <td>水平分辨率（DPI）</td>\n                    </tr>\n                    <tr>\n                      <td><code>dpi_y</code></td>\n                      <td>Number</td>\n                      <td>300</td>\n                      <td>垂直分辨率（DPI）</td>\n                    </tr>\n                    <tr>\n                      <td><code>deviceIndex</code></td>\n                      <td>Number</td>\n                      <td>0</td>\n                      <td>选中的扫描设备索引</td>\n                    </tr>\n                    <tr>\n                      <td><code>showDialog</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否显示设备内置对话框</td>\n                    </tr>\n                    <tr>\n                      <td><code>autoFeedEnable</code></td>\n                      <td>Boolean</td>\n                      <td>true</td>\n                      <td>是否启用自动进纸器</td>\n                    </tr>\n                    <tr>\n                      <td><code>autoFeed</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否自动装填纸张</td>\n                    </tr>\n                    <tr>\n                      <td><code>dupxMode</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否启用双面扫描模式</td>\n                    </tr>\n                    <tr>\n                      <td><code>autoDeskew</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否启用自动纠偏</td>\n                    </tr>\n                    <tr>\n                      <td><code>autoBorderDetection</code></td>\n                      <td>Boolean</td>\n                      <td>false</td>\n                      <td>是否启用自动边框检测</td>\n                    </tr>\n                    <tr>\n                      <td><code>colorMode</code></td>\n                      <td>String</td>\n                      <td>\"RGB\"</td>\n                      <td>色彩模式：RGB(彩色)、GRAY(灰色)、BW(黑白)</td>\n                    </tr>\n                    <tr>\n                      <td><code>transMode</code></td>\n                      <td>String</td>\n                      <td>\"memory\"</td>\n                      <td>数据传输模式：memory、file、native</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">配置示例</h4>\n              <div class=\"code-block\">\n                <pre>\n// 配置扫描参数\nscanonweb.scaner_work_config = {\n    showUI: false,\n    dpi_x: 600,\n    dpi_y: 600,\n    deviceIndex: 0,\n    showDialog: false,\n    autoFeedEnable: true,\n    autoFeed: false,\n    dupxMode: true,  // 启用双面扫描\n    autoDeskew: true,  // 启用自动纠偏\n    autoBorderDetection: true,  // 启用边框检测\n    colorMode: \"RGB\",\n    transMode: \"memory\"\n};</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 设备管理方法 -->\n        <div id=\"device-methods\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">设备管理方法</h2>\n\n          <!-- loadDevices -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">loadDevices()</h3>\n            <p class=\"api-description\">\n              获取系统中所有可用的扫描设备列表。调用后会触发onGetDevicesListEvent事件回调。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置设备列表回调\nscanonweb.onGetDevicesListEvent = function(msg) {\n    console.log('设备列表:', msg.devices);\n    console.log('当前选中设备索引:', msg.currentIndex);\n\n    // 填充设备选择下拉框\n    const deviceSelect = document.getElementById('deviceSelect');\n    deviceSelect.innerHTML = '';\n    msg.devices.forEach((device, index) => {\n        const option = document.createElement('option');\n        option.value = index;\n        option.textContent = device;\n        deviceSelect.appendChild(option);\n    });\n};\n\n// 获取设备列表\nscanonweb.loadDevices();</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- selectScanDevice -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">selectScanDevice(deviceIndex)</h3>\n            <p class=\"api-description\">选择指定的扫描设备作为当前工作设备。</p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>deviceIndex</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>设备索引，从0开始</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 选择第一个设备\nscanonweb.selectScanDevice(0);\n\n// 选择第二个设备\nscanonweb.selectScanDevice(1);</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- setLicenseKey -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">\n              setLicenseKey(licenseMode, key1, key2, licenseServerUrl)\n            </h3>\n            <p class=\"api-description\">\n              设置软件授权信息。在使用扫描功能前需要设置有效的授权密钥。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>licenseMode</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>授权模式</td>\n                    </tr>\n                    <tr>\n                      <td><code>key1</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>授权密钥1</td>\n                    </tr>\n                    <tr>\n                      <td><code>key2</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>授权密钥2</td>\n                    </tr>\n                    <tr>\n                      <td><code>licenseServerUrl</code></td>\n                      <td>String</td>\n                      <td>否</td>\n                      <td>授权服务器URL</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置授权信息\nscanonweb.setLicenseKey(\n    \"online\",\n    \"your-license-key-1\",\n    \"your-license-key-2\",\n    \"https://license.brainysoft.cn\"\n);</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 扫描操作方法 -->\n        <div id=\"scan-methods\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">扫描操作方法</h2>\n\n          <!-- startScan -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">startScan()</h3>\n            <p class=\"api-description\">\n              开始扫描操作。使用当前的scaner_work_config配置进行扫描。扫描完成后会触发onScanFinishedEvent事件。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 配置扫描参数\nscanonweb.scaner_work_config.dpi_x = 600;\nscanonweb.scaner_work_config.dpi_y = 600;\nscanonweb.scaner_work_config.colorMode = \"RGB\";\nscanonweb.scaner_work_config.deviceIndex = 0;\n\n// 设置扫描完成回调\nscanonweb.onScanFinishedEvent = function(msg) {\n    console.log('扫描前图像数量:', msg.imageBeforeCount);\n    console.log('扫描后图像数量:', msg.imageAfterCount);\n\n    // 自动获取扫描结果\n    scanonweb.getAllImage();\n};\n\n// 开始扫描\nscanonweb.startScan();</pre\n                >\n              </div>\n            </div>\n\n            <div class=\"api-notes\">\n              <h4 class=\"api-notes-title\">注意事项</h4>\n              <ul class=\"api-notes-list\">\n                <li>扫描前确保已选择正确的设备（deviceIndex）</li>\n                <li>确保设备已连接并处于就绪状态</li>\n                <li>扫描参数配置会影响扫描质量和速度</li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- clearAll -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">clearAll()</h3>\n            <p class=\"api-description\">\n              清除所有已扫描的图像数据，释放内存空间。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 清除所有图像\nscanonweb.clearAll();\n\n// 同时清除页面显示\ndocument.getElementById('imageList').innerHTML = '';</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 图像处理方法 -->\n        <div id=\"image-methods\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">图像处理方法</h2>\n\n          <!-- getAllImage -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">getAllImage()</h3>\n            <p class=\"api-description\">\n              获取所有已扫描的图像数据。调用后会触发onGetAllImageEvent事件回调，返回Base64编码的图像数据。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置获取图像回调\nscanonweb.onGetAllImageEvent = function(msg) {\n    console.log('图像数量:', msg.imageCount);\n    console.log('当前选中:', msg.currentSelected);\n\n    // 显示图像\n    const imageList = document.getElementById('imageList');\n    imageList.innerHTML = '';\n\n    msg.images.forEach((imageBase64, index) => {\n        const img = document.createElement('img');\n        img.src = 'data:image/jpg;base64,' + imageBase64;\n        img.style.width = '200px';\n        img.style.height = '200px';\n        img.style.margin = '10px';\n        img.style.border = '1px solid #ccc';\n        imageList.appendChild(img);\n    });\n};\n\n// 获取所有图像\nscanonweb.getAllImage();</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- getImageById -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">getImageById(index)</h3>\n            <p class=\"api-description\">\n              获取指定索引的单张图像数据。调用后会触发onGetImageByIdEvent事件回调。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>index</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>图像索引，从0开始</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置单张图像回调\nscanonweb.onGetImageByIdEvent = function(msg) {\n    console.log('图像索引:', msg.imageIndex);\n    console.log('图像数据:', msg.imageBase64);\n\n    // 显示图像\n    const img = document.createElement('img');\n    img.src = 'data:image/jpg;base64,' + msg.imageBase64;\n    document.body.appendChild(img);\n};\n\n// 获取第一张图像\nscanonweb.getImageById(0);</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- getImageCount -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">getImageCount()</h3>\n            <p class=\"api-description\">\n              获取当前已扫描的图像总数。调用后会触发onGetImageCountEvent事件回调。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置图像计数回调\nscanonweb.onGetImageCountEvent = function(msg) {\n    console.log('图像总数:', msg.imageCount);\n    console.log('当前选中图像索引:', msg.currentSelected);\n};\n\n// 获取图像数量\nscanonweb.getImageCount();</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- rotateImage -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">rotateImage(index, angle)</h3>\n            <p class=\"api-description\">\n              旋转指定索引的图像。支持90度的倍数旋转。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>index</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>图像索引，从0开始</td>\n                    </tr>\n                    <tr>\n                      <td><code>angle</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>旋转角度：90、180、270</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 将第一张图像顺时针旋转90度\nscanonweb.rotateImage(0, 90);\n\n// 将第二张图像旋转180度\nscanonweb.rotateImage(1, 180);</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- getImageSize -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">getImageSize(index)</h3>\n            <p class=\"api-description\">\n              获取指定索引图像的尺寸信息。调用后会触发onGetImageSizeEvent事件回调。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>index</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>图像索引，从0开始</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置图像尺寸回调\nscanonweb.onGetImageSizeEvent = function(msg) {\n    console.log('图像宽度:', msg.width);\n    console.log('图像高度:', msg.height);\n    console.log('图像索引:', msg.imageIndex);\n};\n\n// 获取第一张图像尺寸\nscanonweb.getImageSize(0);</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- loadImageFromUrl -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">loadImageFromUrl(url)</h3>\n            <p class=\"api-description\">\n              从远程URL加载图像到扫描控件中。支持多页图像文件。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>url</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>图像文件的URL地址</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 从服务器加载图像\nscanonweb.loadImageFromUrl('https://example.com/document.pdf');\n\n// 加载本地服务器图像\nscanonweb.loadImageFromUrl('/uploads/scan_result.tiff');</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 上传保存方法 -->\n        <div id=\"upload-methods\" class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">上传保存方法</h2>\n\n          <!-- uploadAllImageAsPdfToUrl -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">\n              uploadAllImageAsPdfToUrl(url, id, desc)\n            </h3>\n            <p class=\"api-description\">\n              将所有图像合并为PDF格式并上传到指定URL。调用后会触发onUploadAllImageAsPdfToUrlEvent事件回调。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>url</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>上传目标URL地址</td>\n                    </tr>\n                    <tr>\n                      <td><code>id</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>文档标识ID</td>\n                    </tr>\n                    <tr>\n                      <td><code>desc</code></td>\n                      <td>String</td>\n                      <td>否</td>\n                      <td>文档描述信息</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 设置上传回调\nscanonweb.onUploadAllImageAsPdfToUrlEvent = function(msg) {\n    const result = JSON.parse(msg.uploadResult);\n    if (result.network) {\n        console.log('上传成功:', result.msg);\n    } else {\n        console.error('上传失败:', result.msg);\n    }\n};\n\n// 上传PDF到服务器\nscanonweb.uploadAllImageAsPdfToUrl(\n    'https://api.example.com/upload',\n    'DOC_001',\n    '扫描文档'\n);</pre\n                >\n              </div>\n            </div>\n\n            <!-- 服务器端接收代码 -->\n            <div id=\"server-examples\" class=\"api-server-examples\">\n              <h4 class=\"api-example-title\">服务器端接收代码示例</h4>\n              <p class=\"text-gray-600 mb-4\">\n                ScanOnWeb控件通过multipart/form-data方式提交数据，包含以下4个参数：\n              </p>\n\n              <div\n                class=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\"\n              >\n                <h5 class=\"font-bold text-blue-800 mb-2\">提交参数说明</h5>\n                <ul class=\"space-y-1 text-blue-700 text-sm\">\n                  <li>\n                    <strong>image</strong> - 上传的图像文件二进制数据（PDF格式）\n                  </li>\n                  <li><strong>imageCount</strong> - 本次上传的图像总数</li>\n                  <li><strong>id</strong> - 调用方法时传入的业务ID参数</li>\n                  <li><strong>desc</strong> - 调用方法时传入的描述信息参数</li>\n                </ul>\n              </div>\n\n              <!-- Java Spring Boot -->\n              <div class=\"server-example\">\n                <h5 class=\"server-example-title\">Java Spring Boot</h5>\n                <div class=\"code-block\">\n                  <pre>\n@RestController\n@RequestMapping(\"/api\")\npublic class ScanUploadController {\n\n    @PostMapping(\"/upload\")\n    public ResponseEntity&lt;Map&lt;String, Object&gt;&gt; uploadScanImages(\n            @RequestParam(\"image\") MultipartFile imageFile,\n            @RequestParam(\"imageCount\") Integer imageCount,\n            @RequestParam(\"id\") String id,\n            @RequestParam(value = \"desc\", required = false) String desc) {\n\n        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();\n\n        try {\n            // 验证文件\n            if (imageFile.isEmpty()) {\n                response.put(\"network\", false);\n                response.put(\"msg\", \"上传文件为空\");\n                return ResponseEntity.badRequest().body(response);\n            }\n\n            // 生成文件名\n            String fileName = id + \"_\" + System.currentTimeMillis() + \".pdf\";\n            String uploadDir = \"/uploads/scan/\";\n            Path uploadPath = Paths.get(uploadDir);\n\n            // 创建目录\n            if (!Files.exists(uploadPath)) {\n                Files.createDirectories(uploadPath);\n            }\n\n            // 保存文件\n            Path filePath = uploadPath.resolve(fileName);\n            Files.copy(imageFile.getInputStream(), filePath,\n                      StandardCopyOption.REPLACE_EXISTING);\n\n            // 记录到数据库\n            ScanDocument document = new ScanDocument();\n            document.setBusinessId(id);\n            document.setDescription(desc);\n            document.setImageCount(imageCount);\n            document.setFilePath(filePath.toString());\n            document.setFileName(fileName);\n            document.setFileSize(imageFile.getSize());\n            document.setUploadTime(new Date());\n\n            scanDocumentService.save(document);\n\n            // 返回成功响应\n            response.put(\"network\", true);\n            response.put(\"msg\", \"上传成功\");\n            response.put(\"fileId\", document.getId());\n            response.put(\"fileName\", fileName);\n\n            return ResponseEntity.ok(response);\n\n        } catch (Exception e) {\n            logger.error(\"文件上传失败\", e);\n            response.put(\"network\", false);\n            response.put(\"msg\", \"上传失败: \" + e.getMessage());\n            return ResponseEntity.status(500).body(response);\n        }\n    }\n}</pre\n                  >\n                </div>\n              </div>\n\n              <!-- ASP.NET Core -->\n              <div class=\"server-example\">\n                <h5 class=\"server-example-title\">ASP.NET Core</h5>\n                <div class=\"code-block\">\n                  <pre>\n[ApiController]\n[Route(\"api/[controller]\")]\npublic class ScanUploadController : ControllerBase\n{\n    private readonly ILogger&lt;ScanUploadController&gt; _logger;\n    private readonly IScanDocumentService _scanDocumentService;\n\n    public ScanUploadController(ILogger&lt;ScanUploadController&gt; logger,\n                               IScanDocumentService scanDocumentService)\n    {\n        _logger = logger;\n        _scanDocumentService = scanDocumentService;\n    }\n\n    [HttpPost(\"upload\")]\n    public async Task&lt;IActionResult&gt; UploadScanImages(\n        [FromForm] IFormFile image,\n        [FromForm] int imageCount,\n        [FromForm] string id,\n        [FromForm] string desc = null)\n    {\n        try\n        {\n            // 验证文件\n            if (image == null || image.Length == 0)\n            {\n                return BadRequest(new { network = false, msg = \"上传文件为空\" });\n            }\n\n            // 生成文件名\n            var fileName = $\"{id}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}.pdf\";\n            var uploadDir = Path.Combine(Directory.GetCurrentDirectory(), \"uploads\", \"scan\");\n\n            // 创建目录\n            if (!Directory.Exists(uploadDir))\n            {\n                Directory.CreateDirectory(uploadDir);\n            }\n\n            // 保存文件\n            var filePath = Path.Combine(uploadDir, fileName);\n            using (var stream = new FileStream(filePath, FileMode.Create))\n            {\n                await image.CopyToAsync(stream);\n            }\n\n            // 保存到数据库\n            var document = new ScanDocument\n            {\n                BusinessId = id,\n                Description = desc,\n                ImageCount = imageCount,\n                FilePath = filePath,\n                FileName = fileName,\n                FileSize = image.Length,\n                UploadTime = DateTime.UtcNow\n            };\n\n            await _scanDocumentService.SaveAsync(document);\n\n            // 返回成功响应\n            return Ok(new\n            {\n                network = true,\n                msg = \"上传成功\",\n                fileId = document.Id,\n                fileName = fileName\n            });\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"文件上传失败\");\n            return StatusCode(500, new { network = false, msg = $\"上传失败: {ex.Message}\" });\n        }\n    }\n}</pre\n                  >\n                </div>\n              </div>\n\n              <!-- Go -->\n              <div class=\"server-example\">\n                <h5 class=\"server-example-title\">Go (Gin Framework)</h5>\n                <div class=\"code-block\">\n                  <pre>\npackage main\n\nimport (\n    \"fmt\"\n    \"io\"\n    \"net/http\"\n    \"os\"\n    \"path/filepath\"\n    \"strconv\"\n    \"time\"\n\n    \"github.com/gin-gonic/gin\"\n)\n\ntype ScanDocument struct {\n    ID          uint      `json:\"id\" gorm:\"primaryKey\"`\n    BusinessID  string    `json:\"business_id\"`\n    Description string    `json:\"description\"`\n    ImageCount  int       `json:\"image_count\"`\n    FilePath    string    `json:\"file_path\"`\n    FileName    string    `json:\"file_name\"`\n    FileSize    int64     `json:\"file_size\"`\n    UploadTime  time.Time `json:\"upload_time\"`\n}\n\nfunc uploadScanImages(c *gin.Context) {\n    // 获取表单参数\n    imageFile, header, err := c.Request.FormFile(\"image\")\n    if err != nil {\n        c.JSON(http.StatusBadRequest, gin.H{\n            \"network\": false,\n            \"msg\":     \"获取上传文件失败: \" + err.Error(),\n        })\n        return\n    }\n    defer imageFile.Close()\n\n    imageCount, _ := strconv.Atoi(c.PostForm(\"imageCount\"))\n    id := c.PostForm(\"id\")\n    desc := c.PostForm(\"desc\")\n\n    // 验证文件\n    if header.Size == 0 {\n        c.JSON(http.StatusBadRequest, gin.H{\n            \"network\": false,\n            \"msg\":     \"上传文件为空\",\n        })\n        return\n    }\n\n    // 生成文件名\n    fileName := fmt.Sprintf(\"%s_%d.pdf\", id, time.Now().UnixMilli())\n    uploadDir := \"./uploads/scan\"\n\n    // 创建目录\n    if err := os.MkdirAll(uploadDir, 0755); err != nil {\n        c.JSON(http.StatusInternalServerError, gin.H{\n            \"network\": false,\n            \"msg\":     \"创建目录失败: \" + err.Error(),\n        })\n        return\n    }\n\n    // 保存文件\n    filePath := filepath.Join(uploadDir, fileName)\n    dst, err := os.Create(filePath)\n    if err != nil {\n        c.JSON(http.StatusInternalServerError, gin.H{\n            \"network\": false,\n            \"msg\":     \"创建文件失败: \" + err.Error(),\n        })\n        return\n    }\n    defer dst.Close()\n\n    if _, err := io.Copy(dst, imageFile); err != nil {\n        c.JSON(http.StatusInternalServerError, gin.H{\n            \"network\": false,\n            \"msg\":     \"保存文件失败: \" + err.Error(),\n        })\n        return\n    }\n\n    // 保存到数据库\n    document := ScanDocument{\n        BusinessID:  id,\n        Description: desc,\n        ImageCount:  imageCount,\n        FilePath:    filePath,\n        FileName:    fileName,\n        FileSize:    header.Size,\n        UploadTime:  time.Now(),\n    }\n\n    // 这里应该调用数据库服务保存记录\n    // db.Create(&document)\n\n    // 返回成功响应\n    c.JSON(http.StatusOK, gin.H{\n        \"network\":  true,\n        \"msg\":      \"上传成功\",\n        \"fileId\":   document.ID,\n        \"fileName\": fileName,\n    })\n}\n\nfunc main() {\n    r := gin.Default()\n    r.POST(\"/api/upload\", uploadScanImages)\n    r.Run(\":8080\")\n}</pre\n                  >\n                </div>\n              </div>\n\n              <!-- Rust -->\n              <div class=\"server-example\">\n                <h5 class=\"server-example-title\">Rust (Actix-web)</h5>\n                <div class=\"code-block\">\n                  <pre>\nuse actix_multipart::Multipart;\nuse actix_web::{web, App, HttpResponse, HttpServer, Result};\nuse futures::{StreamExt, TryStreamExt};\nuse serde::{Deserialize, Serialize};\nuse std::io::Write;\nuse tokio::fs;\nuse uuid::Uuid;\n\n#[derive(Serialize, Deserialize)]\nstruct ScanDocument {\n    id: Option&lt;u32&gt;,\n    business_id: String,\n    description: Option&lt;String&gt;,\n    image_count: i32,\n    file_path: String,\n    file_name: String,\n    file_size: u64,\n    upload_time: chrono::DateTime&lt;chrono::Utc&gt;,\n}\n\n#[derive(Serialize)]\nstruct UploadResponse {\n    network: bool,\n    msg: String,\n    #[serde(skip_serializing_if = \"Option::is_none\")]\n    file_id: Option&lt;u32&gt;,\n    #[serde(skip_serializing_if = \"Option::is_none\")]\n    file_name: Option&lt;String&gt;,\n}\n\nasync fn upload_scan_images(mut payload: Multipart) -&gt; Result&lt;HttpResponse&gt; {\n    let mut image_data: Option&lt;Vec&lt;u8&gt;&gt; = None;\n    let mut image_count: Option&lt;i32&gt; = None;\n    let mut business_id: Option&lt;String&gt; = None;\n    let mut description: Option&lt;String&gt; = None;\n\n    // 解析multipart数据\n    while let Ok(Some(mut field)) = payload.try_next().await {\n        let content_disposition = field.content_disposition();\n\n        if let Some(name) = content_disposition.get_name() {\n            match name {\n                \"image\" =&gt; {\n                    let mut data = Vec::new();\n                    while let Some(chunk) = field.next().await {\n                        let chunk = chunk?;\n                        data.extend_from_slice(&chunk);\n                    }\n                    image_data = Some(data);\n                }\n                \"imageCount\" =&gt; {\n                    let mut data = Vec::new();\n                    while let Some(chunk) = field.next().await {\n                        let chunk = chunk?;\n                        data.extend_from_slice(&chunk);\n                    }\n                    if let Ok(count_str) = String::from_utf8(data) {\n                        image_count = count_str.parse().ok();\n                    }\n                }\n                \"id\" =&gt; {\n                    let mut data = Vec::new();\n                    while let Some(chunk) = field.next().await {\n                        let chunk = chunk?;\n                        data.extend_from_slice(&chunk);\n                    }\n                    if let Ok(id_str) = String::from_utf8(data) {\n                        business_id = Some(id_str);\n                    }\n                }\n                \"desc\" =&gt; {\n                    let mut data = Vec::new();\n                    while let Some(chunk) = field.next().await {\n                        let chunk = chunk?;\n                        data.extend_from_slice(&chunk);\n                    }\n                    if let Ok(desc_str) = String::from_utf8(data) {\n                        description = Some(desc_str);\n                    }\n                }\n                _ =&gt; {}\n            }\n        }\n    }\n\n    // 验证必要参数\n    let image_data = match image_data {\n        Some(data) if !data.is_empty() =&gt; data,\n        _ =&gt; {\n            return Ok(HttpResponse::BadRequest().json(UploadResponse {\n                network: false,\n                msg: \"上传文件为空\".to_string(),\n                file_id: None,\n                file_name: None,\n            }));\n        }\n    };\n\n    let business_id = business_id.unwrap_or_else(|| Uuid::new_v4().to_string());\n    let image_count = image_count.unwrap_or(1);\n\n    // 生成文件名\n    let timestamp = chrono::Utc::now().timestamp_millis();\n    let file_name = format!(\"{}_{}.pdf\", business_id, timestamp);\n    let upload_dir = \"./uploads/scan\";\n\n    // 创建目录\n    if let Err(e) = fs::create_dir_all(upload_dir).await {\n        return Ok(HttpResponse::InternalServerError().json(UploadResponse {\n            network: false,\n            msg: format!(\"创建目录失败: {}\", e),\n            file_id: None,\n            file_name: None,\n        }));\n    }\n\n    // 保存文件\n    let file_path = format!(\"{}/{}\", upload_dir, file_name);\n    if let Err(e) = fs::write(&file_path, &image_data).await {\n        return Ok(HttpResponse::InternalServerError().json(UploadResponse {\n            network: false,\n            msg: format!(\"保存文件失败: {}\", e),\n            file_id: None,\n            file_name: None,\n        }));\n    }\n\n    // 保存到数据库\n    let document = ScanDocument {\n        id: None,\n        business_id: business_id.clone(),\n        description,\n        image_count,\n        file_path: file_path.clone(),\n        file_name: file_name.clone(),\n        file_size: image_data.len() as u64,\n        upload_time: chrono::Utc::now(),\n    };\n\n    // 这里应该调用数据库服务保存记录\n    // let saved_document = db_service.save(document).await?;\n\n    // 返回成功响应\n    Ok(HttpResponse::Ok().json(UploadResponse {\n        network: true,\n        msg: \"上传成功\".to_string(),\n        file_id: Some(1), // document.id\n        file_name: Some(file_name),\n    }))\n}\n\n#[actix_web::main]\nasync fn main() -&gt; std::io::Result&lt;()&gt; {\n    HttpServer::new(|| {\n        App::new()\n            .route(\"/api/upload\", web::post().to(upload_scan_images))\n    })\n    .bind(\"127.0.0.1:8080\")?\n    .run()\n    .await\n}</pre\n                  >\n                </div>\n              </div>\n\n              <!-- 前端JavaScript上传示例 -->\n              <div class=\"frontend-examples\">\n                <h4 class=\"api-example-title\">前端JavaScript上传示例</h4>\n                <p class=\"text-gray-600 mb-4\">\n                  第二种上传方式：前端通过WebSocket获取图像数据后，使用JavaScript进行上传\n                </p>\n\n                <!-- 获取图像数据并上传 -->\n                <div class=\"frontend-example\">\n                  <h5 class=\"server-example-title\">\n                    获取图像数据并上传（Base64方式）\n                  </h5>\n                  <div class=\"code-block\">\n                    <pre>\n// 获取扫描图像的Base64数据\nscanonweb.onGetAllImageEvent = function(msg) {\n    console.log('获取到图像数据:', msg.images);\n\n    // 上传所有图像\n    uploadImagesToServer(msg.images, 'DOC_001', '扫描文档');\n};\n\n// 上传图像到服务器\nasync function uploadImagesToServer(images, businessId, description) {\n    try {\n        for (let i = 0; i &lt; images.length; i++) {\n            const base64Data = images[i];\n\n            // 将Base64转换为Blob\n            const blob = base64ToBlob(base64Data, 'image/jpeg');\n\n            // 创建FormData\n            const formData = new FormData();\n            formData.append('image', blob, `scan_${businessId}_${i}.jpg`);\n            formData.append('imageCount', images.length.toString());\n            formData.append('id', businessId);\n            formData.append('desc', description);\n            formData.append('imageIndex', i.toString());\n\n            // 使用axios上传\n            const response = await axios.post('/api/upload-image', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                },\n                onUploadProgress: (progressEvent) =&gt; {\n                    const progress = Math.round(\n                        (progressEvent.loaded * 100) / progressEvent.total\n                    );\n                    console.log(`图像 ${i + 1} 上传进度: ${progress}%`);\n                }\n            });\n\n            console.log(`图像 ${i + 1} 上传成功:`, response.data);\n        }\n\n        alert('所有图像上传完成！');\n\n    } catch (error) {\n        console.error('上传失败:', error);\n        alert('上传失败: ' + error.message);\n    }\n}\n\n// Base64转Blob工具函数\nfunction base64ToBlob(base64Data, contentType = '') {\n    const byteCharacters = atob(base64Data);\n    const byteArrays = [];\n\n    for (let offset = 0; offset &lt; byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n\n        for (let i = 0; i &lt; slice.length; i++) {\n            byteNumbers[i] = slice.charCodeAt(i);\n        }\n\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n    }\n\n    return new Blob(byteArrays, { type: contentType });\n}\n\n// 获取图像数据\nscanonweb.getAllImage();</pre\n                    >\n                  </div>\n                </div>\n\n                <!-- 使用fetch API上传 -->\n                <div class=\"frontend-example\">\n                  <h5 class=\"server-example-title\">使用Fetch API上传</h5>\n                  <div class=\"code-block\">\n                    <pre>\n// 使用fetch API上传图像\nasync function uploadImageWithFetch(base64Data, businessId, description, index) {\n    try {\n        // 将Base64转换为二进制数据\n        const binaryData = atob(base64Data);\n        const bytes = new Uint8Array(binaryData.length);\n\n        for (let i = 0; i &lt; binaryData.length; i++) {\n            bytes[i] = binaryData.charCodeAt(i);\n        }\n\n        // 创建FormData\n        const formData = new FormData();\n        const blob = new Blob([bytes], { type: 'image/jpeg' });\n        formData.append('image', blob, `scan_${businessId}_${index}.jpg`);\n        formData.append('imageCount', '1');\n        formData.append('id', businessId);\n        formData.append('desc', description);\n\n        // 发送请求\n        const response = await fetch('/api/upload-image', {\n            method: 'POST',\n            body: formData\n        });\n\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n\n        if (result.network) {\n            console.log('上传成功:', result.msg);\n            return result;\n        } else {\n            throw new Error(result.msg);\n        }\n\n    } catch (error) {\n        console.error('上传失败:', error);\n        throw error;\n    }\n}\n\n// 批量上传示例\nasync function batchUploadImages() {\n    // 先获取图像数据\n    scanonweb.onGetAllImageEvent = async function(msg) {\n        const images = msg.images;\n        const businessId = 'BATCH_' + Date.now();\n\n        for (let i = 0; i &lt; images.length; i++) {\n            try {\n                await uploadImageWithFetch(\n                    images[i],\n                    businessId,\n                    `批量扫描文档 ${i + 1}`,\n                    i\n                );\n                console.log(`第 ${i + 1} 张图像上传完成`);\n            } catch (error) {\n                console.error(`第 ${i + 1} 张图像上传失败:`, error);\n            }\n        }\n    };\n\n    // 获取所有图像\n    scanonweb.getAllImage();\n}</pre\n                    >\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- saveAllImageToLocal -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">saveAllImageToLocal(filename)</h3>\n            <p class=\"api-description\">\n              将所有图像保存到客户端本地文件。支持多种格式：PDF、TIFF、JPG等。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>filename</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>保存的文件路径和名称</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 保存为PDF文件\nscanonweb.saveAllImageToLocal('D:/Documents/scan_result.pdf');\n\n// 保存为TIFF文件\nscanonweb.saveAllImageToLocal('D:/Documents/scan_result.tiff');\n\n// 保存为JPG文件（仅第一张图像）\nscanonweb.saveAllImageToLocal('D:/Documents/scan_result.jpg');</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- uploadJpgImageByIndex -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">\n              uploadJpgImageByIndex(url, id, desc, index)\n            </h3>\n            <p class=\"api-description\">\n              上传指定索引的单张图像（JPG格式）到服务器。\n            </p>\n\n            <div class=\"api-params\">\n              <h4 class=\"api-params-title\">参数</h4>\n              <div class=\"overflow-x-auto\">\n                <table class=\"api-params-table\">\n                  <thead>\n                    <tr>\n                      <th>参数名</th>\n                      <th>类型</th>\n                      <th>必填</th>\n                      <th>说明</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td><code>url</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>上传目标URL地址</td>\n                    </tr>\n                    <tr>\n                      <td><code>id</code></td>\n                      <td>String</td>\n                      <td>是</td>\n                      <td>图像标识ID</td>\n                    </tr>\n                    <tr>\n                      <td><code>desc</code></td>\n                      <td>String</td>\n                      <td>否</td>\n                      <td>图像描述信息</td>\n                    </tr>\n                    <tr>\n                      <td><code>index</code></td>\n                      <td>Number</td>\n                      <td>是</td>\n                      <td>图像索引，从0开始</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 上传第一张图像\nscanonweb.uploadJpgImageByIndex(\n    'https://api.example.com/upload-image',\n    'IMG_001',\n    '身份证正面',\n    0\n);</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- openClientLocalfile -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">openClientLocalfile()</h3>\n            <p class=\"api-description\">\n              打开客户端文件选择对话框，允许用户选择本地图像文件加载到控件中。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 打开文件选择对话框\nscanonweb.openClientLocalfile();\n\n// 用户选择文件后，会自动加载到控件中\n// 可以通过getAllImage()获取加载的图像</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 界面控制方法 -->\n        <div class=\"card-business p-8 mb-8\">\n          <h2 class=\"heading-secondary mb-6\">界面控制方法</h2>\n\n          <!-- setFocus -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">setFocus()</h3>\n            <p class=\"api-description\">\n              设置扫描控件界面获得焦点，将控件窗口置于前台。\n            </p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 将扫描控件窗口置于前台\nscanonweb.setFocus();</pre\n                >\n              </div>\n            </div>\n          </div>\n\n          <!-- hidden -->\n          <div class=\"api-method\">\n            <h3 class=\"api-method-name\">hidden()</h3>\n            <p class=\"api-description\">隐藏扫描控件界面窗口。</p>\n\n            <div class=\"api-example\">\n              <h4 class=\"api-example-title\">示例代码</h4>\n              <div class=\"code-block\">\n                <pre>\n// 隐藏扫描控件界面\nscanonweb.hidden();</pre\n                >\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </main>\n    <Footer />\n  </div>\n</template>\n\n<script setup>\nimport Header from \"~/components/Header.vue\";\nimport Footer from \"~/components/Footer.vue\";\n\n// SEO设置\nuseHead({\n  title: \"ScanOnWeb API文档 - 完整JavaScript API参考\",\n  meta: [\n    {\n      name: \"description\",\n      content:\n        \"ScanOnWeb扫描控件完整API文档，包含所有JavaScript方法、属性、事件回调的详细说明和代码示例。\",\n    },\n    {\n      name: \"keywords\",\n      content:\n        \"ScanOnWeb,API文档,JavaScript,扫描控件,方法参考,事件回调,参数说明\",\n    },\n  ],\n});\n</script>\n\n<style scoped>\n.api-method {\n  @apply mb-8;\n}\n\n.api-method-name {\n  @apply text-xl font-bold text-gray-900 mb-3 font-mono bg-gray-100 px-3 py-2 rounded;\n}\n\n.api-description {\n  @apply text-gray-700 mb-4;\n}\n\n.api-params-title,\n.api-example-title,\n.api-notes-title {\n  @apply text-lg font-semibold text-gray-800 mb-3;\n}\n\n.api-params-table {\n  @apply w-full border-collapse border border-gray-300;\n}\n\n.api-params-table th,\n.api-params-table td {\n  @apply border border-gray-300 px-4 py-2 text-left;\n}\n\n.api-params-table th {\n  @apply bg-gray-100 font-semibold;\n}\n\n.api-params-table code {\n  @apply bg-gray-200 px-2 py-1 rounded text-sm font-mono;\n}\n\n.code-block {\n  @apply bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto;\n}\n\n.code-block pre {\n  @apply font-mono text-sm m-0;\n}\n\n.api-notes-list {\n  @apply list-disc list-inside space-y-1 text-gray-700;\n}\n\n.api-server-examples {\n  @apply mt-8;\n}\n\n.server-example {\n  @apply mb-6;\n}\n\n.server-example-title {\n  @apply text-lg font-semibold text-gray-800 mb-3 bg-gray-100 px-3 py-2 rounded;\n}\n\n.frontend-examples {\n  @apply mt-8;\n}\n\n.frontend-example {\n  @apply mb-6;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAmrDQ,YAAA;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,SACE;AAAA,QACJ;AAAA,MACF;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}