<template>
  <div v-if="product" class="card-business overflow-hidden">
    <!-- 商业化产品头部 -->
    <div class="bg-business-gradient p-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-white mb-2">
            {{ product.name }}
          </h1>
          <p class="text-blue-100">企业级Web控件解决方案</p>
        </div>
        <div class="text-right text-white">
          <div class="text-sm text-blue-100">软件著作权</div>
          <div class="font-semibold">2013SR145420</div>
        </div>
      </div>
    </div>

    <div class="p-8">
      <div class="mb-8">
        <p class="text-business text-lg leading-relaxed">
          {{ product.description }}
        </p>
      </div>

      <!-- 突出的下载试用版按钮 -->
      <div class="mb-8 p-6 bg-orange-50 rounded-lg border border-orange-200">
        <div class="text-center">
          <h3 class="text-xl font-bold text-gray-900 mb-2">立即开始免费试用</h3>
          <p class="text-gray-600 mb-4">无需注册，支持多平台，专业技术支持</p>
          <NuxtLink
            to="/download"
            class="btn-primary text-lg px-8 py-4 inline-flex items-center"
          >
            <svg
              class="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            免费下载试用版
          </NuxtLink>
        </div>
      </div>

      <div class="mb-8">
        <h3 class="heading-secondary mb-6">核心功能特性</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div
            v-for="feature in product.features"
            :key="feature"
            class="flex items-start p-4 bg-gray-50 rounded-lg"
          >
            <div
              class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0"
            >
              <svg
                class="w-4 h-4 text-orange-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
            <span class="text-gray-700 font-medium">{{ feature }}</span>
          </div>
        </div>
      </div>

      <!-- 产品特性表格 -->
      <div v-if="product.tableData" class="mb-8">
        <h3 class="heading-secondary mb-6">技术规格详情</h3>
        <div class="overflow-x-auto">
          <table
            class="min-w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm"
          >
            <thead>
              <tr class="bg-gray-50">
                <th
                  class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"
                >
                  特性分类
                </th>
                <th
                  class="border-b border-gray-200 px-6 py-4 text-left font-semibold text-gray-900"
                >
                  技术参数
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in product.tableData"
                :key="index"
                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
              >
                <td
                  class="border-b border-gray-200 px-6 py-4 font-medium text-gray-900 w-1/4"
                >
                  {{ item.category }}
                </td>
                <td
                  class="border-b border-gray-200 px-6 py-4 text-gray-700 whitespace-pre-line"
                >
                  {{ item.value }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 技术规格 -->
        <div class="card-business p-6">
          <h3 class="heading-tertiary mb-4">基础技术规格</h3>
          <div class="space-y-3">
            <div
              v-for="(value, key) in product.specs"
              :key="key"
              class="flex justify-between py-2 border-b border-gray-100 last:border-b-0"
            >
              <span class="font-medium text-gray-700">{{ key }}</span>
              <span class="text-gray-600 text-right">{{ value }}</span>
            </div>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="card-business p-6">
          <h3 class="heading-tertiary mb-4">典型应用场景</h3>
          <div class="grid grid-cols-2 gap-3">
            <div
              v-for="useCase in product.useCases"
              :key="useCase"
              class="flex items-center p-3 bg-orange-50 rounded-lg"
            >
              <svg
                class="w-5 h-5 text-orange-500 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                ></path>
              </svg>
              <span class="text-gray-700 font-medium">{{ useCase }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 定价信息 -->
      <div class="feature-highlight mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="heading-tertiary text-orange-700 mb-2">
              企业级定价方案
            </h3>
            <p class="text-orange-600">{{ product.pricing }}</p>
          </div>
          <div class="text-right">
            <div class="text-sm text-orange-600">联系销售获取</div>
            <div class="font-bold text-orange-700">专属优惠价格</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-4">
        <a
          :href="product.videoUrl"
          target="_blank"
          class="btn-secondary flex-1 text-center inline-flex items-center justify-center"
        >
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          观看产品演示
        </a>
        <a
          :href="product.pdfUrl"
          target="_blank"
          class="btn-secondary flex-1 text-center inline-flex items-center justify-center"
        >
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
          下载产品资料
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  product: Object,
});
</script>
