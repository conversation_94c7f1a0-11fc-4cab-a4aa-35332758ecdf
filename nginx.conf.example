# Nginx配置文件示例 - 用于静态部署
# 将相关配置添加到您的Nginx服务器配置中

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /path/to/your/webserver;
    index index.html;

    # 可选：强制HTTPS重定向
    # return 301 https://$server_name$request_uri;
}

# HTTPS配置（如果您有SSL证书）
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     root /path/to/your/webserver;
#     index index.html;
# 
#     # SSL证书配置
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
# 
#     # 处理静态文件和SPA路由
#     location / {
#         try_files $uri $uri/ /index.html;
#     }
# 
#     # 静态资源缓存
#     location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#         add_header Vary Accept-Encoding;
#     }
# 
#     # HTML文件缓存
#     location ~* \.html$ {
#         expires 1h;
#         add_header Cache-Control "public";
#     }
# 
#     # 安全头设置
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin" always;
# 
#     # Gzip压缩
#     gzip on;
#     gzip_vary on;
#     gzip_min_length 1024;
#     gzip_proxied any;
#     gzip_comp_level 6;
#     gzip_types
#         text/plain
#         text/css
#         text/xml
#         text/javascript
#         application/javascript
#         application/xml+rss
#         application/json
#         image/svg+xml;
# 
#     # 禁止访问敏感文件
#     location ~ /\. {
#         deny all;
#         access_log off;
#         log_not_found off;
#     }
# 
#     # 禁止访问备份文件
#     location ~* \.(bak|backup|old|tmp)$ {
#         deny all;
#         access_log off;
#         log_not_found off;
#     }
# }

# 简化版配置（仅HTTP）
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/webserver;
    index index.html;

    # 处理静态文件和SPA路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # HTML文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public";
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
