@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 商业化设计系统 */
:root {
  --primary-blue: #1e3a8a;
  --secondary-blue: #3b82f6;
  --accent-orange: #f97316;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --bg-light: #f8fafc;
  --border-light: #e5e7eb;
  --success-green: #059669;
}

/* 商业化按钮样式 */
.btn-primary {
  @apply bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-md font-semibold transition-colors duration-200 shadow-sm;
}

.btn-secondary {
  @apply bg-white border-2 border-gray-300 hover:border-orange-500 text-gray-700 hover:text-orange-500 px-6 py-3 rounded-md font-semibold transition-all duration-200;
}

.btn-outline {
  @apply border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white px-6 py-3 rounded-md font-semibold transition-all duration-200;
}

/* 商业化卡片样式 */
.card-business {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
}

/* 商业化标题样式 */
.heading-primary {
  @apply text-3xl md:text-4xl font-bold text-gray-900 leading-tight;
}

.heading-secondary {
  @apply text-2xl md:text-3xl font-semibold text-gray-800 leading-tight;
}

.heading-tertiary {
  @apply text-xl font-semibold text-gray-800;
}

/* 商业化文本样式 */
.text-business {
  @apply text-gray-600 leading-relaxed;
}

/* 商业化分割线 */
.divider {
  @apply border-t border-gray-200 my-8;
}

/* 商业化渐变背景（更加克制） */
.bg-business-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}

/* 商业化特色区域 */
.feature-highlight {
  @apply bg-orange-50 border-l-4 border-orange-500 p-4 rounded-r-md;
}