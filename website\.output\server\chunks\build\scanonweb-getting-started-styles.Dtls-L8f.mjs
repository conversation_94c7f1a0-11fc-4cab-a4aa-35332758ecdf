import { s as scanonwebGettingStarted_vue_vue_type_style_index_0_scoped_ec61ad0e_lang } from './scanonweb-getting-started-styles-1.mjs-CR5Mgiae.mjs';

const scanonwebGettingStartedStyles_DtlsL8f = [scanonwebGettingStarted_vue_vue_type_style_index_0_scoped_ec61ad0e_lang, scanonwebGettingStarted_vue_vue_type_style_index_0_scoped_ec61ad0e_lang];

export { scanonwebGettingStartedStyles_DtlsL8f as default };
//# sourceMappingURL=scanonweb-getting-started-styles.Dtls-L8f.mjs.map
