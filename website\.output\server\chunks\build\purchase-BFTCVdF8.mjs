import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { _ as _sfc_main$1, a as _sfc_main$2 } from './Footer-C3PwX65Z.mjs';
import { _ as _export_sfc } from './server.mjs';
import './nuxt-link-2X8I7ISh.mjs';
import '../runtime.mjs';
import 'node:http';
import 'node:https';
import 'fs';
import 'path';
import 'node:fs';
import 'node:url';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'devalue';
import '@unhead/ssr';
import 'unhead';
import '@unhead/shared';
import 'vue-router';

const _sfc_main = {
  __name: "purchase",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-gray-100 min-h-screen" }, _attrs))} data-v-873a08a0>`);
      _push(ssrRenderComponent(_sfc_main$1, null, null, _parent));
      _push(`<main class="container mx-auto py-12 px-4" data-v-873a08a0><section class="mb-12 text-center" data-v-873a08a0><h1 class="text-4xl font-bold mb-4 text-blue-700" data-v-873a08a0>\u4EA7\u54C1\u6CE8\u518C\u4E0E\u8D2D\u4E70</h1><p class="text-lg text-gray-600 max-w-3xl mx-auto" data-v-873a08a0> \u9009\u62E9\u9002\u5408\u60A8\u9700\u6C42\u7684\u4EA7\u54C1\uFF0C\u70B9\u51FB\u4E0B\u65B9\u8D2D\u4E70\u6309\u94AE\u5373\u53EF\u524D\u5F80\u7B2C\u4E09\u65B9\u7ECF\u9500\u5546\u7F51\u7AD9\u5B8C\u6210\u8D2D\u4E70\u6D41\u7A0B\u3002\u8D2D\u4E70\u540E\u6211\u4EEC\u5C06\u63D0\u4F9B\u4E13\u4E1A\u7684\u6280\u672F\u652F\u6301\u4E0E\u670D\u52A1\u3002 </p></section><div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16" data-v-873a08a0><div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition-transform hover:scale-105" data-v-873a08a0><div class="h-20 bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center" data-v-873a08a0><h2 class="text-2xl font-bold text-white" data-v-873a08a0>ScanOnWeb</h2></div><div class="p-6" data-v-873a08a0><div class="mb-4 flex justify-center" data-v-873a08a0><div class="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center" data-v-873a08a0><svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-873a08a0></path></svg></div></div><p class="text-gray-600 mb-6" data-v-873a08a0> \u7528\u4E8E\u5904\u7406\u56FE\u50CF\u626B\u63CF\u7F16\u7A0B\u7684\u63A7\u4EF6\uFF0C\u9002\u5408\u7528\u4E8Eweb\u73AF\u5883\u4E0B\u7684\u626B\u63CF\u4EEA\u7F16\u7A0B\u5E94\u7528\uFF0C\u53EF\u65E0\u7F1D\u96C6\u6210\u5230jsp\u3001php\u3001asp.net\u7B49\u7F16\u7A0B\u6280\u672F\u4E2D\u3002 </p><div class="mb-6" data-v-873a08a0><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u6807\u51C6\u7248</span><span class="font-bold text-blue-700" data-v-873a08a0>\xA51,999</span></div><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4E13\u4E1A\u7248</span><span class="font-bold text-blue-700" data-v-873a08a0>\xA52,999</span></div><div class="flex justify-between" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4F01\u4E1A\u7248</span><span class="font-bold text-blue-700" data-v-873a08a0>\xA55,999</span></div></div><div class="flex justify-center" data-v-873a08a0><a href="https://www.taobao.com" target="_blank" class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition duration-300 flex items-center justify-center" data-v-873a08a0><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-873a08a0></path></svg> \u7ACB\u5373\u8D2D\u4E70 </a></div></div></div><div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition-transform hover:scale-105" data-v-873a08a0><div class="h-20 bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center" data-v-873a08a0><h2 class="text-2xl font-bold text-white" data-v-873a08a0>ImageCapOnWeb</h2></div><div class="p-6" data-v-873a08a0><div class="mb-4 flex justify-center" data-v-873a08a0><div class="w-20 h-20 rounded-full bg-purple-100 flex items-center justify-center" data-v-873a08a0><svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" data-v-873a08a0></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" data-v-873a08a0></path></svg></div></div><p class="text-gray-600 mb-6" data-v-873a08a0> \u7528\u4E8E\u5904\u7406\u6444\u50CF\u5934\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u7684\u63A7\u4EF6\uFF0C\u9002\u5408web\u73AF\u5883\u4E0B\u7684\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u5E94\u7528\uFF0C\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u5927\u90E8\u5206\u6444\u50CF\u5934\u6570\u7801\u8BBE\u5907\u3002 </p><div class="mb-6" data-v-873a08a0><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u6807\u51C6\u7248</span><span class="font-bold text-purple-700" data-v-873a08a0>\xA51,599</span></div><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4E13\u4E1A\u7248</span><span class="font-bold text-purple-700" data-v-873a08a0>\xA52,599</span></div><div class="flex justify-between" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4F01\u4E1A\u7248</span><span class="font-bold text-purple-700" data-v-873a08a0>\xA54,999</span></div></div><div class="flex justify-center" data-v-873a08a0><a href="https://www.jd.com" target="_blank" class="w-full bg-purple-600 text-white py-3 rounded-lg font-medium text-center hover:bg-purple-700 transition duration-300 flex items-center justify-center" data-v-873a08a0><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-873a08a0></path></svg> \u7ACB\u5373\u8D2D\u4E70 </a></div></div></div><div class="bg-white rounded-xl shadow-xl overflow-hidden transform transition-transform hover:scale-105" data-v-873a08a0><div class="h-20 bg-gradient-to-r from-green-500 to-teal-600 flex items-center justify-center" data-v-873a08a0><h2 class="text-2xl font-bold text-white" data-v-873a08a0>GaoPaiYi</h2></div><div class="p-6" data-v-873a08a0><div class="mb-4 flex justify-center" data-v-873a08a0><div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center" data-v-873a08a0><svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" data-v-873a08a0></path></svg></div></div><p class="text-gray-600 mb-6" data-v-873a08a0> \u7528\u4E8E\u5904\u7406\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u7684\u63A7\u4EF6\uFF0C\u9002\u5408\u7528\u4E8Eweb\u73AF\u5883\u4E0B\u7684\u9AD8\u62CD\u4EEA\u56FE\u50CF\u91C7\u96C6\u7F16\u7A0B\u5E94\u7528\uFF0C\u517C\u5BB9Windows\u5E73\u53F0\u4E0B\u7684\u9AD8\u62CD\u4EEA\u4EA7\u54C1\u3002 </p><div class="mb-6" data-v-873a08a0><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u6807\u51C6\u7248</span><span class="font-bold text-green-700" data-v-873a08a0>\xA51,799</span></div><div class="flex justify-between mb-2" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4E13\u4E1A\u7248</span><span class="font-bold text-green-700" data-v-873a08a0>\xA52,799</span></div><div class="flex justify-between" data-v-873a08a0><span class="font-medium" data-v-873a08a0>\u4F01\u4E1A\u7248</span><span class="font-bold text-green-700" data-v-873a08a0>\xA55,499</span></div></div><div class="flex justify-center" data-v-873a08a0><a href="https://www.pinduoduo.com" target="_blank" class="w-full bg-green-600 text-white py-3 rounded-lg font-medium text-center hover:bg-green-700 transition duration-300 flex items-center justify-center" data-v-873a08a0><svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-873a08a0></path></svg> \u7ACB\u5373\u8D2D\u4E70 </a></div></div></div></div><div class="bg-white rounded-xl shadow-lg p-8 mb-12" data-v-873a08a0><h2 class="text-2xl font-bold mb-6 text-center text-gray-800" data-v-873a08a0> \u8D2D\u4E70\u987B\u77E5 </h2><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-v-873a08a0><div class="text-center" data-v-873a08a0><div class="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4" data-v-873a08a0><svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-873a08a0></path></svg></div><h3 class="font-semibold mb-2" data-v-873a08a0>\u6B63\u7248\u6388\u6743</h3><p class="text-gray-600" data-v-873a08a0> \u6240\u6709\u4EA7\u54C1\u5747\u4E3A\u5B98\u65B9\u6388\u6743\u6B63\u7248\u8F6F\u4EF6\uFF0C\u63D0\u4F9B\u6B63\u89C4\u53D1\u7968\u548C\u8BB8\u53EF\u8BC1\u4E66 </p></div><div class="text-center" data-v-873a08a0><div class="w-16 h-16 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-4" data-v-873a08a0><svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-873a08a0></path></svg></div><h3 class="font-semibold mb-2" data-v-873a08a0>\u7EC8\u8EAB\u6280\u672F\u652F\u6301</h3><p class="text-gray-600" data-v-873a08a0> \u8D2D\u4E70\u540E\u4EAB\u53D7\u7EC8\u8EAB\u6280\u672F\u652F\u6301\uFF0C\u5305\u62EC\u95EE\u9898\u54A8\u8BE2\u548C\u6545\u969C\u6392\u9664 </p></div><div class="text-center" data-v-873a08a0><div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4" data-v-873a08a0><svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-873a08a0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" data-v-873a08a0></path></svg></div><h3 class="font-semibold mb-2" data-v-873a08a0>\u514D\u8D39\u5347\u7EA7</h3><p class="text-gray-600" data-v-873a08a0> \u4E00\u5E74\u5185\u4EAB\u53D7\u514D\u8D39\u7248\u672C\u5347\u7EA7\u670D\u52A1\uFF0C\u786E\u4FDD\u60A8\u4F7F\u7528\u6700\u65B0\u529F\u80FD </p></div></div></div><div class="bg-white rounded-xl shadow-lg p-8" data-v-873a08a0><h2 class="text-2xl font-bold mb-6 text-center text-gray-800" data-v-873a08a0> \u5E38\u89C1\u8D2D\u4E70\u95EE\u9898 </h2><div class="space-y-4" data-v-873a08a0><div class="border-b border-gray-200 pb-4" data-v-873a08a0><h3 class="font-medium text-lg mb-2" data-v-873a08a0>\u5982\u4F55\u9009\u62E9\u9002\u5408\u6211\u7684\u7248\u672C\uFF1F</h3><p class="text-gray-600" data-v-873a08a0> \u6807\u51C6\u7248\u9002\u5408\u5C0F\u578B\u9879\u76EE\u548C\u4E2A\u4EBA\u5F00\u53D1\u8005\uFF1B\u4E13\u4E1A\u7248\u9002\u5408\u4E2D\u578B\u4F01\u4E1A\u548C\u5546\u4E1A\u5E94\u7528\uFF1B\u4F01\u4E1A\u7248\u9002\u5408\u5927\u578B\u4F01\u4E1A\u548C\u9700\u8981\u5B9A\u5236\u5316\u670D\u52A1\u7684\u5BA2\u6237\u3002 </p></div><div class="border-b border-gray-200 pb-4" data-v-873a08a0><h3 class="font-medium text-lg mb-2" data-v-873a08a0>\u8D2D\u4E70\u540E\u5982\u4F55\u83B7\u53D6\u8F6F\u4EF6\uFF1F</h3><p class="text-gray-600" data-v-873a08a0> \u5B8C\u6210\u4ED8\u6B3E\u540E\uFF0C\u7CFB\u7EDF\u5C06\u81EA\u52A8\u53D1\u9001\u4E0B\u8F7D\u94FE\u63A5\u548C\u6388\u6743\u7801\u5230\u60A8\u7684\u90AE\u7BB1\u3002\u60A8\u4E5F\u53EF\u4EE5\u901A\u8FC7\u4F1A\u5458\u4E2D\u5FC3\u4E0B\u8F7D\u6700\u65B0\u7248\u672C\u3002 </p></div><div class="border-b border-gray-200 pb-4" data-v-873a08a0><h3 class="font-medium text-lg mb-2" data-v-873a08a0>\u662F\u5426\u63D0\u4F9B\u53D1\u7968\uFF1F</h3><p class="text-gray-600" data-v-873a08a0> \u662F\u7684\uFF0C\u6211\u4EEC\u63D0\u4F9B\u6B63\u89C4\u589E\u503C\u7A0E\u53D1\u7968\u3002\u8D2D\u4E70\u65F6\u8BF7\u7559\u4E0B\u5F00\u7968\u4FE1\u606F\uFF0C\u53D1\u7968\u5C06\u5728\u4ED8\u6B3E\u540E5\u4E2A\u5DE5\u4F5C\u65E5\u5185\u5BC4\u51FA\u6216\u53D1\u9001\u7535\u5B50\u7248\u3002 </p></div><div data-v-873a08a0><h3 class="font-medium text-lg mb-2" data-v-873a08a0>\u5982\u4F55\u83B7\u53D6\u6280\u672F\u652F\u6301\uFF1F</h3><p class="text-gray-600" data-v-873a08a0> \u8D2D\u4E70\u540E\u60A8\u5C06\u83B7\u5F97\u4E13\u5C5E\u7684\u6280\u672F\u652F\u6301\u8D26\u53F7\uFF0C\u53EF\u901A\u8FC7\u7535\u8BDD\u3001\u90AE\u4EF6\u6216\u5728\u7EBF\u5BA2\u670D\u83B7\u53D6\u6280\u672F\u652F\u6301\u670D\u52A1\u3002 </p></div></div></div></main>`);
      _push(ssrRenderComponent(_sfc_main$2, null, null, _parent));
      _push(`</div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/purchase.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const purchase = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-873a08a0"]]);

export { purchase as default };
//# sourceMappingURL=purchase-BFTCVdF8.mjs.map
