// 百度统计插件
export default defineNuxtPlugin(() => {
  // 只在客户端执行
  if (process.client) {
    // 初始化百度统计
    const initBaiduAnalytics = () => {
      // 创建 _hmt 全局变量
      (window as any)._hmt = (window as any)._hmt || [];
      
      // 创建并插入百度统计脚本
      const hm = document.createElement('script');
      hm.src = 'https://hm.baidu.com/hm.js?acc9c394175b8b87b1ee93f9482c2bce';
      hm.async = true;
      
      const s = document.getElementsByTagName('script')[0];
      if (s && s.parentNode) {
        s.parentNode.insertBefore(hm, s);
      }
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initBaiduAnalytics);
    } else {
      initBaiduAnalytics();
    }

    // 路由变化时发送页面浏览事件
    const router = useRouter();
    router.afterEach((to) => {
      // 确保百度统计已加载
      if ((window as any)._hmt) {
        // 发送页面浏览事件
        (window as any)._hmt.push(['_trackPageview', to.fullPath]);
      }
    });
  }
});
