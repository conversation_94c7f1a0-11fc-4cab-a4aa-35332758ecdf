<template>
  <div class="bg-gray-50 min-h-screen">
    <Header />
    <main>
      <!-- 面包屑导航 -->
      <div class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4 py-4">
          <nav class="flex items-center space-x-2 text-sm text-gray-500">
            <NuxtLink to="/" class="hover:text-orange-500">首页</NuxtLink>
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
            <span class="text-gray-900 font-medium">文档资料</span>
          </nav>
        </div>
      </div>

      <!-- 页面标题区域 -->
      <div class="bg-white py-12">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl">
            <h1 class="heading-primary mb-4">技术文档中心</h1>
            <p class="text-xl text-gray-600 mb-6">
              完整的产品文档、API参考、示例代码和视频教程，助您快速上手和深入使用我们的产品
            </p>
            <div class="flex flex-wrap gap-6 text-sm text-gray-500">
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>完整API文档</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>实用代码示例</span>
              </div>
              <div class="flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>视频教程指导</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品选择器 -->
      <div class="bg-orange-50 py-8">
        <div class="container mx-auto px-4">
          <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="mb-4 md:mb-0">
              <h2 class="text-lg font-semibold text-gray-900 mb-2">
                选择产品查看文档
              </h2>
              <p class="text-gray-600">每个产品都有完整的技术文档和使用指南</p>
            </div>
            <div class="flex flex-wrap gap-3">
              <button
                v-for="product in products"
                :key="product.id"
                @click="selectProduct(product.id)"
                :class="[
                  'px-6 py-3 rounded-lg font-semibold transition-all duration-200',
                  selectedProductId === product.id
                    ? 'bg-orange-500 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-orange-100 border border-gray-200',
                ]"
              >
                {{ product.name }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 文档分类导航 -->
          <div class="lg:col-span-1">
            <div class="card-business p-6 sticky top-8">
              <h3 class="heading-tertiary mb-4">文档分类</h3>
              <nav class="space-y-2">
                <button
                  v-for="category in documentCategories"
                  :key="category.id"
                  @click="selectCategory(category.id)"
                  :class="[
                    'w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center',
                    selectedCategoryId === category.id
                      ? 'bg-orange-100 text-orange-700 border-l-4 border-orange-500'
                      : 'text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <component :is="category.icon" class="w-5 h-5 mr-3" />
                  <span class="font-medium">{{ category.name }}</span>
                  <span class="ml-auto text-sm text-gray-500"
                    >({{ getDocumentCount(category.id) }})</span
                  >
                </button>
              </nav>
            </div>
          </div>

          <!-- 文档列表 -->
          <div class="lg:col-span-3">
            <div class="mb-6">
              <h2 class="heading-secondary mb-2">
                {{ getCurrentCategoryName() }}
              </h2>
              <p class="text-gray-600">{{ getCurrentCategoryDescription() }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div
                v-for="document in filteredDocuments"
                :key="document.id"
                class="card-business p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                @click="selectDocument(document)"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0"
                    :class="getDocumentTypeClass(document.type)"
                  >
                    <component
                      :is="getDocumentIcon(document.type)"
                      class="w-6 h-6 text-white"
                    />
                  </div>
                  <div class="flex-1">
                    <h3 class="font-bold text-gray-900 mb-2">
                      {{ document.title }}
                    </h3>
                    <p class="text-gray-600 text-sm mb-3">
                      {{ document.description }}
                    </p>
                    <div
                      class="flex items-center justify-between text-xs text-gray-500"
                    >
                      <span>{{ document.version }}</span>
                      <span>{{ document.date }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="filteredDocuments.length === 0"
              class="text-center py-12"
            >
              <svg
                class="w-16 h-16 text-gray-300 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                暂无相关文档
              </h3>
              <p class="text-gray-500">该产品的此类文档正在准备中，敬请期待</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, h } from "vue";
import Header from "~/components/Header.vue";
import Footer from "~/components/Footer.vue";

// 产品列表
const products = ref([
  {
    id: 1,
    name: "ScanOnWeb",
    description: "专业扫描仪控件解决方案",
  },
  {
    id: 2,
    name: "ImageCapOnWeb",
    description: "摄像头图像采集控件",
  },
  {
    id: 3,
    name: "GaoPaiYi",
    description: "高拍仪图像采集控件",
  },
]);

// 当前选中的产品和分类
const selectedProductId = ref(1);
const selectedCategoryId = ref("getting-started");

// 文档分类定义
const documentCategories = ref([
  {
    id: "getting-started",
    name: "入门指南",
    description: "快速开始使用产品的基础教程和安装指南",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",
        }),
      ]),
  },
  {
    id: "api-docs",
    name: "API文档",
    description: "完整的API参考文档和接口说明",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4",
        }),
      ]),
  },
  {
    id: "video-tutorials",
    name: "视频教程",
    description: "直观的视频教学内容，快速掌握使用技巧",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z",
        }),
      ]),
  },
  {
    id: "code-examples",
    name: "示例代码",
    description: "实用的代码示例和最佳实践",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
        }),
      ]),
  },
  {
    id: "faq",
    name: "常见问题",
    description: "常见问题解答和故障排除指南",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
        }),
      ]),
  },
  {
    id: "features",
    name: "技术特性",
    description: "详细的技术规格表格和平台兼容性信息",
    icon: () =>
      h("svg", { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, [
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
        }),
        h("path", {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2",
          d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z",
        }),
      ]),
  },
]);

// 所有文档数据（按产品和分类组织）
const allDocuments = ref([
  // ScanOnWeb 文档
  {
    id: 1,
    title: "ScanOnWeb 入门指南",
    description: "详细的集成教程，包含HTML和Vue3项目的完整示例代码",
    type: "guide",
    category: "getting-started",
    productId: 1,
    date: "2024-03-15",
    version: "v3.5.0",
    url: "/docs/scanonweb-getting-started",
  },
  {
    id: 16,
    title: "ScanOnWeb 快速入门指南",
    description: "5分钟快速上手ScanOnWeb控件，包含安装、配置和基本使用",
    type: "pdf",
    category: "getting-started",
    productId: 1,
    date: "2024-02-15",
    version: "v3.5.0",
    downloadUrl: "/docs/ScanOnWeb-QuickStart.pdf",
  },
  {
    id: 2,
    title: "ScanOnWeb API 参考手册",
    description: "完整的API接口文档，包含所有方法、事件和参数说明",
    type: "code",
    category: "api-docs",
    productId: 1,
    date: "2024-02-20",
    version: "v3.5.0",
    downloadUrl: "/docs/scanonweb-api",
  },
  {
    id: 3,
    title: "ScanOnWeb 视频教程",
    description: "通过视频演示学习ScanOnWeb的安装配置和常用功能",
    type: "video",
    category: "video-tutorials",
    productId: 1,
    date: "2024-02-25",
    version: "v3.5.0",
    downloadUrl: "/docs/ScanOnWeb-Video.mp4",
  },
  {
    id: 4,
    title: "Vue3 + Spring Boot 集成示例",
    description: "Vue3前端 + Spring Boot后端的完整ScanOnWeb集成项目",
    type: "code",
    category: "code-examples",
    productId: 1,
    date: "2024-03-01",
    version: "v3.5.0",
    downloadUrl: "/downloads/examples/scanonweb-vue3-springboot.zip",
  },
  {
    id: 41,
    title: "Vue3 + ASP.NET Core 集成示例",
    description: "Vue3前端 + ASP.NET Core后端的完整ScanOnWeb集成项目",
    type: "code",
    category: "code-examples",
    productId: 1,
    date: "2024-03-05",
    version: "v3.5.0",
    downloadUrl: "/downloads/examples/scanonweb-vue3-aspnetcore.zip",
  },
  {
    id: 42,
    title: "HTML + JS + Spring Boot 集成示例",
    description: "原生HTML/JavaScript前端 + Spring Boot后端的ScanOnWeb集成项目",
    type: "code",
    category: "code-examples",
    productId: 1,
    date: "2024-03-08",
    version: "v3.5.0",
    downloadUrl: "/downloads/examples/scanonweb-html-springboot.zip",
  },
  {
    id: 43,
    title: "React + Spring Boot 集成示例",
    description: "React前端 + Spring Boot后端的完整ScanOnWeb集成项目",
    type: "code",
    category: "code-examples",
    productId: 1,
    date: "2024-03-10",
    version: "v3.5.0",
    downloadUrl: "/downloads/examples/scanonweb-react-springboot.zip",
  },
  {
    id: 44,
    title: "Vue3 + Go 集成示例",
    description: "Vue3前端 + Go后端的完整ScanOnWeb集成项目",
    type: "code",
    category: "code-examples",
    productId: 1,
    date: "2024-03-12",
    version: "v3.5.0",
    downloadUrl: "/downloads/examples/scanonweb-vue3-go.zip",
  },
  {
    id: 5,
    title: "ScanOnWeb 常见问题解答",
    description: "使用过程中的常见问题和解决方案",
    type: "text",
    category: "faq",
    productId: 1,
    date: "2024-03-05",
    version: "v3.5.0",
    downloadUrl: "/docs/scanonweb-faq",
  },
  {
    id: 6,
    title: "ScanOnWeb 技术特性表格",
    description: "全面的技术规格、平台支持和功能特性对比表格",
    type: "table",
    category: "features",
    productId: 1,
    date: "2024-03-15",
    version: "v3.5.0",
    downloadUrl: "/docs/scanonweb-features",
  },

  // ImageCapOnWeb 文档
  {
    id: 7,
    title: "ImageCapOnWeb 快速入门指南",
    description: "摄像头控件的安装配置和基本使用教程",
    type: "pdf",
    category: "getting-started",
    productId: 2,
    date: "2024-01-20",
    version: "v2.8.0",
    downloadUrl: "/docs/ImageCapOnWeb-QuickStart.pdf",
  },
  {
    id: 7,
    title: "ImageCapOnWeb API 文档",
    description: "摄像头控件的完整API接口说明",
    type: "code",
    category: "api-docs",
    productId: 2,
    date: "2024-01-25",
    version: "v2.8.0",
    downloadUrl: "/docs/ImageCapOnWeb-API.pdf",
  },
  {
    id: 8,
    title: "ImageCapOnWeb 视频教程",
    description: "通过视频学习摄像头控件的使用方法",
    type: "video",
    category: "video-tutorials",
    productId: 2,
    date: "2024-02-05",
    version: "v2.8.0",
    downloadUrl: "/docs/ImageCapOnWeb-Video.mp4",
  },
  {
    id: 9,
    title: "ImageCapOnWeb React 集成示例",
    description: "在React项目中集成ImageCapOnWeb的代码示例",
    type: "code",
    category: "code-examples",
    productId: 2,
    date: "2024-02-10",
    version: "v2.8.0",
    downloadUrl: "/docs/ImageCapOnWeb-React-Example.zip",
  },
  {
    id: 10,
    title: "ImageCapOnWeb 常见问题",
    description: "摄像头控件使用中的常见问题和解决方案",
    type: "text",
    category: "faq",
    productId: 2,
    date: "2024-02-15",
    version: "v2.8.0",
    downloadUrl: "/docs/ImageCapOnWeb-FAQ.pdf",
  },

  // GaoPaiYi 文档
  {
    id: 11,
    title: "GaoPaiYi 快速入门指南",
    description: "高拍仪控件的安装和基本使用方法",
    type: "pdf",
    category: "getting-started",
    productId: 3,
    date: "2024-03-05",
    version: "v2.0.1",
    downloadUrl: "/docs/GaoPaiYi-QuickStart.pdf",
  },
  {
    id: 12,
    title: "GaoPaiYi API 参考手册",
    description: "高拍仪控件的API接口文档",
    type: "code",
    category: "api-docs",
    productId: 3,
    date: "2024-03-10",
    version: "v2.0.1",
    downloadUrl: "/docs/GaoPaiYi-API.pdf",
  },
  {
    id: 13,
    title: "GaoPaiYi 视频教程",
    description: "通过视频学习高拍仪控件的使用",
    type: "video",
    category: "video-tutorials",
    productId: 3,
    date: "2024-03-20",
    version: "v2.0.1",
    downloadUrl: "/docs/GaoPaiYi-Video.mp4",
  },
  {
    id: 14,
    title: "GaoPaiYi Angular 集成示例",
    description: "在Angular项目中集成GaoPaiYi的代码示例",
    type: "code",
    category: "code-examples",
    productId: 3,
    date: "2024-03-25",
    version: "v2.0.1",
    downloadUrl: "/docs/GaoPaiYi-Angular-Example.zip",
  },
  {
    id: 15,
    title: "GaoPaiYi 常见问题",
    description: "高拍仪控件使用中的问题解答",
    type: "text",
    category: "faq",
    productId: 3,
    date: "2024-03-30",
    version: "v2.0.1",
    downloadUrl: "/docs/GaoPaiYi-FAQ.pdf",
  },
]);

// 计算属性和方法
const filteredDocuments = computed(() => {
  return allDocuments.value.filter(
    (doc) =>
      doc.productId === selectedProductId.value &&
      doc.category === selectedCategoryId.value
  );
});

const selectProduct = (productId) => {
  selectedProductId.value = productId;
};

const selectCategory = (categoryId) => {
  selectedCategoryId.value = categoryId;
};

const selectDocument = (document) => {
  // 如果是在线指南，使用路由导航
  if (document.url) {
    navigateTo(document.url);
  } else if (document.downloadUrl) {
    // 如果是下载文档，在新窗口打开
    window.open(document.downloadUrl, "_blank");
  }
};

const getCurrentCategoryName = () => {
  const category = documentCategories.value.find(
    (cat) => cat.id === selectedCategoryId.value
  );
  return category ? category.name : "";
};

const getCurrentCategoryDescription = () => {
  const category = documentCategories.value.find(
    (cat) => cat.id === selectedCategoryId.value
  );
  return category ? category.description : "";
};

const getDocumentCount = (categoryId) => {
  return allDocuments.value.filter(
    (doc) =>
      doc.productId === selectedProductId.value && doc.category === categoryId
  ).length;
};

const getDocumentTypeClass = (type) => {
  switch (type) {
    case "pdf":
      return "bg-red-500";
    case "video":
      return "bg-blue-500";
    case "code":
      return "bg-green-500";
    case "text":
      return "bg-gray-500";
    case "guide":
      return "bg-orange-500";
    case "table":
      return "bg-purple-500";
    default:
      return "bg-gray-400";
  }
};

const getDocumentIcon = (type) => {
  switch (type) {
    case "pdf":
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z",
            }),
          ]
        );
    case "video":
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z",
            }),
          ]
        );
    case "code":
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4",
            }),
          ]
        );
    case "guide":
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",
            }),
          ]
        );
    case "table":
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1V10z",
            }),
          ]
        );
    default:
      return () =>
        h(
          "svg",
          { fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" },
          [
            h("path", {
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
              "stroke-width": "2",
              d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
            }),
          ]
        );
  }
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
