#!/bin/bash

# Web控件产品展示网站 - 静态文件部署脚本
# 使用方法：chmod +x deploy.sh && ./deploy.sh

set -e  # 遇到错误时退出

echo "========================================="
echo "开始构建和部署Web控件产品展示网站"
echo "========================================="

# 检查是否在正确的目录
if [ ! -d "website" ]; then
    echo "错误：未找到website目录，请确保在项目根目录执行此脚本"
    exit 1
fi

# 进入website目录
cd website

echo "1. 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "   安装依赖中..."
    npm install
else
    echo "   依赖已存在"
fi

echo "2. 清理旧的构建文件..."
if [ -d ".output" ]; then
    rm -rf .output
    echo "   已清理旧的构建文件"
fi

echo "3. 生成静态文件..."
npm run generate

# 检查构建是否成功
if [ ! -d ".output/public" ]; then
    echo "错误：静态文件生成失败"
    exit 1
fi

echo "4. 静态文件生成完成！"
echo "   输出目录：website/.output/public/"
echo "   文件列表："
ls -la .output/public/

echo ""
echo "========================================="
echo "部署说明："
echo "========================================="
echo "1. 将 website/.output/public/ 目录中的所有文件"
echo "   上传到您的Web服务器根目录"
echo ""
echo "2. 如果使用Apache服务器，确保配置.htaccess文件"
echo "3. 如果使用Nginx服务器，确保配置正确的重写规则"
echo ""
echo "4. 常用部署命令示例："
echo "   # 复制到本地服务器目录"
echo "   cp -r .output/public/* /var/www/html/"
echo ""
echo "   # 同步到远程服务器"
echo "   rsync -avz .output/public/ user@server:/path/to/webserver/"
echo ""
echo "构建完成！"
